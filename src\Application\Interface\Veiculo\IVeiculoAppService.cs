using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Veiculo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Veiculo;
using SistemaInfo.BBC.Application.Objects.Web.Veiculo;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Veiculo.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Veiculo
{
    public interface IVeiculoAppService : IAppService<Domain.Models.Veiculo.Veiculo, IVeiculoReadRepository, IVeiculoWriteRepository>
    {
        ConsultarGridVeiculoResponse ConsultarGridVeiculoCombo(int proprietarioId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarGridVeiculoResponse ConsultarGridVeiculo(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        VeiculoResponse ConsultarPorId(int idVeiculo);
        Task<VeiculoResponse> ConsultarPorPlaca(string placa);
        Task<RespPadrao> Save(VeiculoRequest lVeiculoReq, bool integracao = false);
        Task<RespPadrao> CadastrarVeiculoCiot(VeiculoCiotRequest veiculoCiotRequest, bool integracao = false);
        Task<RespPadrao> ConsultaVeiculoEmpresaPorPlaca(VeiculoRequest lVeiculoReq, bool integracao = false);
        Task<RespPadraoApi> Integrar(VeiculoIntegrarApiRequest request);
        Task AlterarStatus(VeiculoStatusRequest lVeiculoStatus);
        Task<VeiculoConsultarApiResponse> Consultar(VeiculoConsultarApiRequest request);
        Task<RespPadrao> SalvarVeiculoEmpresa(VeiculoEmpresaRequest lVeiculoEmpresa);
        Task<RespPadrao> ConsultarVeiculoAbastecimento(string placa, string frota, int portadorId, int postoIdMobile = 0);
        RespPadrao ConsultaVeiculoAbastecimento (ConsultaVeiculoAbastecimentoMobileRequest placaFrotaPosto, string portadorCpfCnpj);
        Task<RespPadrao> ConsultarVeiculoFilial(int filialId, int modeloId = 0);
        Task<RespPadrao> ConsultarVeiculoCombustivel(int veiculoId);
        Task<ConsultarGridVeiculoCiotResponse> ConsultarGridVeiculoPortadorCiotCombo(string cpfCnpjProprietario, int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters);
    }
}