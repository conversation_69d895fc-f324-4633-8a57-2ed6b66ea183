using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Retencao;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Retencao;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Retencao.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Retencao
{
    public interface IRetencaoAppService : IAppService<Domain.Models.Retencao.Retencao,
        IRetencaoReadRepository, IRetencaoWriteRepository>
    {
        Task<RespPadrao> Cadastrar(RetencaoCadastrarRequest retencaoCadastrarRequest);

        ConsultarGridRetencaoResp ConsultarGridRetencao(DateTime dtInicial, DateTime dtFinal, int take, int page,
            OrderFilters orderFilters,
            List<QueryFilters> filters);

        List<ConsultarGridRetencao> DadosRelatorioGridRetencao(DateTime dtInicial, DateTime dtFinal, OrderFilters orderFilters,
            List<QueryFilters> filters);

        Task<RespPadrao> SetarRetencaoIntegrada(RetencaoCadastrarRequest  request);
    }
}