﻿using System;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;

namespace SistemaInfo.BBC.Application.Objects.Api.Empresa
{
    public class IntegrarEmpresaRequest : IntegrarPessoaRequest
    {
    }

    public class IntegrarEmpresaRequestValidator : IntegrarPessoaRequestValidator
    {
        public IntegrarEmpresaRequestValidator(IServiceProvider serviceProvider, ICidadeReadRepository cidadeRepository)
            : base(serviceProvider, cidadeRepository)
        {
        }

        public IntegrarEmpresaRequestValidator(IServiceProvider serviceProvider, IntegrarPessoaRequest instance,
            ICidadeReadRepository cidadeRepository) : base(serviceProvider, instance, cidadeRepository)
        {
        }
    }
}