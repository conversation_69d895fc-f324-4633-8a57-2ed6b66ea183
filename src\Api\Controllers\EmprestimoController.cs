using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Api.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Api.Controllers
{
    /// <summary>
    /// Class responsavel por conter metodos utilizados pela aplicação de emprestimo
    /// </summary>
    [Route("Emprestimo")]
    public class EmprestimoController : ApiControllerBase
    {
        
        private readonly IEmprestimoAppService _emprestimoAppService;
        
        /// <summary>
        /// injeção de dependencias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="emprestimoAppService"></param>
        public EmprestimoController(IAppEngine engine, IEmprestimoAppService emprestimoAppService) : base(engine)
        {
            _emprestimoAppService = emprestimoAppService;
        }

        /// <summary>
        /// Consulta emprestimos baseado nas informaçoes repassadas no parametro resquest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Consultar")]
        public JsonResult ConsultarEmprestimo([FromBody] ConsultarEmprestimoRequest request)
        {
            try
            {
                var lEmprestimos = _emprestimoAppService.ConsultarEmprestimos(request.Take, request.Page, request.CpfCnpj);
                
                return ResponseBase.ResponderSucesso(lEmprestimos);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
    }
}