using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Mail;
using SistemaInfo.BBC.Domain.Components.Email;

namespace SistemaInfo.BBC.Application.Email.Posto
{
    public class EmailPostoNotificacaoSla
    {
        public static void EnviarEmail(INotificationEmailExecutor notificationEmailExecutor, string posto, string farolSla, List<string> destinatarios)
        {
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

            using (var streamReader = new StreamReader(caminhoAplicacao + @"\Content\Email\FarolSla\enviar-email-farol-sla.html"))
            {
                var html = streamReader.ReadToEnd();
                html = html.Replace("{POSTO}", posto);
                html = html.Replace("{FAROL}", farolSla);
                
                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                foreach (var destinatario in destinatarios)
                {
                    notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                    {
                        To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                        Priority = MailPriority.High,
                        Subject = "NOTIFICACAO - FAROL SLA",
                        IsBodyHtml = true,
                        AlternateView = view
                    });
                }
            }
        }
    }
}