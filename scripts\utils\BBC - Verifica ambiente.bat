@echo off
::Confihuração de variaveis
set local=http://192.168.100.142
set web-port=9172/web
set posto-port=9173/web
set api-service=9110
set api-port=9161/bbc/api
set api-integracao-port=9161/bbc/apiintegracao
set api-abastecimento-port=9161/bbc/apiabastecimento
set api-mobile-port=9161/apimobile

set log=true

::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
::Não alterar
setlocal enabledelayedexpansion
cd "%~dp0"
set pastaRaiz=%CD%
set "Data=%DATE:/=-% %time:~0,2%-%time:~3,2%"
set "arquivo=%pastaRaiz%\log-validador %Data%.txt"
if %log%==true (echo Date; ErrorType; Target; ErrorLevel; Message >> "%arquivo%")

call :info Processo inicializado!
echo.

::web
curl GET "%local%:%web-port%/swagger" --verbose
if %errorlevel% neq 0 (
	call :log Erro; "%web-port%/swagger"; %errorlevel%; Erro na consulta
	goto error
)
call :log Sucesso; "%web-port%/swagger"; %errorlevel%; Consulta realizada

::posto
curl GET "%local%:%posto-port%/swagger" --verbose
if %errorlevel% neq 0 (
	call :log Erro; "%posto-port%/swagger"; %errorlevel%; Erro na consulta
	goto error
)
call :log Sucesso; "%posto-port%/swagger"; %errorlevel%; Consulta realizada

::api
echo curl GET "%local%:%api-port%/swagger" --verbose
curl GET "%local%:%api-port%/swagger" --verbose
if %errorlevel% neq 0 (
	call :log Erro; "%api-port%/swagger"; %errorlevel%; Erro na consulta
	goto error
)
call :log Sucesso; "%api-port%/swagger"; %errorlevel%; Consulta realizada

::api integração
curl GET "%local%:%api-integracao-port%/swagger" --verbose
if %errorlevel% neq 0 (
	call :log Erro; "%api-integracao-port%/swagger"; %errorlevel%; Erro na consulta
	goto error
)
call :log Sucesso; "%api-integracao-port%/swagger"; %errorlevel%; Consulta realizada

::api abastecimento
curl GET "%local%:%api-abastecimento-port%/swagger" --verbose
if %errorlevel% neq 0 (
	call :log Erro; "%api-abastecimento-port%/swagger"; %errorlevel%; Erro na consulta
	goto error
)
call :log Sucesso; "%api-abastecimento-port%/swagger"; %errorlevel%; Consulta realizada

::api mobile
curl GET "%local%:%api-mobile-port%/swagger" --verbose
if %errorlevel% neq 0 (
	call :log Erro; "%api-mobile-port%/swagger"; %errorlevel%; Erro na consulta
	goto error
)
call :log Sucesso; "%api-mobile-port%/swagger"; %errorlevel%; Consulta realizada

::verifica atualizações pendentes
for /f "delims=" %%i in ('curl -s "%local%:%web-port%/Atualizar/AtualizacaoPendente" --verbose') do set "response=%%i"
if %errorlevel% neq 0 (
	echo Erro: %errorlevel%
	goto error
)
call :log Sucesso; "%web-port%/Atualizar/AtualizacaoPendente"; %errorlevel%; Consulta realizada
call :log Sucesso; "%web-port%/Atualizar/AtualizacaoPendente"; %errorlevel%; %response%
call :info AtualizacaoPendente: %response%
echo %response% | findstr true >nul
if %errorlevel%==0 (
	echo.	
	pause
	::executa atualização se tiver
	curl -X POST "%local%:%web-port%/Atualizar/Executar" -H "accept: application/json" -d "{}"
	if %errorlevel% neq 0 (
		echo Erro: %errorlevel%
		goto error
	)
	echo.
	call :log Sucesso; "%web-port%/Atualizar/Executar"; %errorlevel%; Migration executada
) else (
	echo.
	call :log Info; "%web-port%/Atualizar/AtualizacaoPendente"; %errorlevel%; Sem migrations pendentes
)
echo.
goto success
exit 0

:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:log
	call :setESC
	if %~1==Erro (echo !ESC![91m %* !!ESC![0m)
	if %~1==Info (echo !ESC![96m %* !!ESC![0m)
	if %~1==Sucesso (echo !ESC![94m %* !!ESC![0m)
	if %log%==true (echo %DATE:/=-% %time:~0,8%; %* >> "%arquivo%")
	echo.
	exit /B 0

:info
	call :setESC
	echo !ESC![105m ==========================================================================================!!ESC![0m
	echo !ESC![105m %* !!ESC![0m
	echo !ESC![105m ==========================================================================================!!ESC![0m
	exit /B 0

:success
	call :setESC
	call :log Sucesso; swagger; %errorlevel%; Processo finalizado com Sucesso!
	echo.
	echo !ESC![92m ==========================================================================================!!ESC![0m
	echo !ESC![92m Processo finalizado com Sucesso!!!ESC![0m
	echo !ESC![92m ==========================================================================================!!ESC![0m
	pause
	exit 0
	
:error
	call :setESC
	call :log swagger; %errorlevel%; Erro; Processo finalizado com Erro!
	echo.
	echo !ESC![91m ==========================================================================================!!ESC![0m
	echo !ESC![91m Processo finalizado com Erro!!!ESC![0m
	echo !ESC![91m ==========================================================================================!!ESC![0m
	pause
	exit 1