using System;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Transacao
{
    public class TransacaoRequest
    {
        public int Id { get; set; }
        public int IdPagamentoEvento { get; set; }
        public int? <PERSON><PERSON> { get; set; }
        public int Origem { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string CodigoBanco { get; set; }
        /// <summary>
        /// ETipoContaDock Corrente = 1, Poupanca = 2, Salario = 3
        /// </summary>
        public int? TipoConta { get; set; }
        public decimal Valor { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime? DataBaixa { get; set; }
        public FormaPagamentoEvento FormaPagamento { get; set; }
        public Tipo? Tipo { get; set;  }
        public StatusPagamento Status { get; set; }
        public string Descricao { get; set; }
        public string IdEndToEnd { get; set; }
        public int? Qualificado { get; set; }

        #region Logs
        
        public string JsonEnvioDock { get; set; }
        public string JsonRespostaDock { get; set; }
        public DateTime? DataRetornoDock { get; set; }
        public int ResponseCodeDock { get; set; }

        #endregion
        
        public string Description { get; set; }
    }   
}
