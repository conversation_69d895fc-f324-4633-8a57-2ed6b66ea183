using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.AtualizacaoPrecoCombustivel
{
    public class AtualizacaoPrecoCombustivelRequestItem
    {
        public int Id { get; set; }
        public int CombustivelId { get; set; }
        public int PostoId { get; set; }
        public decimal? ValorBBCSolicitado { get; set; }
        public decimal? ValorBombaSolicitado { get; set; }
        
        /// <summary>
        /// 0 = Reprovada, 1 = Aprovada, 2 = Pendente, 3 = Cancelada
        /// </summary>
        public int? StatusAprovacao { get; set; } = 2;
        public string MotivoInterno { get; set; }
        public string MotivoExterno { get; set; }
        public string MotivoSolicitacao { get; set; }
    }
    
    public class AtualizacaoPrecoCombustivelRequest
    {
        public List<AtualizacaoPrecoCombustivelRequestItem> Solicitacoes { get; set; }
    }
}