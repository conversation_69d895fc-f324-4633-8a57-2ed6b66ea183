# Import
Import-Module -Name ("C:\devops\Utils.ps1")
Import-Module -Name ("C:\devops\Variaveis.ps1")

# Init
$scriptPath = $script:MyInvocation.MyCommand.Path
Log ""
Log "=========================================================================================="  
Log "::::: $scriptPath start :::::"
Log "CI BUILD SCRIPT START"
Log "=========================================================================================="  
Log ""

# VariÃ¡veis em uso
$nucleo           = "jsl"
$projeto          = "bbc"
$current_dir = ${PSScriptRoot}
$repo_folder = "$current_dir\..\.." # igual $clone_folder quando executado pela AppVeyor
$dotnet_folder = "C:\Program Files\dotnet\sdk"
#$imagem_versao = "5.0"
$imagem = "docker.sistemainfo.com.br/nodejs:10.19.0"

Log ">>> Variaveis em uso <<<"
Log "nuget_cache    = $nuget_cache"
Log "current_dir    = $current_dir"
Log "repo_folder    = $repo_folder"
Log "------------------------------------------------------------------------------------------"

# Compilar .net core 2.2
$container_name = "${nucleo}-${projeto}.node-build"
Log "Compilacao utilizando container: $container_name"

Log "Verificar existencia -> Docker-ContainerIsRunning -Name ""$container_name"""
if (Docker-ContainerIsRunning -Name "$container_name") {
    # não permite rodar o mesmo container
    Log "Container rodando em outro processo: $container_name"
    Write-Host "::::: $scriptPath end :::::"
    exit 1
}

Log "Verificar existencia -> Docker-ContainerExists -Name ""$container_name"""
if (Docker-ContainerExists -Name "$container_name") {
    # remover o container para nÃ£o conflitar
    Log "Removendo container: $container_name"
    docker container rm $container_name
}

Log "Criando container $container_name"    
docker create `
	--name $container_name `
	-v ${repo_folder}:${docker_repository} `
	${imagem} "C:\repo\scripts\autobuild\frontbuild.bat"
Log "Utilizando imagem: $imagem"

Log "Executando container $container_name"
$build_task =  Docker-StartContainer($container_name)

# Aguarda final dos builds
$build_task.WaitForExit()
Log "build build_task finalizado code:" 
Write-Host $build_task.ExitCode

# Tratando retorno falhas de build e exeuÃ§Ã£o de testes
if ($build_task.ExitCode -ne 0) {
	if ($build_task.ExitCode -eq 2) {
		$env:TEST_FAILED_ON_BUILD_STEP = $true
		Log "Falha no processo de teste unitario. build_task.ExitCode: $($build_task.ExitCode)"
	} else {
		Log "Falha no processo de build. build_task.ExitCode: $($build_task.ExitCode)"
		Write-Host "::::: $scriptPath end :::::"
		exit $build_task.ExitCode
	}
}

# OK
Log ""
Log "=========================================================================================="
Log "CI BUILD SCRIPT END"
Log "::::: $scriptPath end :::::"
Log "=========================================================================================="
Log ""