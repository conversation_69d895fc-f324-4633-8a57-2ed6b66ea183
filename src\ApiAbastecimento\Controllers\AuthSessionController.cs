using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.Configuration;
using NLog;
using SistemaInfo.BBC.ApiAbastecimento.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Usuario;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.ApiAbastecimento.Controllers
{
    /// <summary>
    /// Class de authsession dispoem de todos os metodos responsaveis pela auth do sistema
    /// </summary>
    [Route("AuthSession")]
    public class AuthSessionController : ApiControllerBase
    {
        /// <summary>
        /// Injeção de interface
        /// </summary>
        public IEmpresaReadRepository EmpresaReadRepository { get; }
        private readonly IConfiguration _config;
        private readonly IUsuarioAppService _UsuarioAppService;
        
        /// <summary>
        /// Injeção de dependencias
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="config"></param>
        /// <param name="usuarioAppService"></param>
        /// <param name="empresaReadRepository"></param>
        public AuthSessionController(IAppEngine engine, IConfiguration config, IUsuarioAppService usuarioAppService,IEmpresaReadRepository empresaReadRepository) : base(engine)
        {
            EmpresaReadRepository = empresaReadRepository;
            _config = config;
            _UsuarioAppService = usuarioAppService;
        }
        
        /// <summary>
        /// Metodo responsavel por gerar o token de acesso
        /// </summary>
        /// <param name="tokenRequest"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [Produces("application/json")]
        [HttpPost("GerarToken")]
        public RespPadrao GerarToken([FromBody]TokenRequest tokenRequest)
        {
            var log = LogManager.GetCurrentClassLogger();
            log.Info("GerarToken: ");
            log.Info("GerarToken: cnpj " + tokenRequest.CnpjEmpresa);
            var lEmpresa = EmpresaReadRepository.FirstOrDefault(x => x.Cnpj == tokenRequest.CnpjEmpresa);
            
            if ( lEmpresa == null)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Autenticação inválida!"
                };
            }

            var tokenSenha = CriptografiaUtils.GetHashSha1(tokenRequest.SenhaApi);
                
            if (tokenSenha!= lEmpresa.SenhaApi)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    data = null,
                    mensagem = "Autenticação inválida!"
                };
            }
            
            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Jti,  Guid.NewGuid().ToString("N")),
                new Claim("EmpresaId", lEmpresa.Id.ToString())
            };
            log.Info("GerarToken: empresaId " + claims.First(claim => claim.Type == "EmpresaId").Value.ToIntSafe());
            
            var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(_config["Authentication:SecretKey"]));
            
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var credential = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            
            var intervaloValidade = 600;
            
            var dataCriacao = DateTime.UtcNow;
            var dataExpiracao = dataCriacao + TimeSpan.FromSeconds(intervaloValidade);
            
            var token = new JwtSecurityToken
            (
                claims: claims,
                signingCredentials: credential,
                expires: dataExpiracao,
                issuer: "MyServer",
                audience: "EveryApplication"
            );
            
            var jwtToken = new
            { 
                token = new JwtSecurityTokenHandler().WriteToken(token),
                expiration = dataExpiracao.ToString("yyyy-MM-dd HH:mm:ss"),
            };
            
            return new RespPadrao()
            {
                sucesso = true,
                mensagem = "",
                data = new
                {
                    jwtToken.token
                }
            };            
        }
    }
}