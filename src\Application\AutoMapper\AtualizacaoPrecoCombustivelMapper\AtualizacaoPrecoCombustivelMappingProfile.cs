﻿using SistemaInfo.BBC.Application.Objects.Web.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.AtualizacaoPrecoCombustivelMapper
{
    public class AtualizacaoPrecoCombustivelMappingProfile : SistemaInfoMappingProfile
    {
        public AtualizacaoPrecoCombustivelMappingProfile()
        {
            CreateMap<AtualizacaoPrecoCombustivel, ConsultarGridHistoricoAtualizacaoPrecoCombustivel>()
                .ForMember(dest => dest.StatusAprovacaoDescricao, opts => opts.MapFrom(s => 
                    s.StatusAprovacao == 0 ? "Reprovada" : 
                    s.StatusAprovacao == 1 ? "Aprovada" : 
                    s.StatusAprovacao == 2 ? "Pendente" : 
                    "Cancelada"))
                .ForMember(dest => dest.DataCadastro,opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao,opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.UnidadeMedida,opts => opts.MapFrom(s => s.Combustivel.UnidadeMedida))
                .ForMember(dest => dest.DiferencaEntreValores, opts => opts.MapFrom(s => s.ValorBombaSolicitado - s.ValorBBCSolicitado))
                .ForMember(dest => dest.NomePosto, opts => opts.MapFrom(s => s.Posto.RazaoSocial))
                .ForMember(dest => dest.UsuarioAlteracao, opts => opts.MapFrom(s => s.Usuario.Nome))
                .ForMember(dest => dest.ValorBomba, opts => opts.MapFrom(s => s.ValorBomba))
                .ForMember(dest => dest.ValorBombaSolicitado , opts => opts.MapFrom(s => s.ValorBombaSolicitado))
                .ForMember(dest => dest.ValorBBC, opts => opts.MapFrom(s => s.ValorBBC))
                .ForMember(dest => dest.ValorBBCSolicitado, opts => opts.MapFrom(s => s.ValorBBCSolicitado))

                .ForMember(dest => dest.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome));
            
            CreateMap<AtualizacaoPrecoCombustivel, AtualizacaoPrecoCombustivelResponse>()
                .ForMember(dest => dest.StatusAprovacaoDescricao, opts => opts.MapFrom(s => 
                    s.StatusAprovacao == 0 ? "Reprovada" : 
                    s.StatusAprovacao == 1 ? "Aprovada" : 
                    s.StatusAprovacao == 2 ? "Pendente" : 
                    "Cancelada"))
                .ForMember(dest => dest.DataCadastro,opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao,opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DiferencaEntreValores, opts => opts.MapFrom(s => s.ValorBombaSolicitado - s.ValorBBCSolicitado))
                .ForMember(dest => dest.NomePosto, opts => opts.MapFrom(s => s.Posto.RazaoSocial))
                .ForMember(dest => dest.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome));
            
            CreateMap<AtualizacaoPrecoCombustivelRequestItem, AtualizacaoPrecoCombustivelSalvarCommand>();

            CreateMap<AtualizacaoPrecoCombustivelRequestItem, AtualizacaoPrecoCombustivelSalvarComRetornoCommand>();

            CreateMap<AtualizacaoPrecoCombustivelSalvarCommand, AtualizacaoPrecoCombustivel>();

            CreateMap<AtualizacaoPrecoCombustivelSalvarComRetornoCommand, AtualizacaoPrecoCombustivel>();
        }
    }
}