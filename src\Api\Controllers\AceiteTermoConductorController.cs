using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Api.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.AceiteTermo;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Api.Controllers
{
    /// <summary>
    /// Class da controller de aceite de termo conductor herdeira de ApiControllerBase
    /// </summary>
    [Route("AceiteTermoConductor")]
    public class AceiteTermoConductorController : ApiControllerBase
    {
        private readonly IAceiteTermoAppService _aceiteTermoAppService;
        /// <summary>
        /// Injeção de dependencias
        /// </summary>
        /// <param name="engine"> injeção da interface de appengine</param>
        /// <param name="aceiteTermoAppService"> injeção da interface aceiteTermo</param>
        public AceiteTermoConductorController(IAppEngine engine, IAceiteTermoAppService aceiteTermoAppService) : base(engine)
        {
            _aceiteTermoAppService = aceiteTermoAppService;
        }
        
        /// <summary>
        /// Metodo de consulta
        /// </summary>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("ConsultarTermos")]
        public JsonResult ConsultarTermos()
        {            
            try
            {                
                var response = _aceiteTermoAppService.ConsultarTermos().Result;
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }
    }
}