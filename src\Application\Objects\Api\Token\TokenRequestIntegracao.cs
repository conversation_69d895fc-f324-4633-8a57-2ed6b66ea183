namespace SistemaInfo.BBC.Application.Objects.Api.Token
{
    public class TokenRequestIntegracao
    {   
        /// <summary>
        /// Cnpj (pode ser tanto do grupo de empresa ou de uma empresa normal)
        /// </summary>
        public string Cnpj { get; set; }
        /// <summary>
        /// Obrigatório
        /// </summary>
        public string SenhaApi { get; set; }
        /// <summary>
        /// ClientSecret (pode ser tanto do grupo de empresa ou de uma empresa normal), obrigatório
        /// </summary>
        public string ClientSecret { get; set; }
        /// <summary>
        /// Cnpj da empresa, obrigatório se Cnpj é de um grupo de empresa
        /// </summary>
        public string Empresa { get; set; }
        
    }
}