﻿using System;
using System.Globalization;
using SistemaInfo.BBC.Application.Objects.Web.MDRPrazos;
using SistemaInfo.BBC.Domain.Models.MDRPrazos;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.MDRPrazosMapper
{
    public class MDRPrazosMappingProfile : SistemaInfoMappingProfile
    {
        public MDRPrazosMappingProfile()
        {
            CreateMap<MDRPrazosRequest, MDRPrazosSalvarCommand>()
                .ForMember(dest => dest.MDR, opt => opt.MapFrom(src => Decimal.Parse(src.MDR, NumberStyles.Any, new CultureInfo("pt-BR"))));
            
            CreateMap<MDRPrazosRequest, MDRPrazosAlterarStatusCommand>();
            
            CreateMap<MDRPrazosRequest, MDRPrazosSalvarComRetornoCommand>()
                .ForMember(dest => dest.MDR, opt => opt.MapFrom(src => Decimal.Parse(src.MDR, NumberStyles.Any, new CultureInfo("pt-BR"))));
            
            CreateMap<MDRPrazosSalvarCommand, MDRPrazos>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));
            
            CreateMap<MDRPrazosSalvarComRetornoCommand, MDRPrazos>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));
            
            CreateMap<MDRPrazosStatusRequest, MDRPrazosAlterarStatusCommand>();

            CreateMap<MDRPrazos, ConsultarGridMDRPrazos>()
                .ForMember(dest => dest.bancoNome, opts => opts.MapFrom(s => s.Banco.Nome));
            
            CreateMap<MDRPrazos, MDRPrazosResponse>();
        }
    }
}