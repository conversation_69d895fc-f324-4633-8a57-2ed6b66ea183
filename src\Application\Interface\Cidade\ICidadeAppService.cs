using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Web.Cidade;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Cidade
{
    public interface ICidadeAppService : IAppService<Domain.Models.Cidade.Cidade, ICidadeReadRepository, ICidadeWriteRepository>
    {
        ConsultarGridCidadeResponse ConsultarGridCidade(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<Domain.Models.Cidade.Cidade> ConsultarPorIbge(int ibge);
    }
}