﻿using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento;
using SistemaInfo.BBC.Domain.Models.PagamentoEvento.Commands;
using SistemaInfo.BBC.Domain.Models.Viagem;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.CentralPendenciasMapper
{
    public class CentralPendenciasMappingProfile : SistemaInfoMappingProfile
    {
        public CentralPendenciasMappingProfile()
        {
            CreateMap<CentralPendenciasRequest, PagamentoEventoSalvarCommand>();
            
            CreateMap<CentralPendenciasRequest, PagamentoEventoSalvarComRetornoCommand>();
            
            CreateMap<PagamentoEventoSalvarCommand, PagamentoEvento>();
            
            CreateMap<CodigoTransacaoSalvarCommand, PagamentoEvento>();
            
            CreateMap<ValorTransferenciaMotoristaCommand, PagamentoEvento>()
                .ForMember(a => a.ValorTransferenciaMotorista, opts => opts.MapFrom(d => d.ValorTransferenciaMotorista));

            CreateMap<PagamentoEventoSalvarComRetornoCommand, PagamentoEvento>()
                .ForMember(a => a.DataSolicitacaoCancelamento, opts => opts.MapFrom(d => d.DataSolicitacaoCancelamento));

            CreateMap<PagamentoEvento, ConsultarGridCentralPendencias>()
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => (int)s.Status))
                .ForMember(dest => dest.ViagemId, opts => opts.MapFrom(s => s.ViagemId))
                .ForMember(dest => dest.Ciot, opts => opts.MapFrom(s => s.Viagem.CiotViagem.Id))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.FilialExternoId, opts => opts.MapFrom(s => s.Viagem.FilialId))
                .ForMember(dest => dest.PagamentoExternoId, opts => opts.MapFrom(s => s.PagamentoExternoId))
                .ForMember(dest => dest.EmpresaNome, opts => opts.MapFrom(s => s.Viagem.Empresa.NomeFantasia))
                .ForMember(dest => dest.NomeProprietario, opts => opts.MapFrom(s => s.Viagem.PortadorProprietario.Nome))
                .ForMember(dest => dest.CpfcnpjProprietario, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Viagem.PortadorProprietario.CpfCnpj)))
                .ForMember(dest => dest.NomeMotorista, opts => opts.MapFrom(s => s.Viagem.PortadorMotorista.Nome))
                .ForMember(dest => dest.CpfcnpjMotorista, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Viagem.PortadorMotorista.CpfCnpj)))
                .ForMember(dest => dest.Tipo, opts => opts.MapFrom(s => (int?)s.Tipo))
                .ForMember(dest => dest.FormaPagamento, opts => opts.MapFrom(s => (int?)s.FormaPagamento))
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => StringHelper.FormatMoney(s.Valor)));
            
            CreateMap<PagamentoEvento, CentralPendenciasResponse>()
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => StringHelper.FormatMoney(s.Valor)));
            
            CreateMap<Viagem, PagamentoViagemIntegrarRequest>()
                .ForMember(dest => dest.CpfCnpjContratado, opts => opts.MapFrom(s => s.PortadorProprietario.CpfCnpj))
                .ForMember(dest => dest.CpfMotorista, opts => opts.MapFrom(s => s.PortadorMotorista.CpfCnpj))
                .ForMember(dest => dest.NomeContratado, opts => opts.MapFrom(s => s.PortadorProprietario.Nome))
                .ForMember(dest => dest.NomeMotorista, opts => opts.MapFrom(s => s.PortadorMotorista.Nome))
                .ForMember(dest => dest.IbgeOrigem, opts => opts.MapFrom(s => s.CidadeOrigem.Ibge))
                .ForMember(dest => dest.IbgeDestino, opts => opts.MapFrom(s => s.CidadeDestino.Ibge));
        }
    }
}