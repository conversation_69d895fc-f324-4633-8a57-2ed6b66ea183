﻿using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.Pix.Responses.Transferencia;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem;

public class CriarTransferenciaPixResponse : RespPadrao
{
    public CriarTransferenciaPixResponse(bool aSucesso, string aMensagem, object aData = null, int? statusCodeDock = null) : base(aSucesso, aMensagem, aData)
    {
        StatusCodeDock = statusCodeDock;
    }

    public CriarTransferenciaPixResponse()
    {
    }

    public CriarTransferenciaPixResponse(bool aSucesso, string aMensagem = null) : base(aSucesso, aMensagem)
    {
    }

    public CriarTransferenciaPixResponse(bool aSucesso, string aMensagem, object aData = null) : base(aSucesso, aMensagem, aData)
    {
    }

    public int? StatusCodeDock { get; set; }
}