using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Web.Modulo;
using SistemaInfo.BBC.Domain.Models.Modulo.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Modulo
{
    public interface IModuloAppService : IAppService<Domain.Models.Modulo.Modulo, IModuloReadRepository, IModuloWriteRepository>
    {
        Task<List<ConsultarModuloResponse>> ConsultarModulos(int? empresaId, int? postoId, int sistema);
    }
}