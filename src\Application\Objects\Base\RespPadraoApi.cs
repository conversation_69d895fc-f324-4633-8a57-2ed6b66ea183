using System.Runtime.Serialization;

namespace SistemaInfo.BBC.Application.Objects.Base
{
    [DataContract, KnownType(typeof(RespPadrao))]
    public class RespPadraoApi : RespPadrao
    {        
        public RespPadraoApi()
        {
        }

        public RespPadraoApi(RespPadrao respPadrao)
        {
            id = respPadrao.id;
            sucesso = respPadrao.sucesso;
            mensagem = respPadrao.mensagem.Length > 250 ? respPadrao.mensagem.Substring(0, 250) : respPadrao.mensagem;
            data = respPadrao.data;
        }
    }
}