
using System;
using SistemaInfo.BBC.Application.Objects.Base;

namespace SistemaInfo.BBC.Application.Objects.Web.Abastecimento
{
    public class DtoConsultaGridAbastecimento : BaseGridRequest
    {
        public DateTime? DataInicial { get; set; }
        public DateTime? DataFinal { get; set; }
    } 
    public class DtoConsultaGridLoteAbastecimento : BaseGridRequest
    {
        public int LotePagamento { get; set; }
    } 
    public class DtoConsultaGridFinanceiroLoteAbastecimento : BaseGridRequest
    {
        public int PagamentoAbastecimento { get; set; }
    }
    public class DtoConsultaGridProtocoloAbastecimento : BaseGridRequest
    {
        public int Protocolo { get; set; }
    }
    public class DtoConsultaGridPainelAbastecimento : BaseGridRequest
    {
        public int Status { get; set; }
        public int EmpresaId { get; set; }
        public DateTime DataInicial { get; set; }
        public DateTime DataFinal { get; set; }
    }
}