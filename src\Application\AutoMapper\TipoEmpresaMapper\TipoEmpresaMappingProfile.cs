﻿using SistemaInfo.BBC.Application.Objects.Web.TipoEmpresa;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.TipoEmpresaMapper
{
    public class TipoEmpresaMappingProfile : SistemaInfoMappingProfile
    {
        public TipoEmpresaMappingProfile()
        {
            CreateMap<TipoEmpresaRequest, TipoEmpresaSalvarCommand>();
            
            CreateMap<TipoEmpresaRequest, TipoEmpresaAlterarStatusCommand>();

            CreateMap<TipoEmpresaRequest, TipoEmpresaSalvarComRetornoCommand>();

            CreateMap<TipoEmpresaSalvarCommand, TipoEmpresa>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<TipoEmpresaSalvarComRetornoCommand, TipoEmpresa>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<TipoEmpresaStatusRequest, TipoEmpresaAlterarStatusCommand>();

            CreateMap<TipoEmpresa, ConsultarGridTipoEmpresa>();
        }
    }
}