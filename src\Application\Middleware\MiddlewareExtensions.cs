using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.FileProviders;

public static class MiddlewareExtensions
{
    public static IApplicationBuilder UseCustomStatusCodePages(this IApplicationBuilder app)
    {
        app.UseStatusCodePages(context => 
        {
            var statusCode = context.HttpContext.Response.StatusCode;

            if (statusCode == 404)
            {
                context.HttpContext.Response.Redirect("/404.html"); // ATENCAO, tem que estar na pasta wwwroot
            }
            else if (statusCode >= 500)
            {
                context.HttpContext.Response.Redirect("/500.html"); // ATENCAO, tem que estar na pasta wwwroot
            }

            return Task.CompletedTask;
        });

        return app;
    }

    public static IApplicationBuilder UseCustomDefaultFiles(this IApplicationBuilder app)
    {
        var options = new DefaultFilesOptions();
        options.DefaultFileNames.Clear();
        options.DefaultFileNames.Add("index.html"); // ATENCAO, tem que estar na pasta wwwroot
        app.UseDefaultFiles(options);

        return app;
    }
    
    public static IApplicationBuilder UseCustomStaticFiles(this IApplicationBuilder app)
    {
       app.UseStaticFiles(new StaticFileOptions
       {
        FileProvider = new PhysicalFileProvider(Path.Combine(Directory.GetCurrentDirectory(), "wwwroot")),
                RequestPath = ""});
    
        return app;
     }
}
