﻿using SistemaInfo.BBC.Application.Objects.Base;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem;

public class ConsultarTransferenciaResponse : RespPadrao
{
    public ConsultarTransferenciaResponse(int? statusCodeDock)
    {
        StatusCodeDock = statusCodeDock;
    }

    public ConsultarTransferenciaResponse(int? statusCodeDock, bool aSucesso, string aMensagem = null) : base(aSucesso, aMensagem)
    {
        StatusCodeDock = statusCodeDock;
    }

    public ConsultarTransferenciaResponse(int? statusCodeDock, bool aSucesso, string aMensagem, object aData = null) : base(aSucesso, aMensagem, aData)
    {
        StatusCodeDock = statusCodeDock;
    }

    public ConsultarTransferenciaResponse()
    {
    }

    public int? StatusCodeDock { get; set; }
}