﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PostoContato;
using SistemaInfo.BBC.Application.Objects.Web.PostoContato.Request;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.PostoContato.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.PostoContato
{
    public interface IPostoContatoAppService : IAppService<Domain.Models.PostoContato.PostoContato, IPostoContatoReadRepository,
        IPostoContatoWriteRepository>
    {
        ConsultarGridPostoContatoResponse ConsultarGridPostoContato(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);
        ConsultarPostoContatoIdResponse ConsultarPorId(int idPosto);
        Task<RespPadrao> Save(PostoContatoRequest lPostoReq);
    }
}