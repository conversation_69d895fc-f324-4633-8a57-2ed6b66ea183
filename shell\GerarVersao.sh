# Linux

# Excluíndo arquivos atualmente na pasta destino da publicação
rm -r "/home/<USER>/SIE/Versao/Cartao/Api"
rm -r "/home/<USER>/SIE/Versao/Cartao/Web"
rm -r "/home/<USER>/SIE/Versao/Cartao/Services"
rm -r "/home/<USER>/SIE/Versao/Cartao/Processadoras/BizVisaCargoApi"

# Publicar projetos como release
dotnet publish "..\Cartao.Api\Cartao.Api.csproj" -c Release -o "/home/<USER>/SIE/Versao/Cartao/Api" -f netcoreapp2.0
dotnet publish "..\Cartao.Web\Cartao.Web.csproj" -c Release -o "/home/<USER>/SIE/Versao/Cartao/Web" -f netcoreapp2.0
dotnet publish "..\Cartao.Service\Cartao.Service.csproj" -c Release -o "/home/<USER>/SIE/Versao/Cartao/Services" -f netcoreapp2.0
dotnet publish "..\Processadora.Biz.Service\Processadora.Biz.Service.csproj" -c Release -o "/home/<USER>/SIE/Versao/Cartao/Processadoras/BizVisaCargo" -f netcoreapp2.0

# Remover arquivos appsettings.json
#del /s /q /f %outputdir%\appsettings*.json
#del /s /q /f %outputdir%\web*.config

rm -rf `find /home/<USER>/SIE/Versao/Cartao/ -iname "*appsettings*.json"`
rm -rf `find /home/<USER>/SIE/Versao/Cartao/ -iname "*web*.config"`
rm -rf `find /home/<USER>/SIE/Versao/Cartao/ -iname "*Web*.config"`
