using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiAbastecimento.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiAbastecimento.Controllers
{
    /// <summary>
    /// Classe contendo metodos utilizado na aplicação de PagamentoAbastecimento
    /// </summary>
    [Route("PagamentoAbastecimentos")]
    public class PagamentoAbastecimentoController : ApiControllerBase<IPagamentoAbastecimentoAppService>
    {
        /// <summary>
        /// Injeção de dependencias para a aplicação PagamentoAbastecimento
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public PagamentoAbastecimentoController(IAppEngine engine, IPagamentoAbastecimentoAppService appService) : base(engine, appService)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="abastecimentoId"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("IntegrarRetencao")]
        public JsonResult IntegrarRetencao([FromBody] int abastecimentoId)
        {            
            try
            {
                var response = AppService.IntegrarRetencao(abastecimentoId).Result;
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }    
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="retencaoId"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("CancelarRetencao")]
        public JsonResult CancelarRetencao([FromBody] int retencaoId)
        {            
            try
            {
                var response = AppService.CancelarRetencao(retencaoId).Result;
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }    
       
        /// <summary>
        /// Metodo responsavel pelo envio de transacoes entre conta transitoria e conta do posto
        /// </summary>
        /// <param name="pagametoId"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("EnviarPagamento")]
        public JsonResult EnviarPagamento([FromBody] int pagametoId)
        {            
            try
            {
                var response = AppService.EnviarPagamento(pagametoId).Result;
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }
    }
 }