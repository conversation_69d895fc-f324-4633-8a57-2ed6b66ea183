using System.Linq;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Helpers.Ciot
{
    public static class HtmlCiotReportHelper
    {
        public static string GetHtmlImpressaoCiot(OperacaoTransporteMessageResponseInfo declaracaoCiot)
        {
            var veiculos = string.Empty;
            var informacoesViagem = string.Empty;
            var css = GetCssImpressaoCiot();

            if (declaracaoCiot.VeiculosList != null && declaracaoCiot.VeiculosList.Any())
                veiculos = declaracaoCiot.VeiculosList.Aggregate(veiculos, (current, veiculo) =>
                        current + $@"<div class='campos'>
                                          <span class='campoBold'> Placa: </span> {veiculo?.Placa?.ToPlacaFormato()}
                                     </div>
                                     <div class='campos'>
                                         <span class='campoBold'> RNTRC : </span> {veiculo?.RNTRC}
                                     </div>");

            if (declaracaoCiot.Tipo == TipoCiot.Padrão.ToInt())
                informacoesViagem = $@"<div class='campos'>
                                           <span class='campoBold'>DATA INICIO:</span> {declaracaoCiot.DataInicioFrete.FormatDateBr()}
                                       </div>
                                       <div class='campos'>
                                           <span class='campoBold'>DATA FIM:</span> {declaracaoCiot.DataFim.FormatDateBr()}
                                       </div>
                                       <div class='campos'>
                                           <span class='campoBold'>ORIGEM:</span> {declaracaoCiot.CidadeOrigemPadrao?.Nome} - {declaracaoCiot.CidadeOrigemPadrao?.Uf}
                                       </div>
                                       <div class='campos'>
                                           <span class='campoBold'>DESTINO:</span> {declaracaoCiot.CidadeDestinoPadrao?.Nome} - {declaracaoCiot.CidadeOrigemPadrao?.Uf}
                                       </div>
                                       <div class='campos'>
                                           <span class='campoBold'>NATUREZA CARGA:</span> {declaracaoCiot.CodNaturezaCarga}
                                       </div>
                                       <div class='campos'>
                                           <span class='campoBold'>PESO CARGA:</span> {declaracaoCiot.Peso}
                                       </div>";

            if (declaracaoCiot.Tipo == TipoCiot.Agregado.ToInt())
            {     
                    
                var linhasTabela = string.Empty;

                var viagensCiot = declaracaoCiot.ViagensList;
                if (viagensCiot.Count > 0)
                {

                    // var viagensAgrupadas =
                    //     viagensCiot.GroupBy(o => new {o.CidadeOrigemId, o.CidadeDestinoId, o.NaturezaCargaId});
              
                    foreach (var viagem in viagensCiot)
                    {
                        // var totalViagens = viagens.Count();
                        var pesoTotal = viagensCiot.Sum(o => o.Peso);
                        // var viagem = viagens.FirstOrDefault();

                        // if (viagem.CidadeOrigem == null)
                        //     viagem.CidadeOrigem =
                        //         cidadeReadRepository.FirstOrDefault(o => o.Id == viagem.CidadeOrigemId);

//                        var naturezaCarga =
//                            naturezaCargaAppService.Repository.Query.FirstOrDefault(o =>
//                                o.Id == viagem.NaturezaCargaId);

                        linhasTabela += $@"<tr>
                                           <td>{viagem.CidadeOrigem?.Nome} - {viagem.CidadeOrigem?.Uf}</td>
                                           <td>{viagem.CidadeDestino?.Nome} - {viagem.CidadeDestino?.Uf}</td>
                                           <td>{viagem.CodigoNaturezaCarga}</td> 
                                           <td>{pesoTotal}</td>
                                           <td>{viagensCiot.Count}</td>
                                       </tr>";
                    }
                }
                
                // Codigo natureza carga - descricao

                var tabela = $@"<div class='div-table'>
                                    <table class='table'>
                                        <thead>
                                            <tr>
                                                <th>ORIGEM</th>
                                                <th>DESTINO</th>
                                                <th>NATUREZA CARGA</th>
                                                <th>PESO CARGA</th>
                                                <th>Nº VIAGENS</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {linhasTabela}
                                        </tbody>
                                    </table>
                                </div>";

                informacoesViagem = tabela;
            }

            var html = $@"{css}
                          <div class='body'>
                              <div class='title'>
                                  DECLARAÇÃO DE OPERAÇÃO DE TRANSPORTE
                              </div>
                              <center>
                                  <div class='cabecalho1'>
                                      <img class='img' src='data:image/png;base64,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'>
                                  </div>
                                  <div class='cabecalho2'>
                                      CÓDIGO IDENTIFICADOR DE OPERAÇÃO DE TRANSPORTE : <span class='campoBold'> {declaracaoCiot.Ciot} </span>
                                  </div>
                              </center>

                              <div class='Subtitle' style='margin-top:30px;'>
                                  INFORMAÇÕES DO CONTRATANTE
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>NOME/RAZÃO SOCIAL:</span> {declaracaoCiot.Contratante?.NomeRazaoSocial}
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>CNPJ/CPF:</span> {declaracaoCiot.Contratante?.CpfCnpj?.ToCNPJFormato()}
                              </div>

                              <div class='Subtitle'>
                                  INFORMAÇÕES DO CONTRATADO
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>NOME/RAZÃO SOCIAL:</span> {declaracaoCiot.Proprietario?.NomeRazaoSocial}
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>CNPJ/CPF:</span> {declaracaoCiot.Proprietario?.CpfCnpj.ToCpfOrCnpj()}
                              </div>
                              <div class='campos' style='width: 90%;'>
                                  <span class='campoBold'>RNTRC:</span> {declaracaoCiot.Proprietario?.RNTRC}
                              </div>
                              <div class='Subtitle'>
                                  INFORMAÇÕES DA VIAGEM
                              </div>
                              {informacoesViagem}

                              <div class='Subtitle'>
                                  INFORMAÇÕES DO FRETE
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>VALOR FRETE:</span> R$ {declaracaoCiot.ValorFrete:N}
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>VALOR IMPOSTOS:</span> R$ {declaracaoCiot?.ValorImposto:N}
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>VALOR COMBUSTÍVEL:</span> R$ {declaracaoCiot?.ValorCombustivel:N}
                              </div>
                              <div class='campos' style='width: 90%;'>
                                  <span class='campoBold'>VALOR PEDÁGIO:</span> R$ {declaracaoCiot?.ValorPedagio:N}
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>QTDE TARIFAS:</span> {declaracaoCiot.QuantidadeTarifas ?? 0}
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>VALOR TARIFAS:</span> R$ {declaracaoCiot.ValorTarifas:N}
                              </div>

                              <div class='Subtitle'>
                                  INFORMAÇÕES DO DESTINATÁRIO
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>NOME/RAZÃO SOCIAL:</span> {declaracaoCiot.Destinatario?.NomeRazaoSocial}
                              </div>
                              <div class='campos'>
                                  <span class='campoBold'>CNPJ/CPF:</span> {declaracaoCiot.Destinatario?.CpfCnpj?.ToCpfOrCnpj()}
                              </div>
                              <div class='campos' style='width: 90%;'>
                                  <span class='campoBold'>ENDEREÇO:</span> {declaracaoCiot.Destinatario?.Endereco}
                              </div>

                              <div class='Subtitle'>
                                  VEÍCULOS
                              </div>
                              {veiculos}
                          </div>";
            return html;
        }

        private static string GetCssImpressaoCiot()
        {
            return @"<style>
                        .body {
                            font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif
                        }

                        .title {
                            border: 1px solid black;
                            width: 100%;
                            font-size: 20px;
                            text-align: center;
                            font-weight: bold;
                            padding: 3px;
                        }

                        .Subtitle {
                            border: 1px solid black;
                            width: 100%;
                            font-size: 15px;
                            text-align: center;
                            font-weight: bold;
                            padding: 3px;
                        }

                        .cabecalho1 {
                            width: auto;
                            /* display: inline-block; */
                            height: auto;
                            margin-bottom: 15px;
                            margin-right: 10px;
                        }

                        .img {
                            width: 150px;
                            margin-left: 10px;
                            margin-top: 15px;
                        }

                        .cabecalho2 {
                            width: auto;
                            display: inline-block;
                            padding: 3px;
                            
                        }

                        .campos {
                            display: inline-block;
                            width: 49%;
                            padding: 2px;
                            margin-bottom: 3px;
                            margin-top: 3px;
                        }

                        .campoBold {
                            font-weight: bold;
                        }

                        .div-table {
                            margin-top: 5px; 
                            margin-bottom: 5px;
                            width: 100%;
                            font-size: 15px;
                            text-align: center;
                            font-weight: bold;
                            padding: 3px;
                        }

                        .table {
                            width: 100%;
                            border-collapse: collapse;
                        }

                        th {
                            background-color: #C4C4C4;
                            /* text-align: left; */
                        }

                        td {
                            /* text-align: center; */
                        }

                        th, td {
                            border: 1px solid black;
                            text-align: center;
                        }

                        tr:nth-child(even) {
                            /*background-color: #f2f2f2;*/
                        }
                     </style>";
        }
    }
}