﻿using SistemaInfo.BBC.Application.Objects.Api.Parametros;
using SistemaInfo.BBC.Application.Objects.Web.Parametros;
using SistemaInfo.BBC.Domain.Contracts.Parametro;
using SistemaInfo.BBC.Domain.Models.Parametros;
using SistemaInfo.BBC.Domain.Models.Parametros.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.ParametroMapper
{
    public class ParametroMappingProfile : SistemaInfoMappingProfile
    {
        public ParametroMappingProfile()
        {
            CreateMap<Parametros, ConsultarGridParametro>();
            
            CreateMap<ParametroRequest, ParametrosSalvarCommand>();
            
            CreateMap<ParametrosSalvarCommand, Parametros>();

            CreateMap<ParametrosCadastrarRequest, ParametrosSalvarCommand>();

            CreateMap<ParametrosCadastrarRequest, SincronizarParametroPedagioMessage>();

            CreateMap<ParametrosValePedagioRequest, ParametroSincronizarMessage>();
            
            CreateMap<ParametrosCadastrarRequest, ParametroSalvarComRetornoCommand>();
            
            CreateMap<ParametroSalvarComRetornoCommand, Parametros>();
            
            CreateMap<Parametros, ParametroSalvarComRetornoCommand>();
            
        }
    }
}