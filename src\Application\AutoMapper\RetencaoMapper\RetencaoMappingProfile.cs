﻿using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Retencao;
using SistemaInfo.BBC.Application.Objects.External.Captalys;
using SistemaInfo.BBC.Application.Objects.Web.Retencao;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Retencao;
using SistemaInfo.BBC.Domain.Models.Retencao.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.RetencaoMapper
{
    public class RetencaoMappingProfile : SistemaInfoMappingProfile
    {
        public RetencaoMappingProfile()
        {
            CreateMap<Retencao, ConsultarGridRetencao>()
                .ForMember(d => d.DataIntegracao, opts => opts.MapFrom(s => s.DataIntegracao.ToStringBr(FormatDateTimeMethod.ShortDate)))
                .ForMember(d => d.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDate)))
                .ForMember(d => d.Status, opts => opts.MapFrom(s => s.Status))
                .ForMember(d => d.Nome, opt => opt.MapFrom(src => src.Pagamento.Portador.Nome))
                .ForMember(d => d.cpfCnpj, opt => opt.MapFrom(src => src.Pagamento.Portador.CpfCnpj.FormatarCpfCnpj(true)))
                .ForMember(d => d.Valor, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.Valor)));

            CreateMap<Retencao, EmprestimoRetencaoConsultarParaRelatorio>()
                .ForMember(o => o.Id, opt => opt.MapFrom(o => o.Id.ToString()))
                .ForMember(o => o.DataIntegracao, opt => opt.MapFrom(o => o.DataIntegracao != null ? o.DataIntegracao.Value.ToString("dd/MM/yyyy") : null))
                .ForMember(o => o.DataCadastro, opt => opt.MapFrom(o => o.DataCadastro.Value.ToString("dd/MM/yyyy")))
                .ForMember(o => o.PagamentoId, opt => opt.MapFrom(o => o.PagamentoId.ToString()))
                .ForMember(o => o.EmprestimoId, opt => opt.MapFrom(o => o.EmprestimoId.ToString()))
                .ForMember(o => o.Status, opt => opt.MapFrom(o => o.Status.DescriptionAttr()))
                .ForMember(o => o.Valor, opt => opt.MapFrom(o => $"R$ {o.Valor:N}"));

            CreateMap<RetencaoReq, Domain.External.Captalys.DTO.Retencao.RetencaoReq>()
                .ForMember(dest => dest.cnpj_conta, opt => opt.MapFrom(src => src.CnpjConta))
                .ForMember(dest => dest.id_operacao, opt => opt.MapFrom(src => src.IdOperacao))
                .ForMember(dest => dest.numero_conta, opt => opt.MapFrom(src => src.NumeroConta))
                .ForMember(dest => dest.dt_lancamento, opt => opt.MapFrom(src => src.DataLancamento.ToString("s")))
                .ForMember(dest => dest.id_lancamento, opt => opt.MapFrom(src => src.IdLancamento))
                .ForMember(dest => dest.valor_liquido, opt => opt.MapFrom(src => src.ValorLiquido))
                .ForMember(dest => dest.descricao_lancamento, opt => opt.MapFrom(src => src.DescricaoLancamento));
            
            CreateMap<RetencaoSalvarCommand, Retencao>();

            CreateMap<RetencaoCadastrarRequest, RetencaoSalvarCommand>();
        }
    }
}