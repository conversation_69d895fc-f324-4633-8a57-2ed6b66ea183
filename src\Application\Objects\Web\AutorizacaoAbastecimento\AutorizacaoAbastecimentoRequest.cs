using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Web.AutorizacaoAbastecimento
{
    public class AutorizacaoAbastecimentoRequest
    {
        public int Id { get; set; }
        public int? EmpresaId { get; set; }
        public int? FilialId { get; set; }
        public int? ModeloId { get; set; }
        public int VeiculoId { get; set; }
        public int CombustivelId { get; set; }
        public decimal Litragem { get; set; }
        public string Uf { get; set; }
        /// <summary>
        /// 0 = Nenhum (Usa o tipo de abastecimento do veículo), 1 = Orçamento, 2 = Autorização, 3 = Extra
        /// </summary>
        public int Metodo { get; set; }
        public EIdentificadorOrigemAutorizacaoAbastecimento? IdentificadorOrigem { get; set; } = EIdentificadorOrigemAutorizacaoAbastecimento.BBCControle;

    }
}