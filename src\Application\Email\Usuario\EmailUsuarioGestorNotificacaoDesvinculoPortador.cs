using System;
using System.IO;
using System.Net.Mail;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Components.Email;

namespace SistemaInfo.BBC.Application.Email.Usuario
{
    public class EmailUsuarioGestorNotificacaoDesvinculoPortador
    {
        public static RespPadrao EnviarEmail(INotificationEmailExecutor notificationEmailExecutor, 
            string destinatario, string portadorCpf, string portadorNome, string gestorNome, string empresaNome)
        {
            try
            {
                var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

                using (var streamReader = new StreamReader(caminhoAplicacao + @"Content\Email\Usuario\gestor-notificacao-desvinculo-portador.html"))
                {
                    var html = streamReader.ReadToEnd();
                    
                    html = html.Replace("{PORTADOR_NOME}", portadorNome);
                    html = html.Replace("{PORTADOR_CPF}", portadorCpf);
                    html = html.Replace("{EMPRESA_NOME}", empresaNome);
                    html = html.Replace("{GESTOR_NOME}", gestorNome);
                    
                    var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                    notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                    {
                        To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                        Priority = MailPriority.High,
                        Subject = "NOTIFICAÇÃO DE CANCELAMENTO DE PORTADOR POR INATIVIDADE",
                        IsBodyHtml = true,
                        AlternateView = view
                    });

                    return new RespPadrao()
                    {
                        sucesso = true,
                        mensagem = "Email enviado com sucesso!"
                    };
                }
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = "Email não enviado! Mensagem: " + e.Message
                };
            }
        }
    }
}