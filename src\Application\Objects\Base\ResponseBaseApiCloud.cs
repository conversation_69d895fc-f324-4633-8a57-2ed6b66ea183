using System;
using Microsoft.AspNetCore.Mvc;
using NLog;

namespace SistemaInfo.BBC.Application.Objects.Base
{
    public class ResponseBaseApiCloud
    {
        
        public static Logger _logger = LogManager.GetCurrentClassLogger();
        public static JsonResult Responder(bool sucesso, string msg, object data, string error = null)
        {
            return BigJson(new
            {
                message = msg ?? "",
                success = sucesso,
                data,
                error
            });
        }

        public static  JsonResult ResponderSucesso(object data)
        {
            return BigJson(new
            {
                message = "Operação realizada com sucesso!",
                success = true,
                data = data
            });
        }
        
        public static  JsonResult ResponderSucesso(string msg)
        {
            return BigJson(new
            {
                message = msg,
                success = true
            });
        }
        
        public static  JsonResult JsonGrid(object data, decimal qtdItens)
        {
            return ResponderSucesso(new
            {
                totalItems = qtdItens,
                items = data 
            });
        }
        
        public static JsonResult ResponderErro(string message)
        {
            return Responder(false, message.Length > 250 ? message.Substring(0, 250) : message, null);
        }
        
        public static JsonResult ResponderErro(Exception ex)
        {
            _logger.Error(ex);

            var msg = ex.Message;
            if (!string.IsNullOrWhiteSpace(ex.InnerException?.Message))
                msg += " / " + ex.InnerException;

            return Responder(false, msg,null);
        }
        
        public static JsonResult BigJson(object data)
        {
            return new JsonResult(data);
        }
    }
}