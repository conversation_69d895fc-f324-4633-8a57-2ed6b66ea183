using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ManutencaoAbastecimento;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.ManutencaoAbastecimento
{
    public interface IManutencaoAbastecimentoAppService : IAppService<Domain.Models.Abastecimento.Abastecimento, IAbastecimentoReadRepository, IAbastecimentoWriteRepository>
    {
        ConsultarGridHistoricoManutencaoAbastecimentoResponse ConsultarGridHistoricoCancelamentos(int IdPosto, DateTime dtInicial, DateTime dtFinal, int status, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> ReimpresaoProtocolo(ManutencaoAbastecimentoReimpresaoRequest abastecimento, bool integracao = false);
        List<ManutencaoAbastecimentoResponse> ConsultarAbastecimentos(int idEmpresa, DateTime startDate, DateTime endDate);
    }
}