﻿using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivel;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivelProduto;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto;

namespace SistemaInfo.BBC.Application.AutoMapper.PostoCombustivelMapper
{
    public class PostoCombustivelMappingProfile : SistemaInfoMappingProfile
    {
        public PostoCombustivelMappingProfile()
        {
            CreateMap<PostoCombustivelRequest, PostoCombustivelSaveCommand>();
            
            CreateMap<PostoCombustivelRequest, PostoCombustivelSaveComRetornoCommand>();

            CreateMap<PostoCombustivelSaveCommand, PostoCombustivel>();
            
            CreateMap<PostoCombustivelSaveComRetornoCommand, PostoCombustivel>();

            CreateMap<PostoCombustivelProdutoRequest, PostoCombustivelSaveCommand>();
            
            CreateMap<PostoCombustivelProdutoRequest, PostoCombustivelSaveComRetornoCommand>();

            CreateMap<PostoCombustivelSaveCommand, PostoCombustivelProduto>();
            
            CreateMap<PostoCombustivelSaveComRetornoCommand, PostoCombustivelProduto>();
        }
    }
}