﻿using SistemaInfo.BBC.Application.Objects.Api.Pedagio;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoValePedagio;
using SistemaInfo.BBC.Domain.Contracts.Pedagio;
using SistemaInfo.BBC.Domain.Contracts.Transacao;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.PedagioMapper;

public class PedagioMappingProfile : SistemaInfoMappingProfile
{
    public PedagioMappingProfile()
    {
        CreateMap<IntegrarPedagioMessageResponse, IntegrarPagamentoPedagioResponse>()
            .ForPath(x => x.data, opts => opts.MapFrom(x => x.Data));
        
        CreateMap<IntegrarPedagioMessageResponseInfo, IntegrarPagamentoPedagioResponseData>()
            .ForMember(dest => dest.PagamentoPedagioId, opt => opt.MapFrom(src => src.PedagioId))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(src => src.DataBaixa))
            .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.Valor))
            .ForMember(dest => dest.ValorTarifa, opt => opt.MapFrom(src => src.ValorTarifa));
        
        CreateMap<ComplementarPedagioMessageResponseInfo, ComplementarPagamentoPedagioResponseData>()
            .ForMember(dest => dest.PagamentoPedagioId, opt => opt.MapFrom(src => src.PedagioId))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(src => src.DataBaixa))
            .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.Valor))
            .ForMember(dest => dest.ValorTarifa, opt => opt.MapFrom(src => src.ValorTarifa));
        
        CreateMap<ConsultarPorIdResponseMessage, ConsultarPagamentoValePedagioResponse>();
        CreateMap<CancelarPagamentoPedagioRequest, CancelarPedagioMessage>();

        CreateMap<TransacaoConsultaGridMessageItem, ConsultarGridTransacaoPagamentoValePedagioItem>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ResponseCodeDock, opt => opt.MapFrom(src => src.ResponseCodeDock))
            .ForMember(dest => dest.DataCadastro, opt => opt.MapFrom(src => src.DataCadastro.ToString("G")))
            .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(src => src.DataBaixa != null ? src.DataBaixa.ToDateTime().ToString("G") : ""))
            .ForMember(dest => dest.DataRetornoDock, opt => opt.MapFrom(src => src.DataRetornoDock != null ? src.DataRetornoDock.ToDateTime().ToString("G") : ""))
            .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.Valor.ToString("C2")))
            .ForMember(dest => dest.TipoTransacao, opt => opt.MapFrom(src => src.Tipo.GetDescription()))
            .ForMember(dest => dest.Mensagem, opt => opt.MapFrom(src => src.Ocorrencia))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.GetDescription()));
        
        CreateMap<PagamentoPedagioHistoricoGridMessageItem, ConsultarGridPagamentoValePedagioHistoricoItem>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.DataCadastro, opt => opt.MapFrom(src => src.DataCadastro != null ? src.DataCadastro.ToString("G") : ""))
                .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(src => src.DataBaixa != null ? src.DataBaixa.ToDateTime().ToString("G") : ""))
                .ForMember(dest => dest.DataAlteracao, opt => opt.MapFrom(src => src.DataAlteracao != null ? src.DataAlteracao.ToDateTime().ToString("G") : ""))
                .ForMember(dest => dest.DataCancelamento, opt => opt.MapFrom(src => src.DataCancelamento != null ? src.DataCancelamento.ToDateTime().ToString("G") : ""))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.Valor.ToString("C2")))
                .ForMember(dest => dest.ValorTotal, opt => opt.MapFrom(src => (src.Valor + src.ValorComplemento).ToString("C2")))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.GetHashCode()))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.FormaPagamento.GetHashCode()));
    }
}