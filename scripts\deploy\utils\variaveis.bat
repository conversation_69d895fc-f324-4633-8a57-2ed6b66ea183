echo off
::IIS
set server=192.168.100.142
set porta=8172
set username=SISTEMAINFO\rodolfo.shimotsu
set pass=9631

cd %~dp0
cd ..\..\..
set projeto=%cd%
set publish_dir=%projeto%\src\artefatos
set sorce_dir=%projeto%\src
set targetFrameWork=netcoreapp2.1

echo sorce_dir: %sorce_dir%
echo publish_dir: %publish_dir%

if exist "%publish_dir%" rd /S /Q "%publish_dir%"
mkdir "%publish_dir%"

set msdeploypath=C:\Program Files (x86)\IIS\Microsoft Web Deploy V3\msdeploy.exe
if not exist "%msdeploypath%" (
	echo msdeploy.exe nao encontrado!
	exit 1
)

set Seven="C:\Program Files\7-Zip\7z.exe"
if not exist %Seven% (
	set Seven="C:\Program Files (x86)\7-Zip\7z.exe"
)
if not exist %Seven% (
	echo 7z.exe nao encontrado!
    exit 1
)

exit /b 0