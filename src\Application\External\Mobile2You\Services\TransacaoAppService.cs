using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.External.Mobile2You.Interface;
using SistemaInfo.BBC.Domain.External.Mobile2You.DTO;
using SistemaInfo.BBC.Domain.External.Mobile2You.Interface;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.External.Mobile2You.Services
{
    public class TransacaoAppService : ITransacaoAppService
    {
        private readonly ITransacaoRepository _transacaoRepository;

        public TransacaoAppService(ITransacaoRepository transacaoRepository)
        {
            _transacaoRepository = transacaoRepository;
        }

        public async Task P2P(P2PRequest request)
        {
            var log = LogManager.GetCurrentClassLogger();
            
            log.Info("--> Início da transação Mobile2You. pagamentoId: " + request.pagamentoId + " <--");
            
            var ret = await _transacaoRepository.P2P(request);

            if (!ret.message.IsNullOrWhiteSpace())
            {
                log.Error("Erro ao realizar a  transação, erro: " + ret.message);
            }
            else
            {
                var p2PResponse = JsonConvert.SerializeObject(ret);
                log.Info("Transação realizada com sucesso! " + p2PResponse);
            }   
            log.Info("--> Fim da transação Mobile2You <--");
        }

        public Task<List<CardResponse>> Card(string cnpj)
        {
            var log = LogManager.GetCurrentClassLogger();
            
            log.Info("--> Início da consulta do lote de cartões <--");

            var ret = _transacaoRepository.Card(cnpj);

            if (ret.Result == null)
            {
                log.Info(" Não existem cartões vinculados ao CNPJ informado. CNPJ: " + cnpj);
                log.Info("--> Fim da consulta do lote de cartões <--");
                return ret;
            }
            
            var cardResponse = JsonConvert.SerializeObject(ret.Result);
            
            log.Info(" " + cardResponse);
            log.Info("--> Fim da consulta do lote de cartões <--");

            return ret;
        }
    }
}