using System;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Pagamento
{
    public class PagamentoViagemIntegrar
    {
        public string Id { get; set; }
        public int PortadorId { get; set; }
        public int FormaPagamento { get; set; }
        public int Tipo { get; set; }
        public string Valor { get; set; }
        public string Descricao { get; set; }
        
        public int? IdContaDestino { get; set; }
        public string CpfContaDestino { get; set; }
        public int? IdContaOrigem { get; set; }

        public decimal PercentualTransferencia { get; set; } = 100;
        public int? IdContaTransferencia { get; set; }
        public string CpfContaTransferencia { get; set; }
        public DateTime? DataPrevisaoPagamento { get; set; } = DateTime.Today;
        
        public int? EtapaErroIntegracao { get; set; }
        public int? IdPagamentoExterno { get; set; }
        
        public Status Status { get; set; }
        
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string <PERSON>co { get; set; }
        public string Tipo<PERSON>onta { get; set; }
    }
}