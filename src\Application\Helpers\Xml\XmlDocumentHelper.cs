﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using SistemaInfo.BBC.Application.Objects.Web.ProtocoloAbastecimento;
using SistemaInfo.Framework.Utils;
namespace SistemaInfo.BBC.Domain.Helper;

public static class XmlDocumentHelper
{
        public static XmlDocument CarregarDocumento(this string xml)
        {
            var lXml = new XmlDocument();
            lXml.LoadXml(Encoding.UTF8.GetString(Convert.FromBase64String(xml)));
            return lXml;
        }

        public static List<ItensXmlSap> CarregarItensSap(this XmlDocument pXml)
        {
            var itensXml = new List<ItensXmlSap>();

            XmlNodeList elemListItens = pXml.GetElementsByTagName("prod");
            var lImpostos = pXml.GetElementsByTagName("imposto");

            for (int i = 0; i < elemListItens.Count; i++)
            {
                var item = new ItensXmlSap();
                XmlNodeList elemListinside = elemListItens[i].ChildNodes;
                for (int j = 0; j < elemListinside.Count; j++)
                {
                    if (elemListinside[j].Name == "cProd")
                    {
                        item.CodigoProduto = elemListinside[j].InnerXml;
                    }

                    if (elemListinside[j].Name == "qCom")
                    {
                        item.Litros = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).ToDecimal();
                    }

                    if (elemListinside[j].Name == "xProd")
                    {
                        item.Descricao = elemListinside[j].InnerXml;
                    }

                    if (elemListinside[j].Name == "vProd")
                    {
                        
                        item.QtdTributo = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).ToDecimal();
                    }

                    if (elemListinside[j].Name == "vUnTrib")
                    {
                        item.ValorUnitarioTributo = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).ToDecimal();
                    }

                    if (elemListinside[j].Name == "vDesc")
                    {
                       item.ValorDesconto = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).ToDecimal();
                    }
                }

                var lImposto = lImpostos[i].ChildNodes;
                for (var k = 0; k < lImposto.Count; k++)
                {
                    if (lImposto[k].Name == "ICMS")
                    {
                        var lIcms00 = lImposto[k].ChildNodes;
                        for (var l = 0; l < lIcms00.Count; l++)
                        {
                            if (lIcms00[l].Name == "ICMS00")
                            {
                                var lIcms = lIcms00[l].ChildNodes;
                                for (var m = 0; m < lIcms.Count; m++)
                                {
                                    if (lIcms[m].Name == "vICMS")
                                    {
                                        item.ValorIcms = lIcms[m].InnerText;
                                    }
                                }
                            }
                        }
                    }
                }

                item.nItem = (i + 1);
                itensXml.Add(item);
            }

            return itensXml;
        }

        public static decimal CarregarTotalXml(this XmlDocument pXml)
        {
            var totalXml = new decimal();
            XmlNodeList elemListValor = pXml.GetElementsByTagName("ICMSTot");
            for (int i = 0; i < elemListValor.Count; i++)
            {
                XmlNodeList elemListinside = elemListValor[0].ChildNodes;
                for (int j = 0; j < elemListinside.Count; j++)
                {
                    if (elemListinside[j].Name == "vNF")
                    {
                        totalXml = ConverterXmlParaDecimaRegrado(elemListinside[j].InnerText).ToDecimal();
                    }
                }
            }
            return totalXml;
        }
        
        private static decimal ConverterXmlParaDecimaRegrado(string xmlValor)
        {
            return Convert.ToDecimal(Math.Truncate(xmlValor.Replace(".", ",").ToDecimal() * 1000) / 1000);
        }
        
        public static IEnumerable<ItemXmlNfe> GetItensNFe(this XmlDocument eXml)
        {
            var lItensXml = new List<ItemXmlNfe>();

            foreach (XmlNode itemNivelUm in eXml.ChildNodes)
            {
                foreach (XmlNode itemNivelDois in itemNivelUm.ChildNodes)
                {
                    foreach (XmlNode itemNivelTres in itemNivelDois.ChildNodes)
                    {
                        foreach (XmlNode itemNivelQuatro in itemNivelTres.ChildNodes)
                        {
                            if (itemNivelQuatro.Name == "det")
                            {
                                var itemXmlNfe = new ItemXmlNfe();

                                foreach (XmlNode itemNivelCinco in itemNivelQuatro.ChildNodes)
                                {
                                    if (itemNivelCinco.Name == "prod")
                                    {
                                        foreach (XmlNode itemNivelSeis in itemNivelCinco.ChildNodes)
                                        {
                                            if (itemNivelSeis.Name == "qCom")
                                            {
                                                itemXmlNfe.qCom = itemNivelSeis.InnerXml;
                                                itemXmlNfe.nQtd = itemNivelSeis.InnerXml;
                                            }

                                            if (itemNivelSeis.Name == "cProd")
                                                itemXmlNfe.cProd = itemNivelSeis.InnerXml;

                                            if (itemNivelSeis.Name == "xProd")
                                                itemXmlNfe.xProd = itemNivelSeis.InnerXml;

                                            if (itemNivelSeis.Name == "vUnCom")
                                                itemXmlNfe.vUnCom = itemNivelSeis.InnerXml;

                                            if (itemNivelSeis.Name == "vProd")
                                                itemXmlNfe.vProd = itemNivelSeis.InnerXml;
                                        }
                                    }
                                }
                                lItensXml.Add(itemXmlNfe);
                            }
                        }
                    }
                }
            }
            return lItensXml;
        }
        
}