namespace SistemaInfo.BBC.Application.Objects.Api.Viagem
{
    public class ViagemIntegrarWebhookResponse
    {
        public ViagemIntegrarWebhookResponse(bool sucesso, string mensagem)
        {
            this.sucesso = sucesso;
            this.mensagem = mensagem;
        }

        public ViagemIntegrarWebhookResponse()
        {
        }

        public bool sucesso { get; set; }
        public int viagemId { get; set; }
        public int? viagemExternoId { get; set; }
        public int? statusViagem { get; set; }
        public string mensagem { get; set; }
        public PagamentoViagemWebhoookResponse pagamento { get; set; }
        public CiotWebhookResponse ciot { get; set; }
    }
    
    public class PagamentoViagemWebhoookResponse
    {
        public int? pagamentoEventoId { get; set; }
        public decimal? valorParcela { get; set; }
        public decimal? valorMotorista { get; set; }
        public int? statusPagamento { get; set; }
        public int? pagamentoExternoId { get; set; }
        public string códTransacao { get; set; }
        public int? formaPagamento { get; set; }
        public string mensagem { get; set; }
    }
    
    public class CiotWebhookResponse
    {
       
    }
}