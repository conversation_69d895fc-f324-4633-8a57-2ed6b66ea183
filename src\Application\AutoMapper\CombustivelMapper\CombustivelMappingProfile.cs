﻿using SistemaInfo.BBC.Application.Objects.Web.Combustivel;
using SistemaInfo.BBC.Domain.Models.Combustivel;
using SistemaInfo.BBC.Domain.Models.Combustivel.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel;

namespace SistemaInfo.BBC.Application.AutoMapper.CombustivelMapper
{
    public class CombustivelMappingProfile : SistemaInfoMappingProfile
    {
        public CombustivelMappingProfile()
        {
            CreateMap<Combustivel, CombustivelResponse>();

            CreateMap<Combustivel, ConsultarGridCombustivel>();

            CreateMap<CombustivelRequest, CombustivelSalvarCommand>();
            
            CreateMap<CombustivelRequest, CombustivelAlterarStatusCommand>();

            CreateMap<CombustivelRequest, CombustivelSalvarComRetornoCommand>();

            CreateMap<CombustivelSalvarCommand, Combustivel>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<CombustivelSalvarComRetornoCommand, Combustivel>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<CombustivelStatusRequest, CombustivelAlterarStatusCommand>();
            
            CreateMap<PostoCombustivel, ConsultarGridCombustivel>()
                .ForMember(a => a.ValorBbc, opts => opts.MapFrom(d => d.ValorCombustivelBBC))
                .ForMember(a => a.ValorBomba, opts => opts.MapFrom(d => d.ValorCombustivelBomba));
        }
    }
}