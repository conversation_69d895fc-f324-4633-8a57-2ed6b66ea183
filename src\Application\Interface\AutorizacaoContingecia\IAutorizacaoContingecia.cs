using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AutorizacaoContingecia;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.AutorizacaoContingecia
{
    public interface IAutorizacaoContingeciaAppService : IAppService<Domain.Models.Abastecimento.Abastecimento, IAbastecimentoReadRepository, IAbastecimentoWriteRepository>
    {
        ConsultarGridHistoricoAutorizacaoContingeciaResponse ConsultarGridHistoricoSolicitacoesPendentes(int IdPosto, DateTime dtInicial, DateTime dtFinal, int status, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> SaveAprovacao(AutorizacaoContingeciaRequest lAutorizacaoContingeciaReq, bool integracaoJaFeita = false);
        List<AutorizacaoContingeciaResponse> SolicitacoesPendentes(int idPosto, DateTime startDate, DateTime endDate);
    }
}