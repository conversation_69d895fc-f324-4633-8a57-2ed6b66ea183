﻿using System.Text;
using SistemaInfo.BBC.Application.Objects.Web.GrupoEmpresa;
using SistemaInfo.BBC.Application.Objects.Web.Saldo;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.GrupoEmpresaMapper
{
    public class GrupoEmpresaMappingProfile : SistemaInfoMappingProfile
    {
        public GrupoEmpresaMappingProfile()
        {
            CreateMap<GrupoEmpresa, ConsultarGrupoEmpresaResponse>()
                .ForMember(dest => dest.statusReprocessamentoPagamentoFrete, opts => opts.MapFrom(s => s.StatusReprocessamentoPagamentoFrete == 1))
                .ForMember(dest => dest.habilitaReprocessamentoValePedagio, opts => opts.MapFrom(s => s.HabilitaReprocessamentoValePedagio == 1))
                .ForMember(dest => dest.cobrancaTarifa, opts => opts.MapFrom(s => s.CobrancaTarifa == 1))
                .ForMember(dest => dest.cobrarTarifaBbcValePedagio, opts => opts.MapFrom(s => s.CobrarTarifaBbcValePedagio == 1));
            
            CreateMap<GrupoEmpresa, GrupoEmpresaAdicionarCommand>();
            
            CreateMap<GrupoEmpresa, ConsultarGrupoEmpresaClientSecretResponse>()
                .ForMember(dest => dest.GrupoEmpresaId, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.GrupoEmpresaNome, opts => opts.MapFrom(s => s.RazaoSocial));

            CreateMap<GrupoEmpresa, ConsultarGrupoEmpresaGrid>()
                .ForMember(dest => dest.cnpj, opts => opts.MapFrom(s => s.Cnpj.ToCNPJFormato()));
            
            CreateMap<GrupoEmpresa, GrupoEmpresaAdicionarCommand>();

           

            CreateMap<GrupoEmpresa, GrupoEmpresaEditarCommand>()
                .ForMember(dest => dest.StatusReprocessamentoPagamentoFrete, 
                    opts => opts.MapFrom(s => s.StatusReprocessamentoPagamentoFrete == 1));

            CreateMap<GrupoEmpresaRequest, GrupoEmpresaEditarCommand>()
                .ForMember(dest => dest.CobrancaTarifa, opts => opts.MapFrom(s => s.cobrancaTarifa ? 1 : 0))
                .ForMember(dest => dest.CobrarTarifaBbcValePedagio, opts => opts.MapFrom(s => s.cobrarTarifaBbcValePedagio ? 1 : 0))
                .ForMember(dest => dest.StatusReprocessamentoPagamentoFrete, opts => opts.MapFrom(s => s.statusReprocessamentoPagamentoFrete ? 1 : 0))
                .ForMember(dest => dest.HabilitaPainelSaldo, opts => opts.MapFrom(s => s.habilitaPainelSaldo ? 1 : 0))
                .ForMember(dest => dest.ValorAdiantamentoBbc, opts => opts.MapFrom(s => s.valorAdiantamentoBbc))
                .ForMember(dest => dest.ImagemCartao, opts => opts.MapFrom(s => Encoding.ASCII.GetBytes(s.imagemCartao)))
                .ForMember(dest => dest.HabilitaReprocessamentoValePedagio, opts => opts.MapFrom(s => s.habilitaReprocessamentoValePedagio ? 1 : 0));
            
            CreateMap<GrupoEmpresaRequest, GrupoEmpresaAdicionarCommand>()
                .ForMember(dest => dest.CobrancaTarifa, opts => opts.MapFrom(s => s.cobrancaTarifa ? 1 : 0))
                .ForMember(dest => dest.CobrarTarifaBbcValePedagio, opts => opts.MapFrom(s => s.cobrarTarifaBbcValePedagio ? 1 : 0))
                .ForMember(dest => dest.StatusReprocessamentoPagamentoFrete, opts => opts.MapFrom(s => s.statusReprocessamentoPagamentoFrete ? 1 : 0))
                .ForMember(dest => dest.HabilitaPainelSaldo, opts => opts.MapFrom(s => s.habilitaPainelSaldo ? 1 : 0))
                .ForMember(dest => dest.ValorAdiantamentoBbc, opts => opts.MapFrom(s => s.valorAdiantamentoBbc))
                .ForMember(dest => dest.ImagemCartao, opts => opts.MapFrom(s => Encoding.ASCII.GetBytes(s.imagemCartao)))
                .ForMember(dest => dest.HabilitaReprocessamentoValePedagio, opts => opts.MapFrom(s => s.habilitaReprocessamentoValePedagio ? 1 : 0));
            
            CreateMap<GrupoEmpresa, GrupoEmpresaAdicionarCommand>()
                .ForMember(dest => dest.StatusReprocessamentoPagamentoFrete, 
                    opts => opts.MapFrom(s => s.StatusReprocessamentoPagamentoFrete == 1));

            CreateMap<GrupoEmpresaAdicionarCommand, GrupoEmpresa>();
            
            CreateMap<GrupoEmpresaEditarCommand, GrupoEmpresa>();
        }
    }
}