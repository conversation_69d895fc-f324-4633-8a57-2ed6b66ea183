using SistemaInfo.BBC.Application.Objects.Web.AuthClientSecret;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret.Commands;
using SistemaInfo.BBC.Domain.Models.ClientSecretAdm.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.AuthClientSecretMapper
{
    public class AuthClientSecretMappingProfile : SistemaInfoMappingProfile
    {
        public AuthClientSecretMappingProfile()
        {
            CreateMap<ConsultarAuthClientSecretResponse, AuthClientSecretEditarCommand>();
            
            CreateMap<AuthClientSecretAlterarStatusRequest, AuthClientSecretAlterarStatusCommand>();
            
            CreateMap<AuthClientSecretEditarCommand, AuthClientSecret>();
            
            CreateMap<AuthClientSecret, AuthClientSecretEditarCommand>();
            
            CreateMap<AuthClientSecret, AuthClientSecretAdicionarCommand>();
            
            CreateMap<AuthClientSecretAdicionarCommand, AuthClientSecret>();
            
            CreateMap<AuthClientSecretRequest, AuthClientSecretAdicionarCommand>();
            
            CreateMap<AuthClientSecretRequest, AuthClientSecretEditarCommand>();
            
            CreateMap<AuthClientSecret, ConsultarAuthClientSecretResponse>();
            
            CreateMap<AuthClientSecret, ConsultarAuthClientSecretGrid>()
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToString("dd/MM/yyyy HH:mm")))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.HasValue ? s.DataAlteracao.Value.ToString("dd/MM/yyyy HH:mm") : ""))
                .ForMember(dest => dest.UsuarioCadastro, opts => opts.MapFrom(s => s.Usuario != null ? s.Usuario.Nome : ""))
                .ForMember(dest => dest.UsuarioAlteracao, opts => opts.MapFrom(s => s.UsuarioAlteracao != null ? s.UsuarioAlteracao.Nome : ""));
        }
    }
}
