﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.ModuloMenu;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.ModuloMenu.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.ModuloMenu
{
    public interface IModuloMenuAppService : IAppService<Domain.Models.ModuloMenu.ModuloMenu, IModuloMenuReadRepository,
            IModuloMenuWriteRepository>
    {
        List<ConsultaModuloMenuResponse> ConsultaModuloMenuApi();
        Task<RespPadraoApi> IntegrarModuloMenuApi(ModuloMenuIntegrarApiRequest request);
        
    }
}