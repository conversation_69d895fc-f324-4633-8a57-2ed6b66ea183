﻿using System.Text;
using SistemaInfo.BBC.Application.Objects.Web.Ciot;
using SistemaInfo.BBC.Application.Objects.Web.Documentos;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Application.Objects.Web.EmpresaCfop;
using SistemaInfo.BBC.Application.Objects.Web.GrupoEmpresa;
using SistemaInfo.BBC.Application.Objects.Web.Saldo;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Documento;
using SistemaInfo.BBC.Domain.Models.Empresa;
using SistemaInfo.BBC.Domain.Models.Empresa.Commands;
using SistemaInfo.BBC.Domain.Models.Empresa.Commands.Base;
using SistemaInfo.BBC.Domain.Models.EmpresaCfop;
using SistemaInfo.BBC.Domain.Models.EmpresaCfop.Commands;
using SistemaInfo.BBC.Domain.Models.PortadorRepresentanteLegal;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.EmpresaMapper
{
    public class EmpresaMappingProfile : SistemaInfoMappingProfile
    {
        public EmpresaMappingProfile()
        {
            CreateMap<PortadorRepresentanteLegal, ConsultarPorIdEmpresaResponse.PortadorRepLegalEmpResponse>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.PortadorRepresentanteId))
                .ForMember(dest => dest.Nome, opts => opts.MapFrom(s => s.PortadorRepresentante.Nome))
                .ForMember(dest => dest.CpfCnpj, opts => opts.MapFrom(s => s.PortadorRepresentante.CpfCnpj.ToCpfOrCnpj()));

            CreateMap<Empresa, ConsultarEmpresaCombo>()
                .ForMember(dest => dest.GrupoDeEmpresaId, opts => opts.MapFrom(s => s.GrupoEmpresa.Id))
                .ForMember(dest => dest.Cnpj, opts => opts.MapFrom(s => s.Cnpj.ToCNPJFormato()))
                .ForMember(dest => dest.GrupoDeEmpresa, opts => opts.MapFrom(s => s.GrupoEmpresa.RazaoSocial));
            
            CreateMap<Empresa, EmpresaRequest>();
            
            CreateMap<Empresa, ConsultarGrupoEmpresaClientSecretResponse>()
                .ForMember(dest => dest.EmpresaId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.EmpresaNome, opt => opt.MapFrom(src => src.RazaoSocial))
                .ForMember(dest => dest.GrupoEmpresaId, opt => opt.MapFrom(src => src.GrupoEmpresaId))
                .ForMember(dest => dest.GrupoEmpresaNome, opt => opt.MapFrom(src => src.GrupoEmpresa.RazaoSocial));

            CreateMap<Empresa, ConsultarPorIdEmpresaResponse>()
                .ForMember(dest => dest.CidadeId, opt => opt.MapFrom(src => src.CidadeId))
                .ForMember(dest => dest.EstadoId, opt => opt.MapFrom(src => src.Cidade.EstadoId))
                .ForMember(dest => dest.GrupoEmpresaNome, opt => opt.MapFrom(src => src.GrupoEmpresa.RazaoSocial))
                .ForMember(dest => dest.LiberaBloqueioSPD, opts => opts.MapFrom(s => s.LiberaBloqueioSPD == 1))
                .ForMember(dest => dest.CobrancaTarifa, opts => opts.MapFrom(s => s.CobrancaTarifa == 1))
                .ForMember(dest => dest.ControlaContingencia, opts => opts.MapFrom(s => s.ControlaContingencia == 1))
                .ForMember(dest => dest.DebitoProtocolo, opts => opts.MapFrom(s => s.DebitoProtocolo == 1))
                .ForMember(dest => dest.DebitoPrazo, opts => opts.MapFrom(s => s.DebitoPrazo == 1))
                .ForMember(dest => dest.UtilizaTarifaEmpresa, opts => opts.MapFrom(s => s.UtilizaTarifaEmpresa == 1))
                .ForMember(dest => dest.RecebedorAutorizado, opts => opts.MapFrom(s => s.RecebedorAutorizado))
                .ForMember(dest => dest.PermitirPagamentoValePedagio, opts => opts.MapFrom(s => s.PermitirPagamentoValePedagio == 1))
                .ForMember(dest => dest.PorcentagemTarifaServiceValePedagio, opts => opts.MapFrom(s => s.PorcentagemTarifaServiceValePedagio))
                .ForMember(dest => dest.CobrarTarifaBbcValePedagio, opts => opts.MapFrom(s => s.CobrarTarifaBbcValePedagio == 1))
                .ForMember(dest => dest.UtilizaTarifaEmpresaPagamentoPedagio, opts => opts.MapFrom(s => s.UtilizaTarifaEmpresaPagamentoPedagio == 1))
                .ForMember(dest => dest.NotificacaoContingenciaCiot, opts => opts.MapFrom(s => s.NotificacaoContingenciaCiot))
                .ForMember(dest => dest.UtilizaTarifaEmpresaPagamentoPedagio, opts => opts.MapFrom(s => s.UtilizaTarifaEmpresaPagamentoPedagio == 1))
                .ForMember(dest => dest.StatusReprocessamentoPagamentoFrete, opts => opts.MapFrom(s => s.StatusReprocessamentoPagamentoFrete == 1))
                .ForMember(dest => dest.HabilitaReprocessamentoValePedagio, opts => opts.MapFrom(s => s.HabilitaReprocessamentoValePedagio == 1))
                .ForMember(dest => dest.HabilitaPainelSaldo, opts => opts.MapFrom(s => s.HabilitaPainelSaldo == 1))
                .ForMember(dest => dest.ValorAdiantamentoBbc, opts => opts.MapFrom(s => s.ValorAdiantamentoBbc))
                .ForMember(dest => dest.ImagemCartao, opts => opts.MapFrom(s => s.ImagemCartao != null ? System.Text.Encoding.Default.GetString(s.ImagemCartao): ""))
                .ForMember(dest => dest.HabilitaReprocessamentoValePedagio, opts => opts.MapFrom(s => s.HabilitaReprocessamentoValePedagio == 1))
                .ForMember(dest => dest.PermitirEncerramentoPainelCiot, opts => opts.MapFrom(s => s.PermitirEncerramentoPainelCiot == 1))
                .ForMember(dest => dest.UtilizaCiot, opts => opts.MapFrom(s => s.UtilizaCiot == 1));

            
            CreateMap<Empresa, EmpresaHabilitadosResponse>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.Nome, opts => opts.MapFrom(s => s.RazaoSocial))
                .ForMember(dest => dest.Base64Img, opts => opts.MapFrom(s => s.ImagemCartao != null ? System.Text.Encoding.Default.GetString(s.ImagemCartao): ""));

            CreateMap<Documento, ConsultarGridDocumentosEmpresaItem>()
                .ForMember(dest => dest.Arquivo, opt => opt.MapFrom(src => src.Foto))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.GetHashCode()))
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.Tipo.GetHashCode()));
            
            
            CreateMap<EmpresaCfop, EmpresaCfopResp>()
                .ForMember(dest => dest.Cfop, opt => opt.MapFrom(src => src.CFOP.Cfop))
                .ForMember(dest => dest.Descricao, opt => opt.MapFrom(src => src.CFOP.Descricao));

            CreateMap<CiotClienteIpResponse, CiotClienteIpResponseAdaptado>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ClienteIpId.ToInt()))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status == "A" ? 1 : 0));

            CreateMap<Empresa, ConsultarPorIdClientSecretResponse>();
                
            
            CreateMap<EmpresaStatusRequest, EmpresaSaveCommand>();

            CreateMap<EmpresaRequest, EmpresaSaveCommand>()
                .ForMember(dest => dest.LiberaBloqueioSPD, opts => opts.MapFrom(s => s.LiberaBloqueioSPD ? 1 : 0))
                .ForMember(dest => dest.CobrancaTarifa, opts => opts.MapFrom(s => s.CobrancaTarifa ? 1 : 0))
                .ForMember(dest => dest.ControlaOdometro, opts => opts.MapFrom(s => s.ControlaOdometro ? 1 : 0))
                .ForMember(dest => dest.RegistraCiot, opts => opts.MapFrom(s => s.RegistraCiot ? 1 : 0))
                .ForMember(dest => dest.ControlaContingencia, opts => opts.MapFrom(s => s.ControlaContingencia ? 1 : 0))
                .ForMember(dest => dest.UtilizaTarifaEmpresa, opts => opts.MapFrom(s => s.UtilizaTarifaEmpresa ? 1 : 0))
                .ForMember(dest => dest.PermitirPagamentoValePedagio, opts => opts.MapFrom(s => s.PermitirPagamentoValePedagio ? 1 : 0))
                .ForMember(dest => dest.CobrarTarifaBbcValePedagio, opts => opts.MapFrom(s => s.CobrarTarifaBbcValePedagio ? 1 : 0))
                .ForMember(dest => dest.UtilizaTarifaEmpresaPagamentoPedagio, opts => opts.MapFrom(s => s.UtilizaTarifaEmpresaPagamentoPedagio ? 1 : 0))
                .ForMember(dest => dest.NotificacaoContingenciaCiot, opts => opts.MapFrom(s => s.NotificacaoContingenciaCiot))
                .ForMember(dest => dest.UtilizaTarifaEmpresaPagamentoPedagio, opts => opts.MapFrom(s => s.UtilizaTarifaEmpresaPagamentoPedagio ? 1 : 0))
                .ForMember(dest => dest.StatusReprocessamentoPagamentoFrete, opts => opts.MapFrom(s => s.StatusReprocessamentoPagamentoFrete ? 1 : 0))
                .ForMember(dest => dest.HabilitaReprocessamentoValePedagio, opts => opts.MapFrom(s => s.HabilitaReprocessamentoValePedagio ? 1 : 0))
                .ForMember(dest => dest.ValorAdiantamentoBbc, opts => opts.MapFrom(s => s.ValorAdiantamentoBbc))
                .ForMember(dest => dest.ImagemCartao, opts => opts.MapFrom(s => Encoding.ASCII.GetBytes(s.ImagemCartao)))
                .ForMember(dest => dest.HabilitaPainelSaldo, opts => opts.MapFrom(s => s.HabilitaPainelSaldo ? 1 : 0))
                .ForMember(dest => dest.HabilitaReprocessamentoValePedagio, opts => opts.MapFrom(s => s.HabilitaReprocessamentoValePedagio ? 1 : 0))
                .ForMember(dest => dest.PermitirEncerramentoPainelCiot, opts => opts.MapFrom(s => s.PermitirEncerramentoPainelCiot ? 1 : 0))
                .ForMember(dest => dest.UtilizaCiot, opts => opts.MapFrom(s => s.UtilizaCiot ? 1 : 0));

            CreateMap<EmpresaSaveCommand, Empresa>();

            CreateMap<EmpresaSaveStatusCommand, Empresa>();
            
            CreateMap<EmpresaSaveComRetornoCommand, Empresa>();

            CreateMap<EmpresaCommandBase, Empresa>();

            CreateMap<CiotClienteIpRequest, CiotClienteIpRequestAdaptado>()
                .ForMember(d => d.Status, opts => opts.MapFrom(s => s.Status == 1 ? "A" : "B"))
                .ForMember(d => d.ClienteIpId, opts => opts.MapFrom(s => s.Id));
            
            CreateMap<EmpresaCfopRequest, EmpresaCfopSalvarComRetornoCommand>();
            
            CreateMap<EmpresaCfopSalvarComRetornoCommand, EmpresaCfop>();
        }
    }
}