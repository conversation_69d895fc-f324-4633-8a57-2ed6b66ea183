﻿using System;
using System.Globalization;
using SistemaInfo.BBC.Application.Objects.Api.Pagamento;
using SistemaInfo.BBC.Application.Objects.External.Conductor;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;

namespace SistemaInfo.BBC.Application.AutoMapper.TransferenciaMapper
{
    public class TransferenciaMappingProfile : SistemaInfoMappingProfile
    {
        public TransferenciaMappingProfile()
        {
            CreateMap<TransferenciaRequest, Transferencia>();

            CreateMap<PagamentoIntegrarRequest, Transferencia>()
                .ForMember(dest => dest.amount, opt => opt.MapFrom(src => Decimal.Parse(src.Valor, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.description, opt => opt.MapFrom(src => src.Descricao))
                .ForMember(dest => dest.originalAccount, opt => opt.MapFrom(src => src.IdContaOrigem ?? 0))
                .ForMember(dest => dest.destinationAccount, opt => opt.MapFrom(src => src.IdContaDestino ?? 0));
            
            CreateMap<TransferenciaP2PRequest, Transferencia>()
                .ForMember(dest => dest.amount, opt => opt.MapFrom(src => Decimal.Parse(src.Valor, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.description, opt => opt.MapFrom(src => "TransferenciaP2P BBC API"))
                .ForMember(dest => dest.originalAccount, opt => opt.MapFrom(src => src.IdContaOrigem ))
                .ForMember(dest => dest.destinationAccount, opt => opt.MapFrom(src => src.IdContaDestino ));

        }
    }
}