﻿using SistemaInfo.BBC.Application.Objects.Web.Cidade;
using SistemaInfo.BBC.Application.Objects.Web.Endereco;
using SistemaInfo.BBC.Domain.Models.Cidade;
using SistemaInfo.BBC.Domain.Models.Estado;

namespace SistemaInfo.BBC.Application.AutoMapper.EstadoMapper
{
    public class EstadoCidadeMappingProfile : SistemaInfoMappingProfile
    {
        public EstadoCidadeMappingProfile()
        {
            CreateMap<Estado, ConsultarEstadosResponse>()
                .ForMember(d => d.descricao, opts => opts.MapFrom(s => s.Nome))
                .ForMember(d => d.sigla, opts => opts.MapFrom(s => s.Uf))
                .ForMember(d => d.id, opts => opts.MapFrom(s => s.Id));
            
            CreateMap<Cidade, ConsultarCidadesResponse>()
                .ForMember(d => d.descricao, opts => opts.MapFrom(s => s.Nome))
                .ForMember(d => d.id, opts => opts.MapFrom(s => s.Id));

            CreateMap<Cidade, ConsultarGridCidade>()
                .ForMember(d => d.NomeEstado, opts => opts.MapFrom(s => s.Estado.Nome))
                .ForMember(d => d.Ibge, opts => opts.MapFrom(s => s.Ibge));
        }
    }
}