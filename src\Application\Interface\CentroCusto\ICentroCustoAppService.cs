using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentroCusto;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.CentroCusto
{
    public interface ICentroCustoAppService : IAppService<Domain.Models.CentroCusto.CentroCusto, ICentroCustoReadRepository, ICentroCustoWriteRepository>
    {
        ConsultarGridCentroCustoResponse ConsultarGridCentroCusto(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        CentroCustoResponse ConsultarPorId(int idCentroCusto);
        Task<RespPadrao> Save(CentroCustoRequest lCentroCustoReq, bool integracao = false);
        Task AlterarStatus(CentroCustoStatusRequest lCentroCustoStatus);
        Task<RespPadrao> AlterarVinculoFilial(CentroCustoAlterarVinculoFilialRequest lCentroCustoAlterarVinculo);

    }
}