﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ServidorCiot;
using SistemaInfo.BBC.Application.Services.MonitoramentoCiot;
using SistemaInfo.BBC.Domain.Contracts.Servidor;
using SistemaInfo.BBC.Domain.Grid;

namespace SistemaInfo.BBC.Application.Interface.MonitoramentoCiot;

public interface IMonitoramentoCiotAppService
{
    Task<List<ConsultarServidorCiotGrid>> VerificaContigencia(List<ConsultarServidorCiotGrid> servidores);
    Task<RespPadrao> AtivarDesativarContigencia(DadosServidorGrid dadosDadosServidor);
    Task<RespPadrao> ConsultarGridServidoresCiot(int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters);
    Task<RespPadrao> SaveServidorCiot(CadastrarServidorCiotRequest aModel);
    Task<RespPadrao> AtivarDesativarServidor(DadosServidorGrid dadosDadosServidor);
    Task SincronizarServidorComBbc(ServidorSincronizarComBbcMessage message);
    Task<RespPadrao> ConsultarGridServidoresCiotHistorico(int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters);
    List<ConsultarServidorCiotHistoricoResponse> ConsultarServidoresHistorico();

}