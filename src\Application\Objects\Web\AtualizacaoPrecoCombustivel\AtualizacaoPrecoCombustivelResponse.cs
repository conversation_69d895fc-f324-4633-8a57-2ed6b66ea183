using SistemaInfo.BBC.Application.Objects.Web.Usuario;

namespace SistemaInfo.BBC.Application.Objects.Web.AtualizacaoPrecoCombustivel
{
    public class AtualizacaoPrecoCombustivelResponse
    {
        public int Id { get; set; }

        public int CombustivelId { get; set; }
        public string NomeCombustivel { get; set; }
        public int PostoId { get; set; }
        public string NomePosto { get; set; }
        public decimal? Valor { get; set; }
        
        public decimal? ValorBBC { get; set; }
        public decimal? ValorBomba { get; set; }
        public decimal? ValorBombaSolicitado { get; set; }
        public decimal? ValorBBCSolicitado { get; set; }

        
        public int? StatusAprovacao { get; set; }
        public string StatusAprovacaoDescricao { get; set; }
        public string MotivoInterno { get; set; }
        public string MotivoExterno { get; set; }
        public string MotivoSolicitacao { get; set; }
        public decimal? DiferencaEntreValores { get; set; }

        public string DataCadastro { get; set; }
        public int? UsuarioAlteracaoId { get; set; }
        public string DataAlteracao { get; set; }
        
        public PostoAtualizacaoPrecoCombustivelResponse Posto { get; set; }
        public CombustivelAtualizacaoPrecoCombustivelResponse Combustivel { get; set; }
        public ConsultarUsuarioResponse UsuarioAlteracao { get; set; }
    }
    
    public class PostoAtualizacaoPrecoCombustivelResponse
    {
        public string RazaoSocial { get; set; }
    }
    
    public class CombustivelAtualizacaoPrecoCombustivelResponse
    {
        public string Nome { get; set; }
    }
  
}