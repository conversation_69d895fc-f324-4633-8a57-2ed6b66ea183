using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AuditoriaSeguranca;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.AuditoriaSeguranca
{
    public interface IAuditoriaSegurancaAppService : IAppService<Domain.Models.AuditoriaSeguranca.AuditoriaSeguranca, IAuditoriaSegurancaReadRepository,
        IAuditoriaSegurancaWriteRepository>
    {
        ConsultarGridAuditoriaSegurancaResponse ConsultarGridAuditoriaSeguranca(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);

        Task<RespPadrao> Save(AuditoriaSegurancaRequest lAuditoriaSegurancaReq, bool integracao = false);
    }
}