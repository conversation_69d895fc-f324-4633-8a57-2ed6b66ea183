using System.Threading.Tasks;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;

namespace SistemaInfo.BBC.Domain.Models.AuthClientSecret.Repository
{
    
    public interface IAuthClientSecretBaseReadRepository<TAuthClientSecretEntity> : IReadOnlyRepository<TAuthClientSecretEntity>
        where TAuthClientSecretEntity : AuthClientSecret
    {
    }
    
    public interface IAuthClientSecretReadRepository : IReadOnlyRepository<AuthClientSecret>
    {
        Task<bool> AnyAtivoAsync(string login);
        Task<AuthClientSecret> ObterPorLoginAsync(string login);
    }
}
