﻿using System;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Pedagio;

public class IntegrarPagamentoPedagioResponse : RespPadrao
{
    public IntegrarPagamentoPedagioResponse(){}
    public IntegrarPagamentoPedagioResponse(bool aSucesso, string aMensagem, IntegrarPagamentoPedagioResponseData aData = null)
    {
        sucesso = aSucesso;
        mensagem = aMensagem?.Length > 250 ? aMensagem.Substring(0, 250) : aMensagem;
        data = aData;
    }
    
    public new IntegrarPagamentoPedagioResponseData data { get; set; }
}

public class IntegrarPagamentoPedagioResponseData
{
    public int PagamentoPedagioId { get; set; }
    public decimal ValorTarifa { get; set; }
    public decimal Valor { get; set; }
    public int PagamentoTarifa { get; set; }
    public EStatusPagamentoPedagio Status { get; set; }
    public DateTime? DataBaixa { get; set; }
}