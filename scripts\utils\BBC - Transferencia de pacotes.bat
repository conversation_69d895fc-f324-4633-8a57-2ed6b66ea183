@echo off
::Confihuração de variaveis
set cliente=BBC
set pasta-destino=C:\sites
set backup-path=%pasta-destino%\Backup

set iis=false
set log=true
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
::Não alterar
setlocal enabledelayedexpansion
call :leiame
echo. 
cd "%~dp0"
set pastaRaiz=%CD%
set "Data=%DATE:/=-% %time:~0,2%-%time:~3,2%"
set "arquivo=%pastaRaiz%\log-transferencia %Data%.txt"
set "arquivo-info=%pastaRaiz%\log-info %Data%.txt"
if %log%==true (echo Date; ErrorType; Target; ErrorLevel; Message >> "%arquivo%")

call :info Processo inicializado!
echo.
set "sites-path=%pasta-destino%"
set "sites-path-backup=%backup-path%"
set ambiente=false
set dev=false
set hml=false
set prod=false
if exist "%pasta-destino%/%cliente%/DEV" (
	set ambiente=true
	set dev=true
)
if exist "%pasta-destino%/%cliente%/HML" (
	set ambiente=true
	set hml=true
)
if exist "%pasta-destino%/%cliente%/PROD" (
	set ambiente=true
	set prod=true
)
:input
if %ambiente%==true (
	call :options
)

call :info Destino: %sites-path%
set Seven="C:\Program Files (x86)\7-Zip\7z.exe"
if not exist %Seven% (
	set Seven="C:\Program Files\7-Zip\7z.exe"
)
if not exist %Seven% (
	call :log Erro; 7z; %errorlevel%; Arquivo exe nao encontrado! %Seven%
	goto :error
)
if not exist %sites-path% (
	call :log Erro; sites-path; %errorlevel%; Caminho da pasta sites nao existente ou nao configurado!
	goto error
)

set sites-backup=false
for /d %%i in (*) do (
	echo.
	if exist "%sites-path%\%%i" (
		call :info Iniciando processo de Backup de %%i
		call %Seven% a -y -tzip "%sites-path-backup%\%%i\BKP %%i %Data%.zip" "%sites-path%\%%i"
		if %errorlevel% neq 0 (
		call :log Erro; %%i; %errorlevel%; Erro ao realizar o backup! BKP %%i %Data%.zip
		goto error
		)
		if not exist "%sites-path-backup%\%%i\BKP %%i %Data%.zip" (
		call :log Erro; %%i; %errorlevel%; Backup nao gerado! BKP %%i %Data%.zip
		goto error
		)
		call :log Sucesso; %%i; %errorlevel%; Backup OK
		if not exist "Transferencia" mkdir "Transferencia"
		xcopy "%%i" "Transferencia/%%i" /E /I /H /Y
		%errorlevel%
		if %errorlevel% neq 0 (
			call :log Erro; %%i; %errorlevel%; Erro ao copiar arquivos de "%%i" para "%pastaRaiz%/Transferencia/%%i"
			goto error
		)
		
		call :log Sucesso; %%i; %errorlevel%; Arquivos copiados de "%%i" para "%pastaRaiz%/Transferencia/%%i"
		rmdir /s /q "%%i"
		call :info Transferencia de %%i realizada com sucesso!
		set sites-backup=true
	)
)
if %sites-backup%==false (
	if not exist "%pastaRaiz%/Transferencia" (
		call :log Info; Backup; %errorlevel%; Sem sites para realizar backup.
		goto success
	)
)

if %iis%==true call :info O IIS sera pausado para prosseguir a transferencia.
call :info Deseja iniciar a transferencia?
call :opt S - Sim
call :opt N - Nao
set /p permissao-transferencia=""
call :log Info; Permissao transferencia; %errorlevel%; Valor selecionado %permissao-transferencia%
if %permissao-transferencia%==S (
	call :info Permissao liberada.
	if %iis%==true (
		if not exist "C:\Windows\System32\iisreset.exe" (
			call :log Erro; IIS; %errorlevel%; IIS nao encontrado!
			goto error
		)
		echo %DATE:/=-% %time:~0,8%: Gravando status IIS antes da pausa >> "%arquivo-info%"
		sc query W3SVC >> "%arquivo-info%"
		echo. >> "%arquivo-info%"
		%windir%\system32\inetsrv\appcmd list site >> "%arquivo-info%"
		
		call :info Parando IIS
		echo %errorlevel%
		if %errorlevel% neq 0 (
			call :log Erro; IIS; %errorlevel%; Erro ao parar o IIS
			goto error
		)
	)

	if exist "%pastaRaiz%/Transferencia" (
		cd "%pastaRaiz%/Transferencia"
		for /d %%i in (*) do (
			echo.
			if exist "%sites-path%\%%i" (
				call :info Iniciando processo de transferencia de %%i
				pause
				xcopy "%%i" "%sites-path%/%%i" /E /I /H /Y
				if %errorlevel% neq 0 (
					call :log Erro; %%i; %errorlevel%; Erro ao copiar arquivos de "%%i" para "%sites-path%\%%i"
					goto error
				)
				call :log Sucesso; %%i; %errorlevel%; Arquivos copiados de "%%i" para "%sites-path%\%%i"
				
				if exist "%sites-path%\%%i\scripts" (
					echo existe "%sites-path%\%%i\scripts"
					call :deleta-arquivos "%sites-path%\%%i\scripts" "*.js"
					if %errorlevel% neq 0 (
						call :log Info; %%i; %errorlevel%; Nao foi possível remover os scripts antigos "%sites-path%\%%i"
					)
					call :log Sucesso; %%i; %errorlevel%; Arquivos scripts antigos removidos com sucesso!
				)
				if exist "%sites-path%\%%i\styles" (
					echo existe "%sites-path%\%%i\styles"
					call :deleta-arquivos "%sites-path%\%%i\styles" "*.css"
					if %errorlevel% neq 0 (
						call :log Info; %%i; %errorlevel%; Nao foi possível remover os styles antigos "%sites-path%\%%i"
					)
					call :log Sucesso; %%i; %errorlevel%; Arquivos styles antigos removidos com sucesso!
				)
				echo renomeando "%%i" para "%%i (Transferencia realizada)"
				ren "%%i" "%%i (Transferencia realizada)"
				if %errorlevel% neq 0 (
					call :log Erro; %%i; %errorlevel%; Erro ao renomear a pasta origem "%%i"
					goto error
				)
				if not exist "%%i (Transferencia realizada)" (
					call :log Erro; %%i; %errorlevel%; Falha ao renomear pasta origem "%%i"
					goto error
				)
				call :log Sucesso; %%i; %errorlevel%; Pasta origem "%%i" renomeada com sucesso!
				call :info Transferencia de %%i realizada com sucesso!
			)
		)
	)
	if %iis%==true (
		call :info Iniciando IIS
		iisreset /start
		if %errorlevel% neq 0 (
			call :log Erro; IIS; %errorlevel%; Erro ao inicializar o IIS
			goto error
		)
		echo . >> "%arquivo-info%"
		echo %DATE:/=-% %time:~0,8%: Gravando status IIS depois da pausa >> "%arquivo-info%"
		sc query W3SVC >> "%arquivo-info%"
		echo. >> "%arquivo-info%"
		%windir%\system32\inetsrv\appcmd list site >> "%arquivo-info%"
	)
) else (
	call :info Permissao rejeitada.
)
goto success
exit 0

:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:options
	call :info Digite o ambiente de transferencia
	call :opt 0 - Padrao
	if %dev%==true (call :opt 1 - DEV)
	if %hml%==true (call :opt 2 - HML)
	if %prod%==true (call :opt 3 - PROD SIMULADO)
	
	echo.
	set /p option=""
	call :log Info; Ambiente de transferencia; %errorlevel%; Valor selecionado %option%
	echo Opcao selecionada: %option%
	if %option%==0 (exit /B 0)
	if %option%==1 (
		if %dev%==false (
			call :info Opção inexistente!
			goto input
		)
		set ambiente=DEV
		exit /B 0
	)
	if %option%==2 (
		if %hml%==false (
			call :info Opção inexistente!
			goto input
		)
		set ambiente=HML
	)
	if %option%==3 (
		if %prod%==false (
			call :info Opção inexistente!
			goto input
		)
		set ambiente=PROD

	) else (
		call :info Opção inexistente!
		goto input
	)
	set sites-path-backup=%backup-path%\%cliente%\%ambiente%
	set sites-path=%pasta-destino%\%cliente%\%ambiente%
	exit /B 0
	
:verifica-atualizacao
	SET source=%~1
	SET destination=%~2
	for /r "%source%" %%F in (*) do (
		if exist "%destination%\%%~nxF" (
			for %%A in ("%destination%\%%~nxF") do for %%B in ("%%F") do (
				if %%~tB GTR %%~tA (
					echo O arquivo %%~nxF está desatualizado. Copiando...
					xcopy "%%F" "%destination%" /Y
				) else (
					echo O arquivo %%~nxF já está atualizado.
				)
			)
		) else (
			echo O arquivo %%~nxF não existe no destino. Copiando...
			xcopy "%%F" "%destination%" /Y
		)
	)

:deleta-arquivos
	set folder=%~1
	set ext="%~2"
	cd "%folder%"
	for /f "delims=" %%a in ('dir /b /o-d %ext%') do (
		set /a count+=1
		if !count! gtr 2 del "%%a"
	)
	set /a count=0
	cd "%pastaRaiz%/Transferencia"
	exit /b 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0
	
:log
	call :setESC
	echo.
	echo !ESC![94m %* !!ESC![0m
	if %log%==true (echo %DATE:/=-% %time:~0,8%; %* >> "%arquivo%")
	exit /B 0

:info
	call :setESC
	echo.
	echo !ESC![105m ==========================================================================================!!ESC![0m
	echo !ESC![105m %* !!ESC![0m
	echo !ESC![105m ==========================================================================================!!ESC![0m
	exit /B 0

:leiame
	call :setESC
	echo.
	echo !ESC![115m ==========================================================================================!!ESC![0m
	echo !ESC![91m ATENCAO!!!ESC![0m !ESC![93m EXECUTE EM MODO ADMINISTRADOR.!!ESC![0m
	echo !ESC![93m O PROCESSO NECESSITA DE PERMISSOES ELEVADAS!!ESC![0m
	echo !ESC![93m A FALTA DE PERMISSAO PODE OCASIONAR FALSOS SUCESSOS NA OPERACAO!!ESC![0m
	echo !ESC![93m A PASTA DE BACKUP ESTA CONFIGURADO NO CAMINHO: %backup-path%!!ESC![0m
	echo !ESC![115m ==========================================================================================!!ESC![0m
	exit /B 0
	
:success
	call :setESC
	call :log Sucesso; %%i; %errorlevel%; Processo finalizado com Sucesso!
	echo.
	echo !ESC![92m ==========================================================================================!!ESC![0m
	echo !ESC![92m Processo finalizado com Sucesso!!!ESC![0m
	echo !ESC![92m ==========================================================================================!!ESC![0m
	echo - Verifique os backups gerados
	echo - Verifique se os sites estão atualizados
	echo - Verifique se não existe erros no log
	echo - Verifique se os sites estão funcionando corretamente
	pause
	exit 0
	
:error
	call :setESC
	call :log %cliente%; %errorlevel%; Erro; Processo finalizado com Erro!
	echo.
	echo !ESC![91m ==========================================================================================!!ESC![0m
	echo !ESC![91m Processo finalizado com Erro!!!ESC![0m
	echo !ESC![91m ==========================================================================================!!ESC![0m
	pause
	exit 1