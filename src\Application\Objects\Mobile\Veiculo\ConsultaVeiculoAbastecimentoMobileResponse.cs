﻿using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Mobile.Veiculo
{
    public class ConsultaVeiculoAbastecimentoMobileResponse
    {
        public int VeiculoId { get; set; }
        public string Placa { get; set; }
        public string Frota { get; set; }
        
        public List<CombustiveisListMobile> ListaCombustiveisApi{ get; set; }
        
    }
    public class CombustiveisListMobile
    {
        public int? CombustivelId { get; set; }
        public string CombustivelNome { get; set; }
        public string UnidadeMedida { get; set; }
        public decimal? ValorBomba { get; set; } = 0;
        public decimal? ValorBBC { get; set; } = 0;

    }
}