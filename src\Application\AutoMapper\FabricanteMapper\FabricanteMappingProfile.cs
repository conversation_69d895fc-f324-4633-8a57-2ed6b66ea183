﻿using SistemaInfo.BBC.Application.Objects.Web.Fabricante;
using SistemaInfo.BBC.Domain.Models.Fabricante;
using SistemaInfo.BBC.Domain.Models.Fabricante.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.FabricanteMapper
{
    public class FabricanteMappingProfile : SistemaInfoMappingProfile
    {
        public FabricanteMappingProfile()
        {
            CreateMap<FabricanteRequest, FabricanteSalvarCommand>();
            
            CreateMap<FabricanteRequest, FabricanteAlterarStatusCommand>();

            CreateMap<FabricanteRequest, FabricanteSalvarComRetornoCommand>();

            CreateMap<FabricanteSalvarCommand, Fabricante>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<FabricanteSalvarComRetornoCommand, Fabricante>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<FabricanteStatusRequest, FabricanteAlterarStatusCommand>();
        }
    }
}