using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Menu;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Menu;
using SistemaInfo.BBC.Domain.Models.Menu.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Menu
{
    public interface IMenuAppService : IAppService<Domain.Models.Menu.Menu, IMenuReadRepository,
            IMenuWriteRepository>
    {
        List<GrupoUsuarioMenuGridResponse> GetMenusDisponiveisPorModulo(int idModulo, int? idGrupoUsuario, int? idEmpresa, int? idPosto);
        List<ConsultaGridMenu> ConsultaMenuApi();
        Task<RespPadraoApi> IntegrarMenuApi(MenuIntegrarApiRequest request);
    }
}