﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.BBC.Application.Interface.Pagamentos;
using SistemaInfo.BBC.Application.Interface.Viagem;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("ServicosPagamento")]
    public class ServicosPagamentoController : ApiControllerBase
    {
        private readonly ICentralPendenciasAppService _centralPendenciasAppService;
        private readonly IViagemAppService _viagemAppService;
        private readonly IPagamentosAppService _pagamentosAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="centralPendenciasAppService"></param>
        /// <param name="viagemAppService"></param>
        /// <param name="pagamentosAppService"></param>
        public ServicosPagamentoController(IAppEngine engine, 
            ICentralPendenciasAppService centralPendenciasAppService, 
            IViagemAppService viagemAppService, 
            IPagamentosAppService pagamentosAppService) 
            : base(engine)
        {
            _centralPendenciasAppService = centralPendenciasAppService;
            _viagemAppService = viagemAppService;
            _pagamentosAppService = pagamentosAppService;
        }
        
        /// <summary>
        /// BAT_PGTO_01: Consulta todos os eventos de pagamento pendentes e tenta reenvia-los.
        /// Deve ser executado a cada 15 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("ReenviarPagamentosPendentes")]
        public async Task ReenviarPagamentosPendentes()
        {
            await _centralPendenciasAppService.ServiceReenviarPagamentosPendentes();
        }
        
        /// <summary>
        /// BAT_PGTO_02: Executa o cancelamento dos eventos de pagamento.
        /// Deve ser executado a cada 5 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("CancelarPagamentoEventos")]
        public async Task CancelarPagamentoEventos()
        {
            await _viagemAppService.ServiceCancelarPagamentoEventos();
        }
        
        /// <summary>
        /// BAT_PGTO_03: Executa os pagamentos agendados na Dock.
        /// Deve ser executado a cada 5 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("RealizarPagamentosAgendados")]
        public async Task RealizarPagamentosAgendados()
        {
            await _pagamentosAppService.ServiceRealizarPagamentosAgendados();
        }
    }
}