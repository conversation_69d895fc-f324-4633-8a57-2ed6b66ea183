using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Interface.Base;
using SistemaInfo.BBC.Application.Objects.Api.Pagamento;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Pagamentos;
using SistemaInfo.BBC.Application.Objects.Web.PainelPagamento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Pagamentos
{
    public interface IPagamentosAppService : IAppService<Domain.Models.Pagamentos.Pagamentos, 
        IPagamentosReadRepository, IPagamentosWriteRepository>,
        IBaseGetAppService<ConsultarPorIdPagamentoResponse>
    {
        ConsultaGridPagamentoResponse ConsultarGridPainelPagamento(String dataInicial, String dataFinal, Status status, int empresaId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        List<ConsultaGridPagamentoItem> DadosRelatorioGridPagamentos(String dtInicial, String dtFinal, Status status, int empresaId, OrderFilters orderFilters, List<QueryFilters> filters);
        Task SavePagamento(PagamentosRequest lModel);
        Task AlterarStatus(PagamentosStatusRequest lPagamentoStatus);
        Task<IntegrarPagamentoResponse>  ReenviarPagamento(PagamentosReenviarRequest lReenviarPagamento);
        Task<IntegrarPagamentoResponse> IntegrarPagamento(PagamentoIntegrarRequest pagamentoIntegrarRequest);
        Task<ConsultarContaResp> ConsultaContaCPF(string cpfCnpj);
        Task<RespPadrao> ValidarCancelamento(int[] pagamentosId);
        Task<RespPadrao> CancelarPagamento(int pagamentoId);
        Task<RespPadrao> BloquearPagamento(int pagamentoId);
        ConsultarPagamentosResponse ConsultarPagamentos(ConsultarPagamentoRequest request);
        Task<RespPadrao> TransferenciaP2P(TransferenciaP2PRequest request);
        Task<decimal> ConsultarValorPagamentosDia(DateTime dtInicio, DateTime dtFim, int empresaId);
        #region Tarefas
        
        Task ServiceRealizarPagamentosAgendados();

        #endregion
    }
}    