﻿using System;
using System.Globalization;
using SistemaInfo.BBC.Application.Objects.Api.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.LotePagamento;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.PagamentoAbastecimentoMapper
{
    public class PagamentoAbastecimentoMappingProfile : SistemaInfoMappingProfile
    {
        public PagamentoAbastecimentoMappingProfile()
        {
            CreateMap<ConsultarGridPainelFinanceiroItem, PagamentoAbastecimentoExport>()
                .ForMember(d => d.Id, o => o.MapFrom(s => s.Id))
                .ForMember(d => d.Cnpj, o => o.MapFrom(s => s.Cnpj))
                .ForMember(d => d.CashbackTransacao, o => o.MapFrom(s => "R$ "+(s.CashbackTransacao ?? 0).ToString("F3")))
                .ForMember(d => d.ValorOperacao, o => o.MapFrom(s => "R$ "+(s.ValorOperacao ?? 0).ToString("F3", CultureInfo.InvariantCulture)))
                .ForMember(d => d.DataPagamento, o => o.MapFrom(s => s.DataPagamento))
                .ForMember(d => d.Status, o => o.MapFrom(s => s.Status))
                .ForMember(d => d.StatusProtocolo, o => o.MapFrom(s => s.StatusProtocolo))
                .ForMember(d => d.DataPagamento, o => o.MapFrom(s => s.DataPagamento))
                .ForMember(d => d.Tarifa, o => o.MapFrom(s => "R$ "+(s.Tarifa ??0).ToString("F3", CultureInfo.InvariantCulture)));

            CreateMap<PagamentoAbastecimento, ConsultarGridPainelFinanceiroItem>()
                .ForMember(dest => dest.LotePagamentoId, opts => opts.MapFrom(s => s.LotePagamentoId))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.Tarifa, opts => opts.MapFrom(s => s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao ? s.TaxaAbastecimento : s.DescontoMDR))
                .ForMember(dest => dest.TarifaPosto, opts => opts.MapFrom(s => Math.Round((s.ValorAbastecimento * (s.Abastecimento.Posto.MdrPrazos.MDR / 100))??0,2)))
                .ForMember(dest => dest.CashbackTransacao, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento * (s.CashbackTransacao ?? 0 / 100), 2)))
                .ForMember(dest => dest.ValorOperacao, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento + s.TaxaAbastecimento - s.ValorAbastecimento * (s.CashbackTransacao ?? 0 / 100), 2)))
                .ForMember(dest => dest.ValorOperacaoPosto, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimentoDesconto,2)))
                .ForMember(dest => dest.Combustivel, opts => opts.MapFrom(s => s.Abastecimento.Combustivel.Nome ?? null))
                .ForMember(dest => dest.RazaoSocial, opts => opts.MapFrom(s => 
                    (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento || (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && s.Abastecimento == null && s.LotePagamento != null)) 
                        ? s.LotePagamento.Posto.RazaoSocial ?? s.LotePagamento.Posto.NomeFantasia : s.Abastecimento.Posto.RazaoSocial ?? s.Abastecimento.Posto.NomeFantasia))
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Abastecimento.Litragem.ToDecimal()))
                .ForMember(dest => dest.NotaFiscal, opts => opts.MapFrom(s => s.Abastecimento.NumeroItemXmlNota ?? null))
                .ForMember(dest => dest.ProtocoloId, opts => opts.MapFrom(s => s.Abastecimento.ProtocoloAbastecimentoId ?? null))
                .ForMember(dest => dest.DataPagamento, opts => opts.MapFrom(s => s.DataBaixa.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.Cnpj, opts => opts.MapFrom(s => 
                    (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento || (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && s.Abastecimento == null && s.LotePagamento != null)) 
                        ? s.LotePagamento.Posto.Cnpj ?? s.LotePagamento.Posto.CnpjFaturamento : s.Abastecimento.Posto.Cnpj ?? s.Abastecimento.Posto.CnpjFaturamento))
                .ForMember(dest => dest.NotaFiscal, opts => opts.MapFrom(s => s.Abastecimento.ProtocoloAbastecimento.NotaFiscal));

            CreateMap<PagamentoAbastecimento, ConsultarGridPainelFinanceiroRelatorioItem>()
                .ForMember(dest => dest.Tarifa,
                    opts => opts.MapFrom(s =>
                        s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao
                            ? s.TaxaAbastecimento
                            : s.DescontoMDR))
                .ForMember(dest => dest.CashbackTransacao,
                    opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento * (s.CashbackTransacao ?? 0 / 100), 2)))
                .ForMember(dest => dest.ValorOperacao,
                    opts => opts.MapFrom(s =>
                        Math.Round(
                            s.ValorAbastecimento + s.TaxaAbastecimento -
                            s.ValorAbastecimento * (s.CashbackTransacao ?? 0 / 100), 2)))
                .ForMember(dest => dest.RazaoSocial, opts => opts.MapFrom(s =>
                    (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento ||
                     (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && s.Abastecimento == null &&
                      s.LotePagamento != null))
                        ? s.LotePagamento.Posto.RazaoSocial ?? s.LotePagamento.Posto.NomeFantasia
                        : s.Abastecimento.Posto.RazaoSocial ?? s.Abastecimento.Posto.NomeFantasia))
                .ForMember(dest => dest.DataPagamento,
                    opts => opts.MapFrom(s => s.DataBaixa.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.Cnpj, opts => opts.MapFrom(s =>
                    (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento ||
                     (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && s.Abastecimento == null &&
                      s.LotePagamento != null))
                        ? s.LotePagamento.Posto.Cnpj ?? s.LotePagamento.Posto.CnpjFaturamento
                        : s.Abastecimento.Posto.Cnpj ?? s.Abastecimento.Posto.CnpjFaturamento));
            
            
            CreateMap<PagamentoAbastecimento, ConsultarGridPagamentoAbastecimentoItem>()
                .ForMember(dest => dest.RazaoSocial, opts => opts.MapFrom(s => 
                    s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento || (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && s.LotePagamentoId != null) 
                        ? s.LotePagamento.Posto.RazaoSocial : s.Abastecimento.Posto.RazaoSocial))
                .ForMember(dest => dest.PostoCnpj, opts => opts.MapFrom(s => 
                    s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento || (s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao && s.LotePagamentoId != null) 
                        ? s.LotePagamento.Posto.Cnpj : s.Abastecimento.Posto.Cnpj))
                .ForMember(dest => dest.ValorAbastecimentoAcrescimoTaxa, opts => opts.MapFrom(s => 
                    s.Abastecimento != null ? s.ValorAbastecimentoAcrescimoTaxa : s.LotePagamento != null && s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao
                        ? s.ValorAbastecimentoAcrescimoTaxa : 0))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => 
                    s.DataAlteracao != null ? s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime) : null))
                .ForMember(dest => dest.ValorTaxaEmpresa, opts => opts.MapFrom(s => 
                    s.AbastecimentoId != null || (s.LotePagamento != null && s.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao) ? s.TaxaAbastecimento : 0))
                .ForMember(dest => dest.DataPrevisaoPagamento, opts => opts.MapFrom(s => 
                    s.DataPrevisaoPagamento != null ? s.DataPrevisaoPagamento.ToStringBr(FormatDateTimeMethod.ShortDateTime) : null))
                .ForMember(dest => dest.LotePagamentoId, opts => opts.MapFrom(s => s.LotePagamentoId));

            CreateMap<PagamentoAbastecimento, LotePagamento>();

            CreateMap<PagamentoAbastecimento, ConsultarGridReceitaAbastecimentoItem>()
                .ForMember(dest => dest.ValorReceita, opts => opts.MapFrom(s => s.ValorAbastecimento))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status))
                .ForMember(dest => dest.DataBaixaReceita, opts => opts.MapFrom(s => s.DataBaixa.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.PagamentoReceitaId, opts => opts.MapFrom(s => s.PagamentoReceitaId ?? 0));
            
            CreateMap<AbastecimentoTransacaoIntegrarApiRequest, PagamentoAbastecimentoAdicionarCommand>();
            
            CreateMap<LoteTransacaoIntegrarApiRequest, PagamentoAbastecimentoAdicionarCommand>();
            
            CreateMap<PagamentoAbastecimentoAdicionarCommand, PagamentoAbastecimento>();
            
            CreateMap<AbastecimentoTransacaoIntegrarApiRequest, PagamentoAbastecimentoAtualizarCommand>();

            CreateMap<PagamentoAbastecimentoAtualizarCommand, PagamentoAbastecimento>();
            
            CreateMap<PagamentoAbastecimento, PagamentoAbastecimentoAtualizarCommand>();
        }
    }
}