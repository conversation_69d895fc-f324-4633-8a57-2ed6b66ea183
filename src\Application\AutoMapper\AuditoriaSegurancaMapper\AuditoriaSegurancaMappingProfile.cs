﻿using SistemaInfo.BBC.Application.Objects.Web.AuditoriaSeguranca;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca;
using SistemaInfo.BBC.Domain.Models.AuditoriaSeguranca.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.AuditoriaSegurancaMapper
{
    public class AuditoriaSegurancaMappingProfile : SistemaInfoMappingProfile
    {
        public AuditoriaSegurancaMappingProfile()
        {
            CreateMap<AuditoriaSeguranca, ConsultarGridAuditoriaSeguranca>()
                .ForMember(dest => dest.DataAcesso,opts => opts.MapFrom(s => s.DataAcesso.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.NomeUsuario,opts => opts.MapFrom(s => s.Usuario.Nome));

            CreateMap<AuditoriaSegurancaRequest, AuditoriaSegurancaSalvarCommand>();

            CreateMap<AuditoriaSegurancaRequest, AuditoriaSegurancaSalvarComRetornoCommand>();

            CreateMap<AuditoriaSegurancaSalvarCommand, AuditoriaSeguranca>();

            CreateMap<AuditoriaSegurancaSalvarComRetornoCommand, AuditoriaSeguranca>();
        }
    }
}