﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.VeiculoCombustivel;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.VeiculoCombustivel
{
    public interface IVeiculoCombustivelAppService : IAppService<Domain.Models.VeiculoCombustivel.VeiculoCombustivel, IVeiculoCombustivelReadRepository, IVeiculoCombustivelWriteRepository>
    {
        ConsultarGridVeiculoCombustivelResponse ConsultarGridVeiculoCombustivel(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        VeiculoCombustivelResponse ConsultarPorId(int idVeiculoCombustivel);
        Task<RespPadrao> Save(VeiculoCombustivelRequest lVeiculoCombustivelReq, bool integracao = false);
    }
}