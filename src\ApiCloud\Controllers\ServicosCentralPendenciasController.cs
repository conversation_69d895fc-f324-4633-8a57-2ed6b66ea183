﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.CentralPendencias;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("ServicosCentralPendencias")]
    public class ServicosCentralPendenciasController : ApiControllerBase
    {
        private readonly ICentralPendenciasAppService _centralPendenciasAppService;
        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        public ServicosCentralPendenciasController(IAppEngine engine, ICentralPendenciasAppService centralPendenciasAppService) 
            : base(engine)
        {
            _centralPendenciasAppService = centralPendenciasAppService;
        }
        
        /// <summary>
        /// BAT_CTPD_01: Reenvia os abastecimentos que obtiveram erro na integração à Movida (Vetor).
        /// Deve ser executado a cada 30min.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("ReenviarAbastecimentosMovida")]
        public async Task ReenviarAbastecimentosMovida() => await _centralPendenciasAppService.ServiceReenviarAbastecimentosMovida();
    }
}