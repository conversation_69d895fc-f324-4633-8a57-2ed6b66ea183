namespace SistemaInfo.BBC.Application.Objects.Api.Parametros
{
    public class ParametrosCadastrarRequest
    {
        public int Id { get; set; }
        public int ReferenciaId { get; set; }
        public Domain.Models.Parametros.Parametros.TipoDoParametro TipoParametros { get; set; }
        public string Valor { get; set; }
        public Domain.Models.Parametros.Parametros.TipoDoValor TipoValor { get; set; }
        public string InfoAdicional { get; set; }
    }
}