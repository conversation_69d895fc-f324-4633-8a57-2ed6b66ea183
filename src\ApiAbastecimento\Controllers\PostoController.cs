﻿using System;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiAbastecimento.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiAbastecimento.Controllers
{
    /// <summary>
    /// Controller Web dos Módulos de menu
    /// </summary>
    [Route("Posto")]
    public class PostoController : ApiControllerBase<IPostoAppService>
    {
        /// <summary>
        /// Injeções de deprendência do Controller Web dos Postos
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public PostoController(IAppEngine engine, IPostoAppService appService) : base(engine, appService)
        {
        }
        
        /// <summary>
        /// Consulta de cnpj ja cadastrado 
        /// </summary>
        /// <param name="cnpj"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("ConsultaCadastroCnpj")]
        public JsonResult ConsultaCadastroCnpj(string cnpj)
        {
            try
            {
                var retornoPosto = AppService.ConsultaCadastroCnpj(cnpj).Result;
    
              
                return ResponseBase.ResponderSucesso(retornoPosto);

            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
    }
}