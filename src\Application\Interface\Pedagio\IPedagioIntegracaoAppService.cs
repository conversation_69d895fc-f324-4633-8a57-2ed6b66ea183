﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Pedagio;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Pedagio;

public interface IPedagioIntegracaoAppService : IAppService
{
    Task<IntegrarPagamentoPedagioResponse> IntegrarPagamentoPedagio(IntegrarPagamentoPedagioRequest request);
    Task<CancelarPagamentoPedagioResponse> CancelarPagamentoPedagio(CancelarPagamentoPedagioRequest request);
    Task<ComplementarPagamentoPedagioResponse> ComplementarPagamentoPedagio(ComplementarPagamentoPedagioRequest request);
    Task<CancelarComplementoPagamentoPedagioResponse> CancelarComplementoPagamentoPedagio(CancelarComplementoPagamentoPedagioRequest request);
}