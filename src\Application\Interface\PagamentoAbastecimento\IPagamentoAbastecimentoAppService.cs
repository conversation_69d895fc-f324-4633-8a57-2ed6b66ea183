using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.Models.PagamentoAbastecimento.Repository;
using SistemaInfo.BBC.Infra.Reports.Objects;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento
{
    public interface IPagamentoAbastecimentoAppService : IAppService<Domain.Models.PagamentoAbastecimento.PagamentoAbastecimento, IPagamentoAbastecimentoReadRepository, IPagamentoAbastecimentoWriteRepository>
    {
        //API
        Task<ConsultarContaResp> ConsultaContaCPFCNPJ(string cpfCnpj);
        Task<RespPadrao> IntegrarRetencao(int abastecimentId, bool integracao = false);
        Task<RespPadrao> IntegrarRetencaoProtocolo(int protocoloId, bool prazo = false);
        Task<RespPadrao> CancelarRetencao(int retencaoId, bool integracao = false);
        Task<RespPadrao> EnviarPagamento(int pagamentoAbastecimentoId, bool integracao = false);
        Task<RespPadrao> ConsultarGridPagamentoAbastecimento(DtoConsultaGridPagamentoAbastecimento request);
        Task<RespPadrao> ConsultarPorId(int idPagamentoAbastecimento);
        Task<RespPadrao> AprovarPagamentoPendente(List<AprovarPagamentoRequest> pagamentoId);
        Task<RespPadrao> ReprovarPagamentoPendente(List<AprovarPagamentoRequest> pagamentoId);
        Task<RespPadrao> ReenvioPagamento(List<AprovarPagamentoRequest> pagamentoId, bool integracao = false);
        Task<RespPadrao> ConsultarGridPainelFinanceiro(DtoConsultaGridPainelFinanceiro request);
        Task<RespPadrao> ConsultarGridPainelFinanceiroRelatorio(DtoConsultaGridPainelFinanceiro request);
        Task<RespPadrao> ExportarRelatorio(DtoExportarRelatorioPagamentoAbastecimento request);
        Task<RespPadrao> ConsultarGridReceitaAbastecimento(DtoConsultaGridReceitaAbastecimento request);
        Task<RespPadrao> ReenviarReceitas(List<ReenviarReceitaAbastecimento> lLoteReceitaId);
        Task<RespPadrao> PagarReceita(int? LoteReceitaId, bool integracao = false);

        #region Tarefas
        
        Task ServiceGerarRegistroRetencao();
        Task ServiceGerarRegistroPagamentoReceita();
        Task ServiceRealizarPagamentoReceita();
        Task ServiceGerarRegistroPagamentoAbastecimento();
        Task ServiceRealizarPagamentoAbastecimento();

        #endregion
    }
}