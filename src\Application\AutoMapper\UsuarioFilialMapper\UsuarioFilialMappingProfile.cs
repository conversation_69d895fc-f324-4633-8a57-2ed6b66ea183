﻿using SistemaInfo.BBC.Application.Objects.Web.UsuarioFilial;
using SistemaInfo.BBC.Domain.Models.UsuarioFilial;
using SistemaInfo.BBC.Domain.Models.UsuarioFilial.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.UsuarioFilialMapper
{
    public class UsuarioFilialMappingProfile : SistemaInfoMappingProfile
    {
        public UsuarioFilialMappingProfile()
        {
            CreateMap<UsuarioFilial, UsuarioFilialResp>()
                .ForMember(dest => dest.Nome, opts => opts.MapFrom(s => s.Filial.NomeFantasia.IsNullOrWhiteSpace()? s.Filial.RazaoSocial : s.Filial.NomeFantasia));

            CreateMap<UsuarioFilialSalvarCommand, UsuarioFilial>();
            
            CreateMap<UsuarioFilialRequest, UsuarioFilialSalvarComRetornoCommand>()
                .ForMember(dest => dest.FilialId, opts => opts.MapFrom(s => s.filialId));

            CreateMap<UsuarioFilialRequest, UsuarioFilialSalvarComRetornoCommand>();
            
            CreateMap<UsuarioFilialSalvarComRetornoCommand, UsuarioFilial>();
        }
    }
}