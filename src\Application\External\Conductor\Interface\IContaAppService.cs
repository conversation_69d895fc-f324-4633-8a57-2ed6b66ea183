using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.External.Conductor.Conta;
using SistemaInfo.BBC.Domain.Grid;

namespace SistemaInfo.BBC.Application.External.Conductor.Interface
{
    public interface IContaAppService
    {
        Task<GridExtratoResponse> ConsultarExtrato(int requestTipoExtrato, int requestTake, int requestPage,
            OrderFilters requestOrder, List<QueryFilters> requestFilters, int idConta, string dataInicio, string dataFim);
        SaldoResponse ConsultarSaldo(int idConta);

    }
}