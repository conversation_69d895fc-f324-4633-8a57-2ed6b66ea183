echo off
echo %~dp0
set pastaOrigem=%~dp0
chcp 65001 >nul
call %pastaOrigem%variaveis.bat
setlocal enabledelayedexpansion

for %%d in ("%CD%") do set folder=%%~nxd
set "Data=%DATE:/=-%"
set "Hora=%time:~0,2%h%time:~3,2%m"

cd %publish_dir%
set temp=%publish_dir%\branch-build.h
set /p branchname-build=<%temp%
set "branchname-build=%branchname-build:/=-%"
call :log %branchname-build%
call :log %projetoNome%

:input0
	call :info Deseja executar testes unitarios
	call :opt 0 - Nao
	call :opt 1 - Sim
	echo.
	set /p testes=""
	call :info Selecionado: %testes%
if %testes%==1 (
	call %pastaOrigem%test.bat
)

:input
	call :info Selecione o modo de pacote
	call :opt 0 - dll padrao
	call :opt 1 - Full dll
	call :opt 2 - Completo
	echo.
	set /p tipo=""
	call :info Tipo de pacote selecionado: %tipo%

call %pastaOrigem%remove-config.bat
::xcopy "%projeto%\scripts\utils\BBC - Transferencia de pacotes.bat" "%publish_dir%" /y
::xcopy "%projeto%\scripts\utils\BBC - Verifica ambiente.bat" "%publish_dir%" /y
set destino=%deploy_dir%\%Data%
mkdir "%destino%"
xcopy "%publish_dir%\branch-build.h" "%destino%" /y
xcopy "%publish_dir%\branch-front.h" "%destino%" /y
xcopy "%publish_dir%\ResultadoTestesUnitarios.trx" "%destino%" /y

cd %publish_dir%
call :log %cd%
if %tipo%==0 (
call %Seven% a -y -tzip "%destino%\%projetoNome% %branchname-build% %Hora%.zip" */*%dllPadrao%*.dll */bin/*%dllPadrao%*.dll */*.html */styles/*.css */scripts/*.js *.bat
call :transfere-servidor
)
if %tipo%==1 (
call %Seven% a -y -tzip "%destino%\%projetoNome% %branchname-build% %Hora%.zip" */*.dll */bin/*.dll */bin/Content */Scripts */*.html */styles/*.css */scripts/*.js */assets/lib/*.js *.bat
call :transfere-servidor
)
if %tipo%==2 (
call %Seven% a -y -tzip "%destino%\%projetoNome% %branchname-build% %Hora%.zip" *
::call %Seven% d "%destino%\%projetoNome% %branchname-build% %Hora%.zip" *.h -r
call :transfere-servidor
)
else (
	call :warn opcao nao existente!
	goto :input
)

:transfere-servidor
xcopy "%destino%\%projetoNome% %branchname-build% %Hora%.zip" "%server_dir%\" /y
call :fim

:fim
	if %errorlevel%==1 (call :erro) else (call :sucesso)
	exit %errorlevel%
	
:: Sets up the ESC string for use later in this script
:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0
	
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:info
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo.
	exit /B 0

:warn
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo !ESC![93m	%*
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:erro
	echo.
	call :setESC
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo !ESC![91m	%~n0%~x0^> %*
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 1

:sucesso
	echo.
	call :setESC
	echo !ESC![92m===================================================================================================!!ESC![0m
	echo !ESC![92m	Operacao Concluida!!ESC![0m
	echo !ESC![92m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 0