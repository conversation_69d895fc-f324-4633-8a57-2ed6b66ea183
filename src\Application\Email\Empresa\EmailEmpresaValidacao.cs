using System;
using System.IO;
using System.Net.Mail;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Email.Empresa
{
    public class EmailEmpresaValidacao
    {
        public static void EnviarEmailValidacao(INotificationEmailExecutor notificationEmailExecutor, string destinatario, StatusCadastro statusCadastro, string parecerExterno, string usuario, string senha)
        {
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;
            var textoMensagem = string.Empty;
            var labelParecerExterno = string.Empty;
            var textoParecerExtreno = string.Empty;
            var labelUsuario = string.Empty;
            var textoUsuario = string.Empty;
            var labelSenha = string.Empty;
            var textoSenha = string.Empty;

            if (statusCadastro == StatusCadastro.Ativo)
            {
                textoMensagem = "Informamos que o cadastro da sua empresa foi aprovado no BBC Ciot. Segue abaixo os dados de acesso ao sistema (para sua segurança, altere a senha durante o primeiro acesso).";
                labelUsuario = "Usuário:";
                textoUsuario = usuario;
                labelSenha = "Senha:";
                textoSenha = senha;
            }

            if (statusCadastro == StatusCadastro.Bloqueado)
            {
                textoMensagem = "Informamos que o cadastro de sua empresa foi rejeitado no BBC Ciot pelos seguintes motivos.";
                labelParecerExterno = "Parecer:";
                textoParecerExtreno = parecerExterno;
            }
            
            using (var streamReader = new StreamReader(caminhoAplicacao + @"\Content\Email\Empresa\validacao-cadastro-empresa.html"))
            {
                var html = streamReader.ReadToEnd();
                html = html.Replace("{TEXTO_EMAIL}", textoMensagem);
                html = html.Replace("{LABEL_PARECER_EXTERNO}", labelParecerExterno);
                html = html.Replace("{TEXTO_PARECER_EXTERNO}", textoParecerExtreno);
                html = html.Replace("{LABEL_USUARIO}", labelUsuario);
                html = html.Replace("{TEXTO_USUARIO}", textoUsuario);
                html = html.Replace("{LABEL_SENHA}", labelSenha);
                html = html.Replace("{TEXTO_SENHA}", textoSenha);
                
                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                
                notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                {
                    //From = new Domain.Components.Email.Email.EmailAddress {Address = "<EMAIL>", DisplayName = "Minhazarma"},
                    To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                    Priority = MailPriority.High,
                    Subject = "VALIDAÇÃO DE CADASTRO",
                    IsBodyHtml = true,
                    AlternateView = view
                });
            }
        }
    }
}