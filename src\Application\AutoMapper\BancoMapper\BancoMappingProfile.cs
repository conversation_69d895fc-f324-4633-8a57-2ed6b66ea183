﻿using SistemaInfo.BBC.Application.Objects.Web.Banco;
using SistemaInfo.BBC.Application.Objects.Web.Fabricante;
using SistemaInfo.BBC.Domain.Models.Banco;
using SistemaInfo.BBC.Domain.Models.Banco.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.BancoMapper
{
    public class BancoMappingProfile : SistemaInfoMappingProfile
    {
        public BancoMappingProfile()
        {
            CreateMap<BancoRequest, BancoSalvarCommand>();
            CreateMap<BancoRequest, BancoAlterarStatusCommand>();

            CreateMap<BancoRequest, BancoSalvarComRetornoCommand>();
            
            CreateMap<BancoSalvarCommand, Banco>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<BancoSalvarComRetornoCommand, Banco>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<BancoStatusRequest, BancoAlterarStatusCommand>();
        }
    }
}