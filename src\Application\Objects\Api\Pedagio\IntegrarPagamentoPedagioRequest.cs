﻿using System.ComponentModel.DataAnnotations;

namespace SistemaInfo.BBC.Application.Objects.Api.Pedagio;

public class IntegrarPagamentoPedagioRequest
{
    /// <summary>Núemro unico da requisição de pagamento</summary>
    /// <example>2734652</example>
    [DataType("Inteiro")]
    public int CodigoRequisicao { get; set; } 
    /// <summary>Núemro unico do vale pedagio</summary>
    /// <example>2734652</example>
    [DataType("Inteiro")]
    public int CodigoValePedagio { get; set; }
    /// <summary>Valor do pagamento</summary>
    /// <example>23.45</example>
    [DataType("Double")]
    public decimal Valor { get; set; }
    /// <summary>Valor do pagamento</summary>
    /// <example>23.45</example>
    [DataType("String")]
    public string Descricao { get; set; }
} 