using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// Class responsavel por conter todos os metodos utilizados pela aplicação portador
    /// </summary>
    [Route("Portador")]
    public class PortadorController : ApiControllerBase
    {
        
        private readonly IPortadorAppService _portadorAppService;
        
        /// <summary>
        /// Injeção de dependencias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="portadorAppService"></param>
        public PortadorController(IAppEngine engine, IPortadorAppService portadorAppService) : base(engine)
        {
            _portadorAppService = portadorAppService;
        }
        
        /// <summary>
        /// Metodo responsavel por consultar portadores
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Consultar")]
        public JsonResult ConsultarPortadores([FromBody] ConsultarPortadorRequest request)
        {
            try
            {
                
                
                ConsultarGridPortadorResponse lPortadores;
                
                if (!string.IsNullOrWhiteSpace(request.CpfCnpj))
                {
                    List<QueryFilters> listFilter = new List<QueryFilters>();
                    listFilter.Add(new QueryFilters()
                    {
                        Campo = "CpfCnpj",
                        Operador = EOperador.Exact,
                        Valor = request.CpfCnpj,
                        CampoTipo = EFieldTipo.String
                    });
                    
                    lPortadores = _portadorAppService.ConsultarGridPortador(request.Take, request.Page, null, listFilter);
                    
                    return ResponseBaseApi.ResponderSucesso(lPortadores);
                }
                
                lPortadores = _portadorAppService.ConsultarGridPortador(request.Take, request.Page, null, null);
                
                return ResponseBaseApi.ResponderSucesso(lPortadores);
                
            }
            catch (Exception e)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
    }
}