﻿using System;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem;

public class ConsultarPagamentosDiaRequest
{
    public ConsultarPagamentosDiaRequest(DateTime dtInicio, DateTime dtFim, int idGrupoEmpresa)
    {
        DtInicio = dtInicio;
        DtFim = dtFim;
        IdGrupoEmpresa = idGrupoEmpresa;
    }

    public ConsultarPagamentosDiaRequest()
    {
    }

    public DateTime DtInicio { get; set; }
    public DateTime DtFim { get; set; }
    public bool? IncluiTarifa { get; set; }
    public int IdGrupoEmpresa { get; set; }
}