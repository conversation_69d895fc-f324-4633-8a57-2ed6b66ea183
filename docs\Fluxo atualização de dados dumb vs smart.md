-> smart pede: "select * from aboe where...."

<- dumb recebe instruição
-- dumb: aplicar o sql
-- dumb: descobre a estrutura de dados
-> dumb: publica para o smart a estrutura de dados do sqk
	{
		"DocEntry": {
			Type: Text
			Size: 50
		},
		"Valor": {
			Type: Text
			Size: 10,
			Precison: 2
		}
        ....
	}

-> dumb: Publica cada linha do resultado na fila. Sendo um publish para cada linha (Não deve ser publicado um mega payload com N linhas)
<- smart: Recebe estrutura de dados e cria tabela/campos caso necessário no postgres referente ao cliente
<- smart: Recebrá cada linha e persistirá no postgre