using System;

namespace SistemaInfo.BBC.Application.Objects.Web.ClientSecret
{
    public class ClientSecretRequest
    {
        public int Id { get; set; }
        public string Descricao { get; set; }
        public string SecretKey { get; set; }
        public DateTime? DataExpiracao { get; set; }
        public string SenhaApi { get; set; }
        public int? IdEmpresa { get; set; }
        public string NomeEmpresa { get; set; }        
        public int? GrupoEmpresaId { get; set; }
        public string NomeGrupoEmpresa { get; set; }
        public int Ativo { get; set; } = 1;
        public DateTime? DataCadastro { get; set; } = DateTime.Now;
        public DateTime? DataDesativacao { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public int? UsuarioCadastroId { get; set; }
        public int? UsuarioAlteracaoId { get; set; }
        public string Senha { get; set; }
    }
}