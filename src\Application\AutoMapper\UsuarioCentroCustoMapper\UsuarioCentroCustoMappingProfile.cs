﻿using SistemaInfo.BBC.Application.Objects.Web.UsuarioCentroCusto;
using SistemaInfo.BBC.Domain.Models.UsuarioCentroCusto;
using SistemaInfo.BBC.Domain.Models.UsuarioCentroCusto.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.UsuarioCentroCustoMapper
{
    public class UsuarioCentroCustoMappingProfile : SistemaInfoMappingProfile
    {
        public UsuarioCentroCustoMappingProfile()
        {
            CreateMap<UsuarioCentroCusto, UsuarioCentroCustoResp>()
                .ForMember(dest => dest.Nome, opts => opts.MapFrom(s => s.CentroCusto.Descricao))
                .ForMember(dest => dest.FilialId, opts => opts.MapFrom(s => s.CentroCusto.FilialId));

            CreateMap<UsuarioCentroCustoSalvarCommand, UsuarioCentroCusto>();
            
            CreateMap<UsuarioCentroCustoRequest, UsuarioCentroCustoSalvarComRetornoCommand>()
                .ForMember(dest => dest.CentroCustoId, opts => opts.MapFrom(s => s.centroCustoId));
        }
    }
}