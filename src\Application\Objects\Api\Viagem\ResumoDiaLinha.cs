﻿using SistemaInfo.BBC.Domain.Helper;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem;

public class ResumoDiaLinha
{
    public string Descricao { get; set; }
    public decimal Valor { get; set; }
    public string ValorFormatted => Valor.FormatMoney();
    public string StyleCss { get; set; }
}
public class ResumoDia
{
    public decimal UtilizadoBBC { get; set; }

    /// <summary>
    /// Adiantamento inicial do cliente (R$ 4.000.000,00)
    /// </summary>
    public decimal AdiantamentoCliente { get; set; }

    /// <summary>
    /// Pagamentos realizados ontem e até o momento do dia atual
    /// </summary>
    public decimal Utilizado { get; set; }
    
    /// <summary>
    /// Valor que previsto JSL Leasing receber hoje
    /// </summary>
    public decimal Compensacao { get; set; }
}
