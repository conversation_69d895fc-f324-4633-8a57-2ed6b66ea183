using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Api.Emprestimo
{
    public class EmprestimoConsultarParaEdicao
    {
        public int Id { get; set; }
        public DateTime? DataEmprestimo { get; set; }
        public int? PortadorId { get; set; }
        public string PortadorNome { get; set; }
        public string CpfCnpjPortador { get; set; }
        public int Status { get; set; }
        public string IdState { get; set; } 
        public string TaxaRetencao { get; set; }
        public string ValorAquisicao { get; set; }
        public string ValorPago { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public List<EmprestimoRetencaoConsultarParaEdicao> Retencoes { get; set; }
    }

    public class EmprestimoRetencaoConsultarParaEdicao
    {
        public int Id { get; set; }
        public string DataIntegracao { get; set; }
        public string DataCadastro { get; set; }
        public int? EmprestimoId { get; set; }
        public int? PagamentoId { get; set; }
        public int Status { get; set; }
        public string StatusStr { get; set; }
        public string Valor { get; set; }
        public string MensagemIntegracao { get; set; }
    }
}