using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Api.Emprestimo
{
    public class EmprestimoConsultarParaRelatorio
    {
        public string Id { get; set; }
        public string DataEmprestimo { get; set; }
        public string PortadorId { get; set; }
        public string PortadorNome { get; set; }
        public string CpfCnpjPortador { get; set; }
        public string Status { get; set; }
        public string IdState { get; set; } 
        public string TaxaRetencao { get; set; }
        public string ValorAquisicao { get; set; }
        public string ValorPago { get; set; }
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public List<EmprestimoRetencaoConsultarParaRelatorio> Retencoes { get; set; }
    }

    public class EmprestimoRetencaoConsultarParaRelatorio
    {
        public string Id { get; set; }
        public string DataIntegracao { get; set; }
        public string DataCadastro { get; set; }
        public string EmprestimoId { get; set; }
        public string PagamentoId { get; set; }
        public string Status { get; set; }
        public string Valor { get; set; }
        public string MensagemIntegracao { get; set; }
    }
}