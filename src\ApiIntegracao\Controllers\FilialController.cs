using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Filial;
using SistemaInfo.BBC.Application.Objects.Api.Filial;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// Class contendo metodos referentes a Filial
    /// </summary>
    [Route("Filiais")]
    public class FilialController : ApiControllerBase<IFilialAppService>
    {
        /// <summary>
        /// Injeção de dependencias
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public FilialController(IAppEngine engine, IFilialAppService appService) : base(engine, appService)
        {
        }
        
        /// <summary>
        /// Metodo responsavel pela consulta de filial
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Consultar")]
        public JsonResult Consultar([FromBody]FilialConsultarApiRequest request)
        {            
            try
            {                
                var ConsultarFilial = AppService.Consultar(request).Result;
                return ResponseBaseApi.ResponderSucesso(ConsultarFilial);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Nenhuma Filial encontrada!");
            }
        }
        
        /// <summary>
        /// Metodo de integração
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Integrar")]
        public JsonResult Integrar([FromBody]FilialIntegrarApiRequest request)
        {            
            try
            {                
                var response = AppService.Integrar(request).Result;
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }
    }
}