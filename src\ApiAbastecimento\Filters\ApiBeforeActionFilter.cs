﻿using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Authentication;
using System.Text;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.IdentityModel.Tokens;
using NLog;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CustomFilters;
using SistemaInfo.BBC.ApiAbastecimento.Security;
using SistemaInfo.BBC.Domain.Exceptions;

namespace SistemaInfo.BBC.ApiAbastecimento.Filters
{
    /// <summary>
    /// 
    /// </summary>
    public class ApiBeforeActionFilter : IActionFilter
    {
        private static readonly Type[] AllowedNullParametersTypes = {typeof(FilterOptions)};
        private static Guid logId = Guid.Empty;
        private Microsoft.Extensions.Configuration.IConfiguration _configuration;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        public ApiBeforeActionFilter(Microsoft.Extensions.Configuration.IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        /// <exception cref="ArgumentException"></exception>
        public void OnActionExecuting(ActionExecutingContext context)
        {
            var nomesParametrosEsperados = context.ActionDescriptor.Parameters
                .Where(p => Nullable.GetUnderlyingType(p.ParameterType) == null &&
                            !AllowedNullParametersTypes.Contains(p.ParameterType))
                .Select(c => c.Name)
                .ToList();

            var basePath = "";
            foreach (var valuesRoute in context.ActionDescriptor.RouteValues.Reverse())
            {
                basePath += (basePath == "" ? valuesRoute.Value : "/" + valuesRoute.Value);
            }

            if (context.HttpContext.Request.Path != "/AuthSession/GerarToken" &&
                context.HttpContext.Request.Path != "/Usuario/Cadastro" &&
                context.HttpContext.Request.Path != "/Usuario/RecuperarSenha" &&
                context.HttpContext.Request.Path != "/Services/ReenviarPagementosPendentes" &&
                context.HttpContext.Request.Path != "/Pix/WebhookPixCatcher")
            {
                var AuthSecretKey =
                    Encoding.UTF8.GetString(Convert.FromBase64String(_configuration["Authentication:SecretKey"]));

                ValidarToken(context.HttpContext.Request.Headers["x-web-auth-token"], //alterar para session-key
                    new TokenConfigurations
                    {
                        Audience = "EveryApplication",
                        Issuer = "MyServer",
                        Seconds = 600
                    }, new SigningConfigurations(AuthSecretKey),
                    context);
            }

            var nomesParametrosRecebidos = context.ActionArguments.Select(c => c.Key).ToList();

            if (!nomesParametrosEsperados.Any())
            {
                return;
            }

            var sequenceEqual = nomesParametrosEsperados.OrderBy(t => t).All(c => nomesParametrosRecebidos.Contains(c));

            if (sequenceEqual)
            {
                return;
            }

            var inexistenteName = nomesParametrosEsperados.First(c => !nomesParametrosRecebidos.Contains(c));
            var parametro = context.ActionDescriptor.Parameters.First(c => c.Name == inexistenteName);

            throw new BadRequestException(
                $"Request {parametro.ParameterType.Name} inválida. Certifique-se se o json de envio é válido de acordo com o esperado pelo método");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="token"></param>
        /// <param name="tokenConfigurations"></param>
        /// <param name="signingConfigurations"></param>
        /// <param name="actionExecutingContext"></param>
        /// <exception cref="SecurityTokenExpiredException"></exception>
        public void ValidarToken(string token, TokenConfigurations tokenConfigurations,
            SigningConfigurations signingConfigurations,
            ActionExecutingContext actionExecutingContext)
        {
            try
            {
                SecurityToken validatedToken;
                var validationToken = new TokenValidationParameters
                {
                    IssuerSigningKey = signingConfigurations.Key,
                    ValidAudience = tokenConfigurations.Audience,
                    ValidIssuer = tokenConfigurations.Issuer,
                    ValidateLifetime = true,
                    RequireExpirationTime = true,
                    RequireSignedTokens = true
                };

                var handler = new JwtSecurityTokenHandler();
                var validToken = handler.ValidateToken(token, validationToken, out validatedToken);

                if (validatedToken.ValidTo < DateTime.UtcNow)
                {
                    throw new SecurityTokenExpiredException();
                }
            }
            catch (Exception)
            {
                throw new AuthenticationException();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="context"></param>
        public void OnActionExecuted(ActionExecutedContext context)
        {
            try
            {

            }
            catch (Exception e)
            {
                var lLog = LogManager.GetCurrentClassLogger();
                lLog.Error(e, "ApiBeforeAction error: ");
            }
        }
    }
}