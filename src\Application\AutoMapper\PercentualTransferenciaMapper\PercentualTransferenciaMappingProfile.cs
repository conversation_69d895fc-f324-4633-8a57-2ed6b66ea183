﻿using SistemaInfo.BBC.Application.Objects.Web.PercentualTransferencia;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Commands;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaHistorico;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador;
using SistemaInfo.BBC.Domain.Models.PercentualTransferenciaPortador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.PercentualTransferenciaMapper
{
    public class PercentualTransferenciaMappingProfile : SistemaInfoMappingProfile
    {
        public PercentualTransferenciaMappingProfile()
        {
            CreateMap<PercentualTransferencia, PercentualTransferenciaResponse>()
                .ForMember(dest => dest.ProprietarioNome, opts => opts.MapFrom(s => s.Proprietario.Nome))
                .ForMember(dest => dest.ProprietarioCpf, opts => opts.MapFrom(s => s.Proprietario.CpfCnpj))
                .ForMember(dest => dest.UsuarioCadastroNome, opts => opts.MapFrom(s => s.UsuarioCadastro.Nome))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)));
           
            CreateMap<PercentualTransferenciaHistorico, PercentualTransferenciaHistoricoResponse>()
                .ForMember(dest => dest.ParaTodosMotoristas, opts => opts.MapFrom(s => s.ParaTodosMotoristas == 1 ? "Ativado" : "Desativado"))
                .ForMember(dest => dest.UsuarioAlteracaoNome, opts => opts.MapFrom(s => s.UsuarioAlteracao.Nome))
                .ForMember(dest => dest.UsuarioAlteracaoCpf, opts => opts.MapFrom(s => s.UsuarioAlteracao.Cpf))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)));

            CreateMap<PercentualTransferenciaPortador, PercentualTransferenciaPortadorResponse>()
                .ForMember(dest => dest.PortadorNome, opts => opts.MapFrom(s => s.Portador.Nome))
                .ForMember(dest => dest.PortadorCpf, opts => opts.MapFrom(s => s.Portador.CpfCnpj))
                .ForMember(dest => dest.UsuarioCadastroNome, opts => opts.MapFrom(s => s.UsuarioCadastro.Nome))
                .ForMember(dest => dest.UsuarioCadastroCpf, opts => opts.MapFrom(s => s.UsuarioCadastro.Cpf))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)));

            CreateMap<Portador, ConsultarGridPercentualTransferencia>()
                .ForMember(dest => dest.Nome, opts => opts.MapFrom(s => s.Nome))
                .ForMember(dest => dest.CpfCnpj, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.CpfCnpj)));
            
            CreateMap<PercentualTransferenciaRequest, PercentualTransferenciaSalvarComRetornoCommand>();
            
            CreateMap<PercentualTransferenciaRequest, PercentualTransferenciaSalvarCommand>();
            
            CreateMap<PercentualTransferenciaSalvarCommand, PercentualTransferencia>();
            
            CreateMap<PercentualTransferenciaSalvarComRetornoCommand, PercentualTransferencia>();

            CreateMap<PercentualTransferenciaPortadorRequest, PercentualTransferenciaPortadorSalvarCommand>();
            
            CreateMap<PercentualTransferenciaPortadorAlterarStatusRequest, PercentualTransferenciaPortadorAlterarStatusCommand>();
            
            CreateMap<PercentualTransferenciaPortadorSalvarCommand, PercentualTransferenciaPortador>();
            
            CreateMap<PercentualTransferenciaPortadorAlterarStatusCommand, PercentualTransferenciaPortador>();

        }
    }
}