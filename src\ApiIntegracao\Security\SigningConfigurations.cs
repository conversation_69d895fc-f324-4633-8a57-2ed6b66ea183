﻿using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace SistemaInfo.BBC.ApiIntegracao.Security
{
    /// <summary>
    /// 
    /// </summary>
    public class SigningConfigurations
    {
        /// <summary>
        /// 
        /// </summary>
        public SecurityKey Key { get; }
        /// <summary>
        /// 
        /// </summary>
        public SigningCredentials SigningCredentials { get; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="secretkey"></param>
        public SigningConfigurations(string secretkey)
        {
            Key = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(secretkey));
            SigningCredentials = new SigningCredentials(Key, SecurityAlgorithms.HmacSha512);
        }
    }
}