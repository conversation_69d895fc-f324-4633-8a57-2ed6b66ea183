﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.External.Captalys.Interface;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("ServicosEmprestimo")]
    public class ServicosEmprestimoController : ApiControllerBase
    {
        private readonly IEmprestimoCaptalysAppService _emprestimoCaptalysAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="emprestimoCaptalysAppService"></param>
        public ServicosEmprestimoController(IAppEngine engine,
            IEmprestimoCaptalysAppService emprestimoCaptalysAppService) 
            : base(engine)
        {
            _emprestimoCaptalysAppService = emprestimoCaptalysAppService;
        }
        
        /// <summary>
        /// BAT_EMPR_01: Lista e integra emprestimos.
        /// Deve ser executado a cada 24h.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("IntegrarEmprestimosCaptalys")]
        public /*async Task*/ void IntegrarEmprestimosCaptalys()
        {
            //Desligado pois não funciona e não é mais necessário
            
            //await _emprestimoCaptalysAppService.ServiceIntegrarEmprestimosCaptalys();
        }
    }
}