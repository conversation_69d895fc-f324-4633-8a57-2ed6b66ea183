﻿using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Domain.Models.Documento;
using SistemaInfo.BBC.Domain.Models.Documento.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.DocumentoMapper
{
    public class DocumentoMappingProfile : SistemaInfoMappingProfile
    {
        public DocumentoMappingProfile()
        {

            CreateMap<DocumentosEmpresaRequest, DocumentoSalvarCommand>()
                .ForMember(d => d.Foto, opts =>
                    opts.MapFrom(s => s.Arquivo))
                .ForMember(d => d.Descricao, opts =>
                    opts.MapFrom(s => s.Descricao))
                .ForMember(d => d.Nome, opts =>
                    opts.MapFrom(s => s.Nome))
                .ForMember(d => d.Id, opts =>
                    opts.MapFrom(s => s.Id))
                .ForMember(d => d.Status, opts => 
                    opts.MapFrom(d => d.Status))
                .ForMember(d => d.Tipo, opts => 
                    opts.MapFrom(d => d.Tipo));

            CreateMap<DocumentoSalvarCommand, Documento>()
                .ForMember(d => d.Foto, opts =>
                    opts.MapFrom(s => s.Foto))
                .ForMember(d => d.Descricao, opts =>
                    opts.MapFrom(s => s.Descricao))
                .ForMember(d => d.Nome, opts =>
                    opts.MapFrom(s => s.Nome))
                .ForMember(d => d.Id, opts =>
                    opts.MapFrom(s => s.Id))
                .ForMember(d => d.Status, opts => 
                    opts.MapFrom(d => d.Status.GetHashCode()))
                .ForMember(d => d.Tipo, opts => 
                    opts.MapFrom(d => d.Tipo.GetHashCode()));
            
        }
    }
}