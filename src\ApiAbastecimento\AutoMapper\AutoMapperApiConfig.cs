﻿using AutoMapper;

namespace SistemaInfo.BBC.ApiAbastecimento.AutoMapper
{
    /// <summary>
    /// Class responsavel pela configuração com o apiAutoMapper
    /// </summary>
    public class AutoMapperApiConfig
    {
        
        /// <summary>
        /// Metodo responsavel pelo registro dos mapeamentos
        /// </summary>
        /// <param name="map"></param>
        public static void RegisterMappings(IMapperConfigurationExpression map)
        {
            map.AllowNullCollections = false;
            map.AllowNullDestinationValues = false;
            map.AddProfile(new DomainToApiResponseProfile());

//            var map = new MapperConfiguration(p =>
//            {
//                p.AllowNullCollections = false;
//                p.AllowNullDestinationValues = false;
//                p.AddProfile(new DomainToApiResponseProfile());
//            });
//
//            return map;
        }
    }
}