using System;
using System.IO;
using System.Net.Mail;
using System.Threading.Tasks;
using NLog;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Email.Usuario
{
    public class EmailUsuarioRecuperarSenha
    {
        public static async Task<RespPadrao> EnviarEmail(INotificationEmailExecutor notificationEmailExecutor, string nomeUsuario, string novaSenha, string destinatario, string novoUsuario)
        {
            try
            {
                var lCaminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

                using (var lStreamReader =
                    new StreamReader(lCaminhoAplicacao + @"Content\Email\Usuario\recuperar-senha-usuario.html"))
                {
                    var lEmailHtml = await lStreamReader.ReadToEndAsync();
                    lEmailHtml = lEmailHtml
                        .Replace("{EMAIL_TITLE}", string.IsNullOrEmpty(novoUsuario) ? "RECUPERAÇÃO DE SENHA" : "NOVO ACESSO")
                        .Replace("{CORPO}", $"class='{(string.IsNullOrEmpty(novoUsuario) ? "corpo" : "corpoNovoUsuario")}'")
                        .Replace("{EMAIL_BODY}", string.IsNullOrEmpty(novoUsuario)
                            ? $"&nbsp;&nbsp;&nbsp; Olá, {nomeUsuario}! Sua senha foi redefinida automaticamente. " +
                              "Para sua segurança, altere-a novamente ao realizar login no sistema."
                            : $"&nbsp;&nbsp;&nbsp; Olá, {nomeUsuario}! <br>Bem vindo ao sistema BBC Controle! " +
                              "Suas informações de acesso se encontram abaixo. <br>A senha é provisória e será expirada em breve. " +
                              "<br>Ao realizar o primeiro login, será solicitado automaticamente a troca da senha. " +
                              "<br>Para o cadastro da nova senha siga as instruções abaixo: " +
                              "<br>Quantidade mínima de 8 caracteres. " +
                              "Ao menos uma letra maiúscula e uma minúscula, números e caracteres especiais.")
                        .Replace("{STYLE_USUARIO}", $"style='display: {(string.IsNullOrEmpty(novoUsuario) ? "none" : "block")}'")
                        .Replace("{NOVO_USUARIO}", novoUsuario)
                        .Replace("{NOVA_SENHA}", novaSenha);

                    var lEmailView = AlternateView.CreateAlternateViewFromString(lEmailHtml, null, "text/html");

                    await notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                    {
                        To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                        Priority = MailPriority.High,
                        Subject = novoUsuario.IsNullOrWhiteSpace() ? "RECUPERAÇÃO DE SENHA" : "NOVO ACESSO",
                        IsBodyHtml = true,
                        AlternateView = lEmailView
                    });

                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Email enviado com sucesso!"
                    };
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Houve um erro ao enviar o e-mail ao usuário."
                };
            }
        }
    }
}