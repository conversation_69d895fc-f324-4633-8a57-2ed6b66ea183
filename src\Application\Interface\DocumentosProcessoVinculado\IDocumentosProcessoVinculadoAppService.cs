using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.DocumentosProcessoVinculado
{
    public interface IDocumentosProcessoVinculadoAppService : IAppService<Domain.Models.DocumentosProcessoVinculado.DocumentosProcessoVinculado, IDocumentosProcessoVinculadoReadRepository, IDocumentosProcessoVinculadoWriteRepository>
    {
        ConsultarGridDocumentosProcessoVinculadoResponse ConsultarGridDocumentosProcessoVinculado(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        DocumentosProcessoVinculadoResponse ConsultarPorId(int idDocumentosProcessoVinculado);
        Task<RespPadrao> Save(DocumentosProcessoVinculadoRequest lDocumentosProcessoVinculadoReq);
        Task AlterarStatus(DocumentosProcessoVinculadoStatusRequest lDocumentosProcessoVinculadoStatus);
        
        List<DocumentosProcessoVinculadoAnexarResponse> ConsultarDocumentosProcessoVinculado(int processoVinculado);

    }
}