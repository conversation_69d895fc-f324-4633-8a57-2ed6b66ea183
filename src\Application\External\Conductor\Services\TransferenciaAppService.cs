using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;

namespace SistemaInfo.BBC.Application.External.Conductor.Services
{
    public class TransferenciaAppService : ITransferenciaAppService
    {
        private ITransferenciaRepository _transferenciaRepository;
        
        public TransferenciaAppService(ITransferenciaRepository transferenciaRepository)
        {
            _transferenciaRepository = transferenciaRepository;
        }
    }
}