﻿using SistemaInfo.BBC.Application.Objects.Api.Documento;
using SistemaInfo.BBC.Application.Objects.Mobile.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Posto.Request;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivel;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivelProduto;
using SistemaInfo.BBC.Application.Objects.Web.PostoContato;
using SistemaInfo.BBC.Domain.Models.Documento;
using SistemaInfo.BBC.Domain.Models.Posto;
using SistemaInfo.BBC.Domain.Models.Posto.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel;
using SistemaInfo.BBC.Domain.Models.PostoCombustivelProduto;
using SistemaInfo.BBC.Domain.Models.PostoContato;
using SistemaInfo.BBC.Domain.Models.Usuario;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.PostoMapper
{
    public class PostoMappingProfile : SistemaInfoMappingProfile
    {
        public PostoMappingProfile()
        {
            CreateMap<Posto, PostoResponse>()
                .ForMember(d => d.StatusCadastro, opts => opts.MapFrom(s => s.StatusCadastro.GetHashCode()));

            CreateMap<PostoContato, ConsultarPostoContatoIdResponse>();
            
            CreateMap<PostoCombustivel, PostoCombustivelResponse>()
                .ForMember(d => d.ValorCombustivelBBC, opts => opts.MapFrom(s => s.ValorCombustivelBBC))
                .ForMember(d => d.ValorCombustivelBomba, opts => opts.MapFrom(s => s.ValorCombustivelBomba))
                .ForMember(d => d.ValorDesconto, opts => opts.MapFrom(s => s.ValorCombustivelBBC - s.ValorCombustivelBomba))
                .ForMember(d => d.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(d => d.UnidadeMedida, opts => opts.MapFrom(s => s.Combustivel.UnidadeMedida));

            CreateMap<PostoCombustivelProduto, PostoCombustivelProdutoResponse>()
                .ForMember(d => d.CombustivelId, opts => opts.MapFrom(s => s.Combustivel.Id))
                .ForMember(d => d.PostoId, opts => opts.MapFrom(s => s.Posto.Id))
                .ForMember(d => d.CodigoProduto, opts => opts.MapFrom(s => s.CodigoProduto))
                .ForMember(d => d.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome));

            CreateMap<Documento, DocumentoResponse>();

            CreateMap<Posto, ConsultarPostoGrid>()
                .ForMember(dest => dest.StatusCadastro, opt => opt.MapFrom(src => src.StatusCadastro.GetHashCode()))
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => FormatUtils.CpfCnpj(src.Cnpj)))
                .ForMember(dest => dest.FarolSla, opt => opt.MapFrom(src => src.FarolSla.GetHashCode()));
     
            CreateMap<PostoRequest, PostoSaveComRetornoCommand>();
            
            CreateMap<PostoCombustivelProdutoRequest, PostoCombustivelProduto>();
            
            CreateMap<Usuario, PostoAutenticacaoViewModel>();
            
            CreateMap<Usuario, PostoLogadoViewModel>()
                .ForMember(d => d.IdPosto, opts => opts.MapFrom(s => s.PostoId))
                .ForMember(d => d.NomePosto, opts => opts.MapFrom(s => s.Nome))
                .ForMember(d => d.GrupoUsuario, opts => opts.MapFrom(s => s.GrupoUsuarioId))
                .ForMember(d => d.Ativo, opts => opts.MapFrom(s => s.Ativo));

            CreateMap<Posto, PostoLogadoViewModel>()
                .ForMember(d => d.IdPosto, opts => opts.MapFrom(s => s.Id))
                .ForMember(d => d.NomePosto, opts => opts.MapFrom(s => s.NomeFantasia))
                .ForMember(d => d.RazaoSocial, opts => opts.MapFrom(s => s.RazaoSocial))
                .ForMember(d => d.Ativo, opts => opts.MapFrom(s => s.Ativo));
            
            CreateMap<Posto, PostoAutenticacaoViewModel>()
                .ForPath(d => d.posto.IdPosto, opts => opts.MapFrom(s => s.Id))
                .ForPath(d => d.posto.NomePosto, opts => opts.MapFrom(s => s.NomeFantasia))
                .ForPath(d => d.posto.RazaoSocial, opts => opts.MapFrom(s => s.RazaoSocial))
                .ForPath(d => d.posto.Ativo, opts => opts.MapFrom(s => s.Ativo));

            //api mobile response tranferencia de informacao
            CreateMap<PostoResponse, ConsultaPostosIdApiResponse>()
                .ForPath(d => d.PossuiBorracharia, opts => opts.MapFrom(s => s.PossuiBorracharia == 1 ? true : false))
                .ForPath(d => d.PossuiComputador, opts => opts.MapFrom(s => s.PossuiComputador == 1 ? true : false))
                .ForPath(d => d.PossuiRestaurante, opts => opts.MapFrom(s => s.PossuiRestaurante == 1 ? true : false))
                .ForPath(d => d.PossuiVestiario, opts => opts.MapFrom(s => s.PossuiVestiario == 1 ? true : false))
                .ForPath(d => d.PossuiAreaDescanso, opts => opts.MapFrom(s => s.PossuiAreaDescanso == 1 ? true : false))
                .ForPath(d => d.PossuiVeiculoApoio, opts => opts.MapFrom(s => s.PossuiVeiculoApoio == 1 ? true : false))
                .ForPath(d => d.PostoCombustiveisMobiles, opts => opts.MapFrom(s => s.PostoCombustiveis));

            CreateMap<PostoCombustivelResponse, PostoCombustiveisMobile>()
                .ForPath(d => d.ValorBomba, opts => opts.MapFrom(s => s.ValorCombustivelBomba ?? (0.00).ToDecimal()))
                .ForPath(d => d.ValorBBC, opts => opts.MapFrom(s => s.ValorCombustivelBBC ?? (0.00).ToDecimal()))
                .ForPath(d => d.Nome, opts => opts.MapFrom(s => s.NomeCombustivel));

            CreateMap<PostoRequest, PostoSaveCommand>();
            
            CreateMap<PostoRequest, PostoAlterarStatusCommand>();
            
            CreateMap<Posto, PostoAprovarCredenciamentoCommand>();
            
            CreateMap<Posto, PostoReprovarCredenciamentoCommand>();
            
            CreateMap<Posto, PostoSaveCommand>();
            
            CreateMap<Posto, PostoSaveComRetornoCommand>();

            CreateMap<PostoRequest, PostoSaveComRetornoCommand>()
                .ForMember(x => x.Documentos, opts => opts.MapFrom(d => d.Documentos))
                .ForMember(x => x.PostoCombustiveis, opts => opts.MapFrom(d => d.PostoCombustiveis))
                .ForMember(x => x.PostoContatos, opts => opts.MapFrom(d => d.PostoContatos))
                .ForMember(x => x.PostoCombustivelProduto, opts => opts.MapFrom(d => d.PostoCombustivelProduto));;

            CreateMap<PostoContatoRequest, PostoContato>();
            
            CreateMap<PostoCombustivelRequest, PostoCombustivel>();
            
            CreateMap<DocumentoRequest, Documento>()
                .ForMember(x => x.DataCadastro, opts => opts.MapFrom(d => d.DataCadastro.ToDateTime()));
            
            CreateMap<DocumentoCommand, Documento>()
                .ForMember(x => x.DataCadastro, opts => opts.MapFrom(d => d.DataCadastro.ToDateTime()))
                .ForMember(x => x.DocumentosProcessoVinculadoId, opts => opts.MapFrom(d => d.DocumentosProcessoVinculadoId))
                .ForMember(x => x.Tipo, opts => opts.MapFrom(d => d.Tipo));

            CreateMap<Documento, DocumentoCommand>()
                .ForMember(x => x.Status, opts => opts.MapFrom(d => d.Status.GetHashCode()))
                .ForMember(x => x.Tipo, opts => opts.MapFrom(d => d.Tipo.GetHashCode()));
            
            CreateMap<DocumentoRequest, DocumentoCommand>()
                .ForMember(x => x.DataCadastro, opts => opts.MapFrom(d => d.DataCadastro.ToDateTime()));
            
            CreateMap<PostoSaveCommand, Posto>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo))
                .ForMember(d => d.Senha, opts => opts.MapFrom(s => s.Senha));

            CreateMap<PostoSaveComRetornoCommand, Posto>()
                
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo))
                .ForMember(d => d.Senha, opts => opts.MapFrom(s => s.Senha));

            CreateMap<PostoStatusRequest, PostoAlterarStatusCommand>();
        }
    }
}