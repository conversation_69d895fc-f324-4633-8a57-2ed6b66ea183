:: linux dotnet publish "Cartao.Api.csproj" -c Release -o "/home/<USER>/SIE/Versao/Cartao" -f netcoreapp2.0

@echo off

:: Forçar mudança de diretório atual para o atual do arquivo .bat
cd /D "%~dp0"

echo EXECUTAR COMO ADMINISTRADOR - EXPORTAR E ATUALIZAR IIS
echo ---------------------------

set outputdir=C:\Versao\MicroService\Cartoes
set poolPrefix=
set iisRootDir=

echo Diretorio de exportacao: %outputdir%
echo ---------------------------

:: SVN Update
::set /p svnupdate= SVN Update [S/N] (Default=S): 
if "%svnupdate%"=="" (set svnupdate=S)
if "%svnupdate%"=="S" (
	svn update ..\..\..\
	echo Atualizacao de fontes concluida
	echo ---------------------------	
)
echo.

:: Escolhendo projeto para exportar
echo PROJETOS
echo 0-Todos
echo 1-Cartao.Api
echo 2-Cartao.Web
echo 3-Cartao.Service
echo 4-Processadora.Biz.Service
::set /p opcao="Opcao para atualizar (Default=0-Todos): "
if "%opcao%"=="" (set opcao=0)

::set /p iis="Atualizar IIS [S/N] (Default=S): "
if "%iis%"=="" (set iis=S)

::set /p poolPrefix="Prefixo Pool [MS.Local/MS.Dev/MS.Hom/MS.Prd] (Default=MS.Hml): "
if "%poolPrefix%"=="" (set poolPrefix=MS.Hml)

::set /p iisRootDir="Root App Folder (Default=C:\IIS\%poolPrefix%): "
if "%iisRootDir%"=="" (set iisRootDir=C:\IIS\%poolPrefix%)

echo ---------------------------
echo Opcao: %opcao%
set api=false
set web=false
set service=false
set processadoraBizVisaCargo=false

if %opcao%==0 (
	set api=true
	set web=true
	set service=true
	set processadoraBizVisaCargo=true)
if %opcao%==1 set api=true
if %opcao%==2 set web=true
if %opcao%==3 set service=true
if %opcao%==4 set processadoraBizVisaCargo=true

echo Api: %api%
echo Web: %web%
echo Services: %service%
echo Processadora - Biz Visa Cargo: %processadoraBizVisaCargo%
echo.
echo Atualizar IIS: %iis%
echo Prefixo Pool: %poolPrefix%
echo Pasta IIS: %iisRootDir%

:: Excluíndo arquivos atualmente na pasta destino da publicação
echo ---------------------------
echo Limpando diretorio
if %api%==true (@RD /S /Q "%outputdir%\Api")
if %web%==true (@RD /S /Q "%outputdir%\Web")
if %service%==true (@RD /S /Q "%outputdir%\Services")
if %processadoraBizVisaCargo%==true (@RD /S /Q "%outputdir%\Processadoras")

:: Publicar projetos como release
echo ---------------------------
echo Exportando projetos
if %api%==true (dotnet publish "..\Cartao.Api\Cartao.Api.csproj" -c Release -o "%outputdir%\Api" -f netcoreapp2.0)
if %web%==true (dotnet publish "..\Cartao.Web\Cartao.Web.csproj" -c Release -o "%outputdir%\Web" -f netcoreapp2.0)
if %service%==true (dotnet publish "..\Cartao.Service\Cartao.Service.csproj" -c Release -o "%outputdir%\Services" -f netcoreapp2.0)
if %processadoraBizVisaCargo%==true (dotnet publish "..\Processadora.Biz.Service\Processadora.Biz.Service.csproj" -c Release -o "%outputdir%\Processadoras\BizVisaCargo" -f netcoreapp2.0)

:: Remover arquivos appsettings.json
echo ---------------------------
echo Excluindo AppSettings*.json
del /s /q /f %outputdir%\appsettings*.json
del /s /q /f %outputdir%\web*.config

:: Atualizando IIS
if %iis%==S (
	echo Atualizando IIS
	
	c:
	cd c:\Windows\System32\inetsrv
	
	if %api%==true (
		appcmd stop apppool /apppool.name:"%poolPrefix%.Cartoes.Api"
		xcopy "%outputdir%\Api" "%iisRootDir%\Cartoes\Api" /s/h/e/k/f/c/y
		appcmd start apppool /apppool.name:"%poolPrefix%.Cartoes.Api"
	)	
	
	if %web%==true (
		appcmd stop apppool /apppool.name:"%poolPrefix%.Cartoes.Web"
		xcopy "%outputdir%\Web" "%iisRootDir%\Cartoes\Web" /s/h/e/k/f/c/y
		appcmd start apppool /apppool.name:"%poolPrefix%.Cartoes.Web"
	)
	
	if %service%==true (
		appcmd stop apppool /apppool.name:"%poolPrefix%.Cartoes.Services"
		xcopy "%outputdir%\Services" "%iisRootDir%\Cartoes\Services" /s/h/e/k/f/c/y
		appcmd start apppool /apppool.name:"%poolPrefix%.Cartoes.Services"
	)
	
	if %processadoraBizVisaCargo%==true (
		appcmd stop apppool /apppool.name:"%poolPrefix%.Cartoes.Processadoras.BizVisaCargo"
		xcopy "%outputdir%\Processadoras\BizVisaCargo" "%iisRootDir%\Cartoes\\Processadoras\BizVisaCargo" /s/h/e/k/f/c/y
		appcmd start apppool /apppool.name:"%poolPrefix%.Cartoes.Processadoras.BizVisaCargo"
	)
)

echo ---------------------------
echo Processo concluido

::pause
