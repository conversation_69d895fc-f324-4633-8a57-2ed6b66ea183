﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.DeclaracaoCiot;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("ServicosCiot")]
    public class ServicosCiotController : ApiControllerBase
    {
        private readonly IDeclaracaoCiotAppService _declaracaoCiotAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="declaracaoCiotAppService"></param>
        public ServicosCiotController(IAppEngine engine, 
            IDeclaracaoCiotAppService declaracaoCiotAppService) 
            : base(engine)
        {
            _declaracaoCiotAppService = declaracaoCiotAppService;
        }
        
        
        /// <summary>
        /// BAT_CIOT_01: Realiza o encerramento de declarações de CIOT vencidas do tipo Agregado.
        /// Deve ser executado a cada 5 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("EncerrarCiots")]
        public async Task EncerrarCiots()
        {
            await _declaracaoCiotAppService.ServiceEncerrarCiots();
        }
        
        /// <summary>
        /// BAT_CIOT_02: Realiza o cancelamento de declarações de CIOT
        /// Deve ser executado a cada 5 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("CancelarCiots")]
        public async Task CancelarCiots()
        {
            await _declaracaoCiotAppService.ServiceCancelarCiots();
        }
    }
}