using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Api.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Documento;
using SistemaInfo.BBC.Application.Objects.Api.Documento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Api.Controllers
{
    /// <summary>
    /// Class composta por metodos relacionados a documento
    /// </summary>
    [Route("Documento")]
    public class DocumentoController : ApiControllerBase
    {
        private readonly IDocumentoAppService _documentoAppService;
        
        /// <summary>
        /// Injeção de dependencias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="documentoAppService"></param>
        public DocumentoController(IAppEngine engine, IDocumentoAppService documentoAppService) : base(engine)
        {
            _documentoAppService = documentoAppService;
        }
        
        /// <summary>
        /// metodo responsavel por envio da imagem do documento
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("EnviarImagem")]
        public JsonResult EnviarImagem([FromBody] DocumentoRequest request)
        {
            try
            {
                var lImagens = _documentoAppService.EnviarImagem(request).Result;

                if (lImagens.Sucesso)
                {
                    return ResponseBase.ResponderSucesso(lImagens); 
                }
                
                return ResponseBase.ResponderErro(lImagens.message);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro(e.Message);
            }
        }
    }
}