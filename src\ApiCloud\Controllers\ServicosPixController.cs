﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("ServicosPix")]
    public class ServicosPixController : ApiControllerBase
    {
        private readonly IPixAppService _pixAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        public ServicosPixController(IAppEngine engine, 
            IPixAppService pixAppService) 
            : base(engine)
        {
            _pixAppService = pixAppService;
        }
        
        /// <summary>
        /// BAT_PIX_01: Consulta os Pix pendentes para verificar se foram processados e atualizar seu status.
        /// Deve ser executado a cada 30 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("ConsultarStatusPix")]
        public async Task ConsultarStatusPix()
        {
            await _pixAppService.ServiceConsultarStatusPix();
        }
    }
}