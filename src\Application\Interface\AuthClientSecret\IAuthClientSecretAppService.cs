using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AuthClientSecret;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.AuthClientSecret
{
    public interface IAuthClientSecretAppService : IAppService<Domain.Models.AuthClientSecret.AuthClientSecret, IAuthClientSecretReadRepository,
                      IAuthClientSecretWriteRepository>    
    {
        Task<RespPadrao> ConsultarPorId(int idAuthClientSecret);
        Task<RespPadrao> ConsultarGridAuthClientSecret(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);
        Task<RespPadrao> SaveAuthClientSecret(AuthClientSecretRequest lModel);    
        Task<RespPadrao> AlterarStatus(AuthClientSecretAlterarStatusRequest lModel);    
    }
}
