﻿using SistemaInfo.BBC.Application.Objects.Web.BloqueioSpd;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.BloqueioSpdMapper
{
    public class BloqueioSpdMappingProfile : SistemaInfoMappingProfile
    {
        public BloqueioSpdMappingProfile()
        {
            CreateMap<BloqueioSpdSaveCommand, BloqueioSpdSaveComRetornoCommand>();

            CreateMap<BloqueioSpdRequest, BloqueioSpdSaveComRetornoCommand>();

            CreateMap<BloqueioSpdStatusRequest, BloqueioSpdAlterarStatusCommandComRentorno>();
            
            CreateMap<BloqueioSpdRequest, BloqueioSpdSaveCommand>();
            
            CreateMap<BloqueioSpdSaveCommand, BloqueioSpd>();
        }
    }
}