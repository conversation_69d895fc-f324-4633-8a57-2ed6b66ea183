using System;
using System.Threading.Tasks;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret.Repository;
using SistemaInfo.Framework.CQRS;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.CQRS;

namespace SistemaInfo.BBC.Domain.Models.AuthClientSecret.Commands.CommandHandlers
{
    public class AuthClientSecretCommandHandler : CommandHandler<AuthClientSecret,
            IAuthClientSecretReadRepository, IAuthClientSecretWriteRepository>,
        I<PERSON>and<PERSON><AuthClientSecretAdicionarCommand>,
        <PERSON><PERSON>andler<AuthClientSecretEditarCommand>,
        <PERSON><PERSON><PERSON><PERSON><AuthClientSecretAlterarStatusCommand, AuthClientSecret>
    {
        public IAuthClientSecretReadRepository AuthClientSecretReadRepository { get; }
        public IAuthClientSecretWriteRepository AuthClientSecretWriteRepository { get; }

        public AuthClientSecretCommandHandler(IAppEngine engine, IAuthClientSecretReadRepository readRepository,
            IAuthClientSecretWriteRepository writeRepository) : base(engine, readRepository, writeRepository)
        {
            AuthClientSecretReadRepository = readRepository;
            AuthClientSecretWriteRepository = writeRepository;
        }

        public async Task HandlerAsync(AuthClientSecretAdicionarCommand command)
        {
            if(command == null || command.Id == 0)
            {
                var lAuthClientSecret = Mapper.Map<AuthClientSecret>(command);
                await Repository.Command.AddAsync(lAuthClientSecret);
            }
            
            await SaveChangesAsync();        
        }
        
        public async Task HandlerAsync(AuthClientSecretEditarCommand command)
        {
            var lAuthClientSecret = Repository.Query.FirstOrDefault(x => x.Id == command.Id);
            
            lAuthClientSecret.DataAlteracao = DateTime.Now;
            lAuthClientSecret.UsuarioAlteracaoId = Engine.User.Id;
            lAuthClientSecret.Login = command.Login;
            lAuthClientSecret.Senha = string.IsNullOrWhiteSpace(command.Senha) ? lAuthClientSecret.Senha : command.Senha;
            lAuthClientSecret.ClientSecret = command.ClientSecret;
            lAuthClientSecret.Descricao = command.Descricao;
            
            Repository.Command.Update(lAuthClientSecret);
            await SaveChangesAsync();
        }

        public async Task<AuthClientSecret> HandlerAsync(AuthClientSecretAlterarStatusCommand command)
        {
            var lAuthClientSecret = Repository.Query.FirstOrDefault(x => x.Id == command.Id);
            
            lAuthClientSecret.DataAlteracao = DateTime.Now;
            lAuthClientSecret.UsuarioAlteracaoId = Engine.User.Id;
            lAuthClientSecret.Ativo = command.Ativo;
            
            Repository.Command.Update(lAuthClientSecret);
            await SaveChangesAsync();
            
            return lAuthClientSecret;
        }
    }
}
