﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Web: SistemaInfo.BBC.Web.Controle" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/Web/Web.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value=".NETCoreApp,Version=v2.0" />
    <option name="LAUNCH_PROFILE_NAME" value="SistemaInfo.BBC.Web" />
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
      <option name="GulpBeforeRunTask" enabled="true">
        <node-interpreter>project</node-interpreter>
        <node-options />
        <gulpfile>$PROJECT_DIR$/Front/gulpfile.js</gulpfile>
        <tasks>
          <task>serve</task>
        </tasks>
        <arguments />
        <envs />
      </option>
    </method>
  </configuration>
</component>