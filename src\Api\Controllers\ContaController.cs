using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Api.Controllers.Base;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Api.Controllers
{
	/// <summary>
	/// class conposta de todos os metodos utilizados pela aplicação de conta
	/// </summary>
	[Route("Conta")]
	public class ContaController : ApiControllerBase
	{
		private readonly ICartaoAppService _cartaoAppService;
		private readonly IPortadorAppService _portadorAppService;

		/// <summary>
		/// Injeção de dependeicas e heranças
		/// </summary>
		/// <param name="engine"></param>
		/// <param name="cartaoAppService"></param>
		/// <param name="portadorAppService"></param>
		public ContaController(IAppEngine engine, ICartaoAppService cartaoAppService,
			IPortadorAppService portadorAppService) : base(engine)
		{
			_cartaoAppService = cartaoAppService;
			_portadorAppService = portadorAppService;
		}

		/// <summary>
		/// Consulta de agencia vinculada a conta
		/// </summary>
		/// <param name="CpfCnpjPortador"></param>
		/// <returns></returns>
		[Produces("application/json")]
		[HttpPost("ConsultarContaAgencia")]
		public JsonResult ConsultarContaAgencia([FromBody] string CpfCnpjPortador)
		{
			try
			{
				if (CpfCnpjPortador != null)
				{
					var retornoConta = _cartaoAppService.ConsultarContaAgencia(CpfCnpjPortador).Result;

					if (retornoConta.Sucesso)
					{
						return ResponseBase.ResponderSucesso(retornoConta);
					}
				}

				return ResponseBase.ResponderErro("Não foi encontrado conta para esse CPF/CNPJ.");
			}
			catch (Exception e)
			{
				return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
			}
		}

		/// <summary>
		/// Consulta de contas utilizado pela aplicação conta
		/// </summary>
		/// <param name="cpfcnpj"></param>
		/// <returns></returns>
		[Produces("application/json")]
		[HttpPost("Consultar")]
		public JsonResult ConsultarContas([FromBody] string cpfcnpj)
		{
			try
			{
				var lContas = _cartaoAppService.ConsultarContas(null, null, null, null, cpfcnpj);


				List<string> listContas = lContas?.Result.content.Select(x => x.id.ToString()).ToList();

				return ResponseBase.ResponderSucesso(listContas);
			}
			catch (Exception e)
			{
				return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
			}
		}

		/// <summary>
		/// adição de cartão a conta
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[Produces("application/json")]
		[HttpPost("AdicionarCartao")]
		public JsonResult AdicionarCartao([FromBody] PortadorRequest request)
		{
			try
			{
				var response = _portadorAppService.AdicionarCartao(request).Result;
				return !response.sucesso
					? ResponseBase.ResponderErro(response.mensagem)
					: ResponseBase.ResponderSucesso(response);
			}
			catch (Exception e)
			{
				return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
			}
		}

		/// <summary>
		/// consulta cartoes vinculados a conta
		/// </summary>
		/// <param name="idConta"></param>
		/// <returns></returns>
		[Produces("application/json")]
		[HttpPost("ConsultaCartao")]
		public JsonResult ConsultaCartao([FromBody] int idConta)
		{
			try
			{
				var response = _cartaoAppService.ConsultaCartaoPorConta(idConta).Result;

				var content = response.content != null
					? response.content.FirstOrDefault(c => c.idStatus == 1 || c.idStatus == 2)
					  ?? response.content.FirstOrDefault(c => c.idStatus == 6)
					: null;

				return response.content == null || !response.Sucesso
					? ResponseBase.ResponderErro(response.message)
					: ResponseBase.ResponderSucesso(content);
			}
			catch (Exception e)
			{
				return ResponseBase.ResponderErro(e.Message);
			}
		}

		/// <summary>
		/// Consulta de status de contas
		/// </summary>
		/// <param name="CpfCnpjPortador"></param>
		/// <returns></returns>
		[Produces("application/json")]
		[HttpPost("ConsultarStatus")]
		public JsonResult ConsultarStatus([FromBody] string CpfCnpjPortador)
		{
			try
			{
				if (CpfCnpjPortador != null)
				{
					var retornoStatus = _cartaoAppService.ConsultarStatus(CpfCnpjPortador).Result;

					if (retornoStatus.Sucesso)
					{
						return ResponseBase.ResponderSucesso(retornoStatus);
					}
				}

				return ResponseBase.ResponderErro("Não foi possível consultar situação para esse CPF/CNPJ.");

			}
			catch (Exception e)
			{
				return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
			}
		}
	}
}
