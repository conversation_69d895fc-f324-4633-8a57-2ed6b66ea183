﻿using SistemaInfo.BBC.Application.Objects.Web.Mensagem;
using SistemaInfo.BBC.Domain.Models.Mensagem;
using SistemaInfo.BBC.Domain.Models.Mensagem.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.MensagemMapper
{
    public class MensagemMappingProfile : SistemaInfoMappingProfile
    {
        public MensagemMappingProfile()
        {
            CreateMap<MensagemRequest, MensagemSalvarCommand>()
                .ForMember(dest => dest.CodigoAplicacao, opts => opts.MapFrom(p => p.CodigoAplicacao));
            
            CreateMap<MensagemRequest, MensagemAlterarStatusCommand>()
                .ForMember(dest => dest.CodigoAplicacao, opts => opts.MapFrom(p => p.CodigoAplicacao));

            CreateMap<MensagemRequest, MensagemSalvarComRetornoCommand>()
                .ForMember(dest => dest.CodigoAplicacao, opts => opts.MapFrom(p => p.CodigoAplicacao));
            
            CreateMap<MensagemRequest, MensagemAdicionarComRetornoCommand>()
                .ForMember(dest => dest.CodigoAplicacao, opts => opts.MapFrom(p => p.CodigoAplicacao));
    
            CreateMap<MensagemSalvarCommand, Mensagem>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<MensagemSalvarComRetornoCommand, Mensagem>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));
            
            CreateMap<MensagemAdicionarComRetornoCommand, Mensagem>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));
            
            CreateMap<MensagemStatusRequest, MensagemAlterarStatusCommand>();

            CreateMap<Mensagem, ConsultarGridMensagem>()
                .ForMember(dest => dest.DataInicioMensagem, opts => opts.MapFrom(s => s.DataInicioMensagem.ToStringBr(FormatDateTimeMethod.ShortDate)))
                .ForMember(dest => dest.DataFimMensagem, opts => opts.MapFrom(s => s.DataFimMensagem.ToStringBr(FormatDateTimeMethod.ShortDate)));
        }
    }
}