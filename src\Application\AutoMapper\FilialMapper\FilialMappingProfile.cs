﻿using SistemaInfo.BBC.Application.Objects.Api.Filial;
using SistemaInfo.BBC.Application.Objects.Web.Filial;
using SistemaInfo.BBC.Application.Objects.Web.FilialCentroCusto;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.CentroCusto;
using SistemaInfo.BBC.Domain.Models.Filial;
using SistemaInfo.BBC.Domain.Models.Filial.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.FilialMapper
{
    public class FilialMappingProfile : SistemaInfoMappingProfile
    {
        public FilialMappingProfile()
        {
            CreateMap<FilialRequest, FilialSaveCommand>();
            
            CreateMap<FilialSaveCommand, Filial>();

            CreateMap<FilialRequest, FilialSaveComRetornoCommand>();

            CreateMap<FilialStatusRequest, FilialAlterarStatusCommand>();
            
            CreateMap<FilialCentroCustoRequest, CentroCusto>();

            CreateMap<Filial, ConsultarGridFilial>()
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => FormatUtils.CpfCnpj(src.Cnpj)))
                .ForMember(dest => dest.Telefone,
                    opt => opt.MapFrom(src => src.Telefone.ToTelefoneFormato()));

            CreateMap<FilialIntegrarApiRequest, FilialRequest>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.Id) ? "0" : src.Id));

            CreateMap<FilialParaEmpresaRequest, FilialSaveCommand>();

            CreateMap<FilialSaveCommand, FilialSaveComRetornoCommand>();
            
            CreateMap<CentroCusto, FilialCentroCustoResp>()
                .ForMember(dest => dest.CentroCustoId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Descricao, opt => opt.MapFrom(src => src.Descricao));

            CreateMap<Filial, FilialResponse>()
                .ForMember(dest => dest.EstadoId, opt => opt.MapFrom(src => src.Cidade.Estado.Id));
        }
    }
}