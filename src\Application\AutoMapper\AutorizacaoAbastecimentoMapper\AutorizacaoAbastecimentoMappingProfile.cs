﻿using SistemaInfo.BBC.Application.Objects.Web.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.AutorizacaoAbastecimentoMapper
{
    public class AutorizacaoAbastecimentoMappingProfile : SistemaInfoMappingProfile
    {
        public AutorizacaoAbastecimentoMappingProfile()
        {
            CreateMap<AutorizacaoAbastecimento, ConsultarGridAutorizacaoAbastecimento>()
                .ForMember(dest => dest.dataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.placa, opts => opts.MapFrom(s => s.Veiculo.Placa))
                .ForMember(dest => dest.combustivelNome, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.status, opts => opts.MapFrom(s => s.Status.GetHashCode()))
                .ForMember(dest => dest.usuarioCadastro, opts => opts.MapFrom(s =>
                    s.IdentificadorOrigem == null || s.IdentificadorOrigem.Equals(EIdentificadorOrigemAutorizacaoAbastecimento.BBCControle)
                        ? s.UsuarioCadastro.Nome + " / " + (string.IsNullOrWhiteSpace(s.Empresa.RazaoSocial) ? s.Empresa.RazaoSocial : s.Empresa.NomeFantasia)
                        : string.IsNullOrWhiteSpace(s.Empresa.RazaoSocial) ? s.Empresa.RazaoSocial : s.Empresa.NomeFantasia))
                .ForMember(dest => dest.litragemAbastecida, opts => opts.MapFrom(s => s.LitragemUtilizada))
                .ForMember(dest => dest.litragemDisponivel, opts => opts.MapFrom(s => s.Litragem));

            CreateMap<AutorizacaoAbastecimento, AutorizacaoAbastecimentoResponse>()
                .ForMember(dest => dest.Ativo, opts => opts.MapFrom(s => s.Status == StatusAutorizacaoAbastecimento.Aberto));

            CreateMap<AutorizacaoAbastecimentoRequest, AutorizacaoAbastecimentoSalvarCommand>();

            CreateMap<AutorizacaoAbastecimentoRequest, AutorizacaoAbastecimentoSalvarComRetornoCommand>();

            CreateMap<AutorizacaoAbastecimentoSalvarCommand, AutorizacaoAbastecimento>();

            CreateMap<AutorizacaoAbastecimentoSalvarComRetornoCommand, AutorizacaoAbastecimento>();
        }
    }
}