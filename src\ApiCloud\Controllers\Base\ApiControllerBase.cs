﻿using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Controllers;

namespace SistemaInfo.BBC.ApiCloud.Controllers.Base
{
    /// <inheritdoc />
    public class ApiControllerBase : SistemaController
    {
        /// <inheritdoc />
        public ApiControllerBase(IAppEngine engine) : base(engine)
        {
        }
    }

    /// <inheritdoc />
    public class ApiControllerBase<TAppService> : SistemaController<TAppService>
    {
        /// <inheritdoc />
        public ApiControllerBase(IAppEngine engine, TAppService appService) : base(engine, appService)
        {
        }
    }
}