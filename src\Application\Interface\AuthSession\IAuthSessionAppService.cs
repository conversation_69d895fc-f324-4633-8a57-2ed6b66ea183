﻿using System.Threading.Tasks;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Usuario;
using SistemaInfo.BBC.Domain.Models.AuthSession.Repository;

namespace SistemaInfo.BBC.Application.Interface.AuthSession
{
    public interface
        IAuthSessionAppService : IAppService<Domain.Models.AuthSession.AuthSession, IAuthSessionReadRepository, IAuthSessionWriteRepository>
    {
        Task<RespPadrao> GerarTokenPosto(PostoLoginRequest request);
        Task<RespPadrao> GerarToken(UsuarioLoginRequest request);
        RespPadrao GetByToken(string token);
        Domain.Models.AuthSession.AuthSession GetAuthSessionByToken(string token);
        Task<RespPadrao> UpdateUltimaReq(string token);
        Task<RespPadrao> SaveTokenControle(int id, string token);
        Task<RespPadrao> SaveTokenMobile(int idPortador, string token);
        Task<RespPadrao> SaveTokenPosto(int idPosto, string token, int idUsuario = 1);
    }
}