using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Api.Documento
{
    public class DocumentoRequest
    {
        public int Id { get; set; }
        public string CpfCnpjPortador { get; set; }
        public List<Imagem> Imagens { get; set; }
        public List<DocumentosRepLegal> DocumentosRepLegais { get; set; }
        public int? IdPortador { get; set; }
        
        public string Descricao { get; set; }
        public string Foto { get; set; }

        public int? Status { get; set; }
        public string Motivo { get; set; }
        
        public int Tipo { get; set; }
        public string DataCadastro { get; set; }
        
        public int Acao { get; set; }
        
        public int? DocumentosProcessoVinculadoId { get; set; }
        
    }
    
    public class DocumentosRepLegal
    {
        public string CpfCnpj { get; set; }
        public List<Imagem> Imagens { get; set; }
    }

    public class Imagem
    {
        public string Foto { get; set; }
        public int TipoImagem { get; set; }
    }
}