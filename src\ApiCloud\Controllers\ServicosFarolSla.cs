﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Posto;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    /// <summary>
    /// class conposta de todos os metodos utilizados pela aplicação de conta
    /// </summary>
    [Route("ServicosFarolSla")]
    public class ServicosFarolSlaController : ApiControllerBase
    {
        private readonly IPostoAppService _postoAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="postoAppService"></param>
        public ServicosFarolSlaController(IAppEngine engine, 
            IPostoAppService postoAppService) 
            : base(engine)
        {
            _postoAppService = postoAppService;
        }
        
        
        /// <summary>
        /// BAT_FSLA_01: Realiza o envio dos emails de aviso sobre os prazos das validações de
        /// credenciamento de estabelecimento comercial.
        /// Deve ser executado a cada 1 hora.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("EnviarEmailNotificacaoFarolSla")]
        public async Task EnviarEmailNotificacaoFarolSla()
        {
            await _postoAppService.ServiceEnviarEmailNotificacaoFarolSla();
        }
        
        /// <summary>
        /// BAT_FSLA_02: Executa o envio de notificações de farol sla credenciamento.
        /// Deve ser executado a cada 5 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("EnviarEmailControleCredenciamentoFarolSla")]
        public async Task EnviarEmailControleCredenciamentoFarolSla()
        {
            await _postoAppService.ServiceEnviarEmailControleCredenciamentoFarolSla();
        }
        
    }
}