﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PercentualTransferencia;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.PercentualTransferencia.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.PercentualTransferencia
{
    public interface IPercentualTransferenciaAppService 
        : IAppService<Domain.Models.PercentualTransferencia.PercentualTransferencia, 
            IPercentualTransferenciaReadRepository, 
            IPercentualTransferenciaWriteRepository>
    {
        Task<ConsultarGridPercentualTransferenciaResponse> ConsultarGridPercentualTransferencia(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridPercentualTransferenciaPortadorResponse> ConsultarGridMotoristasProprietario(int proprietarioId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridPortadorReduzidoResponse> ConsultarGridMotoristasCombo(int proprietarioId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridPercentualTransferenciaPortadorResponse> ConsultarGridMotoristasComHistoricos(int proprietarioId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridPercentualTransferenciaPortadorHistoricoResponse> ConsultarGridHistoricoPorMotorista(int percentualTransferenciaPortadorId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridPercentualTransferenciaHistoricoResponse> ConsultarGridHistoricosDoPercentual(int proprietarioId, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<PercentualTransferenciaResponse> ConsultarPercentualTransferenciaPorId(int proprietarioId);
        Task<RespPadrao> AdicionarMotorista(AdicionarMotoristaPercentualTransferenciaRequest request);
        Task<RespPadrao> Save(PercentualTransferenciaRequest request);
        Task<RespPadrao> SavePercentualTransferenciaPortador(PercentualTransferenciaPortadorRequest request);
        Task<RespPadrao> AlterarStatusPercentualTransferenciaPortador(PercentualTransferenciaPortadorAlterarStatusRequest request);
    }
}