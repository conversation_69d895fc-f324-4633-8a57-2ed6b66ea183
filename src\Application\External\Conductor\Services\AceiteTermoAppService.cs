using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Interface.AceiteTermo;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.AceiteTermo;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;

namespace SistemaInfo.BBC.Application.External.Conductor.Services
{
    public class AceiteTermoAppService : IAceiteTermoAppService
    {
        private IAceiteTermoRepository _aceiteTermoRepository;
         
        public AceiteTermoAppService(IAceiteTermoRepository aceiteTermoRepository)
        {
            _aceiteTermoRepository = aceiteTermoRepository;
        }
        public Task<AceiteTermoResponse> ConsultarTermos()
        {
            return _aceiteTermoRepository.ConsultarAceiteTermos();
        }
    }
}