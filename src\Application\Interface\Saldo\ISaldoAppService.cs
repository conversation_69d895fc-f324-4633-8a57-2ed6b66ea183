﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Saldo;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Saldo;

public interface ISaldoAppService : IAppService
{
    Task<PagamentoDiaGraficoAngular> GetPagamentosGrafico(int empresaId, DateTime? dataBase);
    Task<List<TotalizadorItem>> GetPagamentosPorDia(int empresaId, DateTime? dataBase);
    Task<List<ResumoDiaLinha>> GetResumoDia(int empresaId, DateTime? dataBase);
    Task<List<EmpresaHabilitadosResponse>> GetEmpresaHabilitados();
    Task<RespPadrao> ConsultaParametrosConfiguracaoTelaoSaldo();

}