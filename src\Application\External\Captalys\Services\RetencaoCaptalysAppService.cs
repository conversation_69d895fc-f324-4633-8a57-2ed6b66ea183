using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using SistemaInfo.BBC.Application.External.Captalys.Interface;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.External.Captalys.Interface;
using RetencaoReq = SistemaInfo.BBC.Application.Objects.External.Captalys.RetencaoReq;

namespace SistemaInfo.BBC.Application.External.Captalys.Services
{
    public class RetencaoCaptalysAppService : IRetencaoCaptalysAppService
    {
        private IRetencaoCaptalysRepository _retencaoCaptalysRepository;

        public RetencaoCaptalysAppService(IRetencaoCaptalysRepository retencaoCaptalysRepository)
        {
            _retencaoCaptalysRepository = retencaoCaptalysRepository;
        }

        public async Task<RespPadrao> IntegrarRetencao(List<RetencaoReq> retencaoReq)
        {
            try
            {
                if (retencaoReq == null)
                {
                    throw new Exception("Não foi possível realizar esta operação.");
                }
                
                var lListRetencao = new List<Domain.External.Captalys.DTO.Retencao.RetencaoReq>();
                    
                foreach (var retencao in retencaoReq)
                {
                    var lRetReq = Mapper.Map<Domain.External.Captalys.DTO.Retencao.RetencaoReq>(retencao);
                        
                    lListRetencao.Add(lRetReq);
                }
                
                var response = await _retencaoCaptalysRepository.IntegrarRetencao(lListRetencao);

                return response == null
                    ? new RespPadrao {sucesso = false, mensagem = "Ocorreu um erro durante a integração."}
                    : new RespPadrao {sucesso = true, mensagem = "Integração de retenção efetuada com sucesso!"};
            }
            catch (Exception e)
            {
                return new RespPadrao()
                {
                    sucesso = false,
                    mensagem = e.Message
                };
            }
        }
    }
}