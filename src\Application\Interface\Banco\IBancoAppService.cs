using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Banco;
using SistemaInfo.BBC.Application.Objects.Web.Fabricante;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Banco.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Banco
{
    public interface IBancoAppService : IAppService<Domain.Models.Banco.Banco, IBancoReadRepository, IBancoWriteRepository>
    {
        ConsultarGridBancoResponse ConsultarGridBanco(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarGridBancoResponse ConsultarGridBancoComMDRCombo(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        BancoResponse ConsultarPorId(string idBanco);
        Task<RespPadrao> Save(BancoRequest lBancoReq, bool integracao = false);
        Task<RespPadrao> AlterarStatus(BancoStatusRequest request);
    }
}