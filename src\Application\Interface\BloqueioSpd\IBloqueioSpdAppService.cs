﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.BloqueioSpd;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.BloqueioSpd.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.BloqueioSpd
{
    public interface IBloqueioSpdAppService : IAppService<IBloqueioSpdReadRepository, IBloqueioSpdWriteRepository>
    {
        Task<RespPadrao> Save(BloqueioSpdRequest lBloqueioSpdReq);
        Task<RespPadrao>  AlterarStatus(BloqueioSpdStatusRequest lBloqueioSpdStatus);
        BloqueioSpdResponse ConsultarPorId(int idBloqueioSpd);
        ConsultarGridBloqueioSpdResponse ConsultarGridBloqueioSpd(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
    }
}