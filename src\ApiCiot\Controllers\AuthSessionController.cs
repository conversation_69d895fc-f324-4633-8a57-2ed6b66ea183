using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using SistemaInfo.BBC.ApiCiot.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.AuthSessionApi;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;

namespace SistemaInfo.BBC.ApiCiot.Controllers
{
    /// <summary>
    /// Class de authsession dispoem de todos os metodos responsaveis pela auth do sistema
    /// </summary>
    [Route("AuthSession")]
    public class AuthSessionController : ApiControllerBase
    {
        /// <summary>
        /// Injeção de interface
        /// </summary>
        private readonly IEmpresaReadRepository EmpresaReadRepository;
        private readonly IAuthSessionApiCiotAppService _authSessionApiCiotAppService;
        private readonly IConfiguration _config;
        
        /// <summary>
        /// Injeção de dependencias
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="config"></param>
        /// <param name="empresaReadRepository"></param>
        public AuthSessionController(IAppEngine engine, IConfiguration config, IEmpresaReadRepository empresaReadRepository, IAuthSessionApiCiotAppService authSessionApiCiotAppService) : base(engine)
        {
            EmpresaReadRepository = empresaReadRepository;
            _config = config;
            _authSessionApiCiotAppService = authSessionApiCiotAppService;
        }

        /// <summary>
        /// Metodo responsavel por gerar o token de acesso
        /// </summary>
        /// <param name="tokenRequest"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [Produces("application/json")]
        [HttpPost("GerarToken")]
        public async Task<RespPadrao> GerarToken([FromBody] TokenRequestIntegracao tokenRequest)
        {
            try
            {
                return await _authSessionApiCiotAppService.GerarToken(tokenRequest);
            }
            catch (Exception e)
            {
                return new RespPadrao(false, "Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
    }
}