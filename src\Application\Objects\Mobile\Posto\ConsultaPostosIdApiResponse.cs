﻿using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Mobile.Posto
{
    public class ConsultaPostosIdApiResponse
    {
        public int Id { get; set; }
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Cnpj { get; set; }
        public string Bandeira { get; set; }
        
        public string Longitude { get; set; }
        public string Latitude { get; set; }

        public string Endereco { get; set; }
        public int? EnderecoNumero { get; set; }
        public string Bairro { get; set; }
        public string Cep { get; set; }
        public string CidadeUf { get; set; }
        public string CidadeNome { get; set; }
        
        public string ContatoNome { get; set; }
        public string Telefone { get; set; }

        public bool Aberto24h { get; set; }
        public bool PossuiRestaurante { get; set; }
        public bool PossuiAreaDescanso { get; set; }
        public bool PossuiBorracharia { get; set; }
        public bool PossuiVestiario { get; set; }
        public bool PossuiComputador { get; set; }
        public bool PossuiVeiculoApoio { get; set; }
        
        public List<PostoCombustiveisMobile> PostoCombustiveisMobiles { get; set; }

    }
    public class PostoCombustiveisMobile
    {
        public int CombustivelId { get; set; }
        public string UnidadeMedida { get; set; }
        public decimal? ValorBomba { get; set; }
        public decimal? ValorBBC { get; set; }
        //public string Nfe { get; set; }
        public string Nome { get; set; }
    }
}