﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.ApiCiot.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Operacoes;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCiot.Controllers
{
    public class OperacoesController : ApiControllerBase
    {
        private readonly IOperacoesAppService _operacoesAppService;
        public OperacoesController(IAppEngine engine, IEmpresaReadRepository empresaReadRepository, IOperacoesAppService operacoesAppService) : base(engine)
        {
            _operacoesAppService = operacoesAppService;

            if (!empresaReadRepository.PermiteUsarApi(engine.User.EmpresaId).Result)
                throw new Exception("Empresa não pode acessar as funções da API");
        }
        
        [Produces("application/json")]
        [HttpPost("DeclararOperacaoTransporte")]
        public async Task<RespBase> DeclararOperacaoTransporte([FromBody] DeclararOperacaoTransporteReq req)
        {
            try
            {
                return await _operacoesAppService.DeclararOperacaoTransporte(req);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("EncerrarOperacaoTransporte")]
        public async Task<RespBase> EncerrarOperacaoTransporte([FromBody] EncerrarOperacaoTransporteReq req)
        {
            try 
            { 
                return await _operacoesAppService.EncerrarOperacaoTransporte(req); 
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }

        [Produces("application/json")]
        [HttpPost("ConsultarSituacaoTransportador")]
        public async Task<RespBase> ConsultarSituacaoTransportador([FromBody] ConsultarSituacaoTransportadorReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarSituacaoTransportador(req);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }
        
        [Produces("application/json")]
        [HttpPost("ConsultarFrotaTransportador")]
        public async Task<RespBase> ConsultarFrotaTransportador([FromBody] ConsultarFrotaTransportadorReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarFrotaTransportador(req);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }
        
        [Produces("application/json")]
        [HttpPost("CancelarOperacaoTransporte")]
        public async Task<RespBase> CancelarOperacaoTransporte([FromBody] CancelarOperacaoTransporteReq req)
        {
            try
            {
                return await _operacoesAppService.CancelarOperacaoTransporte(req);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }
        
        [Produces("application/json")]
        [HttpPost("RetificarOperacaoTransporte")]
        public async Task<RespBase> RetificarOperacaoTransporte([FromBody] RetificarOperacaoTransporteReq req)
        {
            try
            {
                return await _operacoesAppService.RetificarOperacaoTransporte(req);
            }
            catch (Exception e) { return RetornaExcecao(e.Message); }
        }
        
        [Produces("application/json")]
        [HttpPost("ConsultarSituacaoCiot")]
        public async Task<RespBase> ConsultarSituacaoCiot([FromBody] ConsultarSituacaoCiotReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarSituacaoCiot(req);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }
        
        [Produces("application/json")]
        [HttpPost("ConsultarOperacaoTacAgregado")]
        public async Task<RespBase> ConsultarOperacaoTacAgregado([FromBody] ConsultarOperacaoTacAgregadoReq req)
        {
            try
            {
                return await _operacoesAppService.ConsultarOperacaoTacAgregado(req);
            }
            catch (Exception e) { return  RetornaExcecao(e.Message); }
        }
        
        private RespBase RetornaExcecao(string eMessage)
        {
            return new RespBase
            {
                Sucesso = false,
                Excecao = new Excecao
                {
                    Mensagem = "Não foi possível realizar a operação. Mensagem: "+ eMessage
                }
            };
        }
    }
}