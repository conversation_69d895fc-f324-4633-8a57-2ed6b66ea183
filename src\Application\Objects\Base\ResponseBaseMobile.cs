using System;
using Microsoft.AspNetCore.Mvc;
using NLog;

namespace SistemaInfo.BBC.Application.Objects.Base
{
    public static class ResponseBaseMobile
    {
        
        public static Logger _logger = LogManager.GetCurrentClassLogger();
        public static JsonResult Responder(bool sucesso, string msg, object data, string erro = null)
        {
            return BigJson(new
            {
                mensagem = msg ?? "",
                sucesso = sucesso,
                data,
                erro
            });
        }

        public static  JsonResult ResponderSucesso(object data)
        {
            return BigJson(new
            {
                mensagem = "Operação realizada com sucesso!",
                sucesso = true,
                data = data
            });
        }
        
        public static JsonResult ResponderSucesso(string msg)
        {
            return BigJson(new
            {
                mensagem = msg,
                sucesso = true
            });
        }
        
        public static  JsonResult JsonGrid(object data, decimal qtdItens)
        {
            return ResponderSucesso(new
            {
                itensTotal = qtdItens,
                itens = data 
            });
        }
        
        public static JsonResult ResponderErro(string mensagem)
        {
            return Responder(false, mensagem, null);
        }
        
        public static JsonResult ResponderErro(Exception ex)
        {
            _logger.Error(ex);

            var msg = ex.Message;
            if (!string.IsNullOrWhiteSpace(ex.InnerException?.Message))
                msg += " / " + ex.InnerException;

            return Responder(false, msg,null);
        }
        
        public static JsonResult BigJson(object data)
        {
            return new JsonResult(data);
        }
    }
}