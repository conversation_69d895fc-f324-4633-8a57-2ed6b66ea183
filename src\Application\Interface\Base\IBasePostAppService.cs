﻿using System;
using System.Threading.Tasks;

namespace SistemaInfo.BBC.Application.Interface.Base
{
    public interface IBasePostAppService<in TInput> : IDisposable
    {
        Task Integrar(TInput request);
        Task Remover(int id);
    }

    public interface IBasePostAppService<in TInput, TResult> : IDisposable
    {
        Task<TResult> Integrar(TInput request);
        Task Remover(int id);
    }
}
