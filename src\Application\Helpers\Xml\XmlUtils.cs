using System;
using System.Text;
using System.Xml;

namespace SistemaInfo.BBC.Application.Helpers.Xml;

public static class XmlUtils
{
    public static string NotaXml(this string xml)
    {
        XmlDocument lXmlDados = new XmlDocument();

        try
        {
            lXmlDados.LoadXml(Encoding.UTF8.GetString(Convert.FromBase64String(xml)));
        }
        catch (Exception)
        {
            throw new Exception("Xml inválido!");
        }

        return lXmlDados.GetElementsByTagName("nNF")[0].InnerText;
    }
        
    public static decimal ValorXml(this string xml)
    {
        XmlDocument lXmlDados = new XmlDocument();

        try
        {
            lXmlDados.LoadXml(
                Encoding.UTF8.GetString(Convert.FromBase64String(xml)));
                
            var inteiro = lXmlDados.GetElementsByTagName("vNF")[0].InnerText.Split('.')[0];
            var decimais = lXmlDados.GetElementsByTagName("vNF")[0].InnerText.Split('.').Length > 1 
                ? lXmlDados.GetElementsByTagName("vNF")[0].InnerText.Split('.')[1].Length > 2 ? 
                    lXmlDados.GetElementsByTagName("vNF")[0].InnerText.Split('.')[1].Substring(0,3) : 
                    lXmlDados.GetElementsByTagName("vNF")[0].InnerText.Split('.')[1] : "";
                            
            var decimalfomatado = Convert.ToDecimal(decimais != "" ? inteiro + "," + decimais : inteiro);

            return decimalfomatado;
        }
        catch (Exception)
        {
            throw new Exception("Xml inválido!");
        }
    }
}