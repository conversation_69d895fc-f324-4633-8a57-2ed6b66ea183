﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Infra.Data.Migrations
{
    public partial class SPRINT_55_ADD_MENU_API_FRETE : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //adiciona menu de Autenticação Frete
            migrationBuilder.Sql("INSERT INTO \"BBC\".\"Menu\" (\"Id\", \"Descricao\", \"IsMenuPai\", \"Link\", \"MenuPaiId\", \"Sequencia\", \"IsMostraApenasAdmin\") VALUES(71, 'Autenticar Api Frete', 0, 'auth-client-secret.index', 1, 3, 0)");
            
            //adiciona menu de Autenticação Frete
            migrationBuilder.Sql("INSERT INTO \"BBC\".\"ModuloMenu\" (\"ModuloId\", \"MenuId\", \"Id\") VALUES (1, 71, 74)");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
