using System;
using System.Globalization;
using System.Linq;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Abastecimento;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.LotePagamento;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Models.ProtocoloAbastecimento.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.ProtocoloAbastecimentoMapper
{
    public class ProtocoloAbastecimentoMappingProfile : SistemaInfoMappingProfile
    {
        public ProtocoloAbastecimentoMappingProfile()
        {
            CreateMap<ProtocoloAbastecimentoRequest, ProtocoloAbastecimentoSalvarCommand>();
            
            CreateMap<ProtocoloAbastecimento, ProtocoloAbastecimentoSalvarCommand>();

            CreateMap<ProtocoloAbastecimento, ProtocoloAbastecimentoSalvarComRetornoCommand>();
            
            CreateMap<AbastecimentoLoteRetencaoRequest, AbastecimentoSalvarLoteRetencaoCommand>();
            
            CreateMap<ProtocoloAbastecimentoSalvarComRetornoCommand, RemoverAutorizacaoAbastecimentoCommand>();

            CreateMap<ProtocoloAbastecimento, VetorAbastecimentoRequest>();

            CreateMap<ProtocoloAbastecimento, ProtocoloAbastecimentoAlterarStatusCommand>();

            CreateMap<ProtocoloAbastecimentoRequest, ProtocoloAbastecimentoAlterarStatusCommand>();

            CreateMap<ProtocoloAbastecimentoRequest, ProtocoloAbastecimentoSalvarComRetornoCommand>();
            CreateMap<ProtocoloAbastecimentoSalvarCommand, ProtocoloAbastecimento>();

            CreateMap<ProtocoloAbastecimentoSalvarComRetornoCommand, ProtocoloAbastecimento>();

            CreateMap<ProtocoloAbastecimentoStatusRequest, ProtocoloAbastecimentoAlterarStatusCommand>();

            CreateMap<XmlAbastecimentoRequest, ProtocoloAbastecimentoSalvarComRetornoCommand>();

            CreateMap<ProtocoloAbastecimento, LotePagamento>();
            
            CreateMap<ProtocoloAbastecimento, ProtocoloAbastecimentoRequest>();
            
            CreateMap<ProtocoloAbastecimentoSalvarLoteCommand, ProtocoloLoteRequest>();

            CreateMap<ProtocoloAbastecimento, ProtocoloAbastecimentoControlePaiExport>()
                .ForMember(x => x.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(x => x.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToShortDateString()))
                .ForMember(x => x.PedidoSap, opts => opts.MapFrom(s => s.NumeroPedidoSap))
                .ForMember(x => x.NotaFiscal, opts => opts.MapFrom(s => s.NotaFiscal))
                .ForMember(x => x.Cnpj, opts => opts.MapFrom(s => s.Posto.Cnpj.ToCNPJFormato()))
                .ForMember(x => x.Status, opts => opts.MapFrom(s => s.Status.GetDescription()))
                .ForMember(x => x.QtdLitrosXml, opts => opts.MapFrom(s => s.QtdLitrosXml.ToString(CultureInfo.InvariantCulture)))
                .ForMember(x => x.ValorXml, opts => opts.MapFrom(s => s.ValorXml.ToString("C2")))
                .ForMember(x => x.RazaoSocial, opts => opts.MapFrom(s => s.Posto.RazaoSocial));

            CreateMap<ProtocoloLoteRequest, ProtocoloAbastecimentoSalvarLoteCommand>();

            CreateMap<DadosAbastecimentoRequest, ProtocoloAbastecimentoSalvarComRetornoCommand>()
                .ForMember(x => x.Cnpj, opts => opts.MapFrom(s => s.CnpjAFaturar));

            CreateMap<Abastecimento, ConsultarGridProtocoloAbastecimento>()
                .ForMember(dest => dest.DataAbastecimento, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.ValorAbastecido, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2)))
                .ForMember(dest => dest.ValorUnitario, opts => opts.MapFrom(s => Math.Round(s.ValorUnitario, 2)))
                .ForMember(dest => dest.QtdLitros, opts => opts.MapFrom(s => Math.Truncate(s.Litragem * 1000) / 1000))
                .ForMember(dest => dest.AbastecimentoId, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.EmpresaId, opts => opts.MapFrom(s => s.Veiculo.EmpresaId))
                .ForMember(dest => dest.Combustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.CodCombustivel, opts => opts.MapFrom(s => s.Combustivel.Id))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa))
                .ForMember(dest => dest.PedidoSap, opts => opts.MapFrom(s => s.ProtocoloAbastecimento.NumeroPedidoSap))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status == (EStatusAbastecimento) 1 ? "Aprovado" : s.Status == (EStatusAbastecimento) 2 ? "Reprovado" : s.Status == (EStatusAbastecimento) 3 ? "Aguardando Aprovação" : "Cancelado"))
                .ForMember(dest => dest.CnpjAFaturar, opts => opts.MapFrom(s => s.CnpjAFaturar));

            CreateMap<XmlAbastecimentoRequest, VetorAbastecimentoRequest>();

            CreateMap<ProtocoloAbastecimento, ConsultarGridProtocoloAbastecimento>()
                .ForMember(dest => dest.ValorXml, opts => opts.MapFrom(s => Math.Round(s.ValorXml, 2)))
                .ForMember(dest => dest.ValorAbastecido, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecido, 2)))
                .ForMember(dest => dest.DataAbastecimento, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                 .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status == (EStatusProtocolo) 0 ? "Reprovado" : s.Status == (EStatusProtocolo) 1 ? "Aprovado" : s.Status == (EStatusProtocolo) 2 ? "Pendente" : "Pendente"));

            CreateMap<ProtocoloAbastecimento, ConsultarGridProtocoloReenvioSap>()
                .ForMember(dest => dest.CnpjAFaturar, opts => opts.MapFrom(s => s.Abastecimentos.FirstOrDefault().CnpjAFaturar))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.NotaFiscal, opts => opts.MapFrom(s => s.NotaFiscal))
                .ForMember(dest => dest.CnpjPosto, opts => opts.MapFrom(s => s.Abastecimentos.FirstOrDefault().Posto.Cnpj));

            CreateMap<ProtocoloAbastecimento, ConsultarGridPainelPedidoPendente>()
                .ForMember(dest => dest.NomeEmpresa, opts => opts.MapFrom(s => s.Empresa.RazaoSocial ?? s.Empresa.NomeFantasia))
                .ForMember(dest => dest.CnpjEmpresa, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Empresa.Cnpj)))
                .ForMember(dest => dest.NomePosto, opts => opts.MapFrom(s => s.Posto.RazaoSocial ?? s.Posto.NomeFantasia))
                .ForMember(dest => dest.CnpjPosto, opts => opts.MapFrom(s => FormatUtils.CpfCnpj(s.Posto.Cnpj)))
                .ForMember(dest => dest.NotaFiscal, opts => opts.MapFrom(s => s.NotaFiscal))
                .ForMember(dest => dest.ValorXml, opts => opts.MapFrom(s => s.ValorXml))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.NumeroPedidoSap, opts => opts.MapFrom(s => s.NumeroPedidoSap))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.NumeroPedidoSap != null ? "Integrado" : "Pendente"));
            
            CreateMap<ProtocoloAbastecimento, ConsultarGridProtocoloAbastecimentoRelatorioItem>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.RazaoSocial, opts => opts.MapFrom(s => s.Posto.RazaoSocial))
                .ForMember(dest => dest.Cnpj, opts => opts.MapFrom(s => s.Posto.Cnpj.ToCNPJFormato()))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.FormatDateBr()))
                .ForMember(dest => dest.ValorXml, opts => opts.MapFrom(s => "R$ " + s.ValorXml.ToString("N3")))
                .ForMember(dest => dest.QtdLitrosXml, opts => opts.MapFrom(s => s.QtdLitrosXml))
                .ForMember(dest => dest.NotaFiscal, opts => opts.MapFrom(s => s.NotaFiscal))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s =>  s.Status.ToString()))
                .ForMember(dest => dest.PedidoSAP, opts => opts.MapFrom(s => s.NumeroPedidoSap));
        }
    }
}