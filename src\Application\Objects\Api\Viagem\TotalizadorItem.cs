﻿using System;
using System.ComponentModel;
using System.Linq;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem;

public class TotalizadorItem
    {
        public TotalizadorItem()
        {
        }

        private string Format { get; set; }

        [DisplayName("Detalhamento carga")]
        public string Categoria { get; set; }
        public decimal? Segunda { get; set; }
        public decimal? Terca { get; set; }
        public decimal? Quarta { get; set; }
        public decimal? Quinta { get; set; }
        public decimal? Sexta { get; set; }
        public decimal? Sabado { get; set; }
        public decimal? Domingo { get; set; }
        public decimal? TotalSemana => (Segunda ?? 0) + (Terca ?? 0) + (Quarta ?? 0) + (Quinta ?? 0) + (Sexta ?? 0) + (Sabado ?? 0) + (Domingo ?? 0);
        public decimal? TotalMes { get; set; }

        [DisplayName("Segunda")]
        public string SegundaFormatted => Segunda?.ToString(Format);

        [DisplayName("Terça")]
        public string TercaFormatted => Terca?.ToString(Format);

        [DisplayName("Quarta")]
        public string QuartaFormatted => Quarta?.ToString(Format);

        [DisplayName("Quinta")]
        public string QuintaFormatted => Quinta?.ToString(Format);

        [DisplayName("Sexta")]
        public string SextaFormatted => Sexta?.ToString(Format);

        [DisplayName("Sábado")]
        public string SabadoFormatted => Sabado?.ToString(Format);

        [DisplayName("Domingo")]
        public string DomingoFormatted => Domingo?.ToString(Format);

        [DisplayName("Total Semana")]
        public string TotalSemanaFormatted => TotalSemana?.ToString(Format);

        [DisplayName("Total Mês")]
        public string TotalMesFormatted => TotalMes?.ToString(Format);

        public TotalizadorItem(string aCategoria, string aFormat)
        {
            Categoria = aCategoria;
            Format = aFormat;
        }

        public void SetValue(DayOfWeek aDay, decimal aValor)
        {
            switch (aDay)
            {
                case DayOfWeek.Monday:
                    Segunda = aValor;
                    break;
                case DayOfWeek.Tuesday:
                    Terca = aValor;
                    break;
                case DayOfWeek.Wednesday:
                    Quarta = aValor;
                    break;
                case DayOfWeek.Thursday:
                    Quinta = aValor;
                    break;
                case DayOfWeek.Friday:
                    Sexta = aValor;
                    break;
                case DayOfWeek.Saturday:
                    Sabado = aValor;
                    break;
                case DayOfWeek.Sunday:
                    Domingo = aValor;
                    break;
            }
        }

        public static TotalizadorItem Somar(string aCategoria, params TotalizadorItem[] aItems)
        {
            var lRet = new TotalizadorItem(aCategoria, aItems.First().Format);
            foreach (var lItem in aItems)
            {
                if (lRet.Segunda == null && lItem.Segunda != null)
                    lRet.Segunda = 0;
                if (lRet.Terca == null && lItem.Terca != null)
                    lRet.Terca = 0;
                if (lRet.Quarta == null && lItem.Quarta != null)
                    lRet.Quarta = 0;
                if (lRet.Quinta == null && lItem.Quinta != null)
                    lRet.Quinta = 0;
                if (lRet.Sexta == null && lItem.Sexta != null)
                    lRet.Sexta = 0;
                if (lRet.Sabado == null && lItem.Sabado != null)
                    lRet.Sabado = 0;
                if (lRet.Domingo == null && lItem.Domingo != null)
                    lRet.Domingo = 0;
                if (lRet.TotalMes == null && lItem.TotalMes != null)
                    lRet.TotalMes = 0;

                lRet.Segunda += lItem.Segunda ?? 0;
                lRet.Terca += lItem.Terca ?? 0;
                lRet.Quarta += lItem.Quarta ?? 0;
                lRet.Quinta += lItem.Quinta ?? 0;
                lRet.Sexta += lItem.Sexta ?? 0;
                lRet.Sabado += lItem.Sabado ?? 0;
                lRet.Domingo += lItem.Domingo ?? 0;
                lRet.TotalMes += lItem.TotalMes ?? 0;
            }
            return lRet;
        }
        
        public void SomarCom(TotalizadorItem outro)
        {
            if (outro == null) return;

            Segunda = (Segunda ?? 0) + (outro.Segunda ?? 0);
            Terca = (Terca ?? 0) + (outro.Terca ?? 0);
            Quarta = (Quarta ?? 0) + (outro.Quarta ?? 0);
            Quinta = (Quinta ?? 0) + (outro.Quinta ?? 0);
            Sexta = (Sexta ?? 0) + (outro.Sexta ?? 0);
            Sabado = (Sabado ?? 0) + (outro.Sabado ?? 0);
            Domingo = (Domingo ?? 0) + (outro.Domingo ?? 0);
            TotalMes = (TotalMes ?? 0) + (outro.TotalMes ?? 0);
        }
    }