using System;
using System.Linq;
using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;

namespace SistemaInfo.BBC.Application.Helpers.Emprestimo
{
    public static class HtmlEmprestimoReportHelper
    {
        public static string GetHtmlImpressaoEmprestimo(EmprestimoConsultarParaRelatorio emprestimoConsultarParaRelatorio)
        {
            var css = GetCssImpressaoEmprestimo();
            var retencoes = string.Empty;
            
            if (emprestimoConsultarParaRelatorio.Retencoes != null && emprestimoConsultarParaRelatorio.Retencoes.Any())
                foreach (var retencao in emprestimoConsultarParaRelatorio.Retencoes)
                {
                    retencoes += $@"<tr class='text-center' style='font-size: 10pt'>
                                        <td height='20px'>{retencao.Id}</td>
                                        <td>{retencao.PagamentoId}</td>
                                        <td>{retencao.Valor}</td>
                                        <td>{retencao.Status}</td>
                                        <td>{retencao.DataIntegracao}</td>
                                        <td>{retencao.DataCadastro}</td>
                                        <td>{retencao.MensagemIntegracao}</td>
                                    </tr>";
                }

            var html = $@"{css}
                          <div class='body'>
                              <div class='header'>
                                  <div class='header-container-imagem'>
                                      <img class='img' src='data:image/png;base64,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' alt=''>
                                  </div>
                                  <div class='header-container-titulo'>
                                      <b>RELATÓRIO DE EMPRÉSTIMO</b>
                                  </div>
                                  <div class='header-container-informacoes'>
                                      <table style='float: right; font-size: 8pt'>
                                          <tr>
                                              <td><b>DATA:</b> {DateTime.Now.ToShortDateString()}</td>
                                          </tr>
                                          <tr>
                                              <td><b>HORA:</b> {DateTime.Now.ToLongTimeString()}</td>
                                          </tr>
                                      </table>
                                  </div>
                              </div>

                              <div class='corpo-emprestimo'>
                                  <table class='table'>
                                      <tr>
                                          <td colspan='6' class='table-td-title'><b>EMPRÉSTIMO</b></td>
                                      </tr>
                                      <tr style='font-size: 10pt'>
                                          <td height='20px'><b>CÓDIGO: </b>{emprestimoConsultarParaRelatorio.Id}</td>
                                          <td><b>AGÊNCIA: </b>{emprestimoConsultarParaRelatorio.Agencia}</td>
                                          <td><b>CONTA: </b>{emprestimoConsultarParaRelatorio.Conta}</td>
                                      </tr>
                                      <tr style='font-size: 10pt'>
                                          <td height='20px'><b>PORTADOR: </b>{emprestimoConsultarParaRelatorio.PortadorNome}</td>
                                          <td><b>CPF: </b>{emprestimoConsultarParaRelatorio.CpfCnpjPortador}</td>
                                          <td><b>STATUS: </b>{emprestimoConsultarParaRelatorio.Status}</td>
                                      </tr>
                                      <tr style='font-size: 10pt'>
                                          <td height='20px'><b>TAXA DE RETENÇÃO: </b>{emprestimoConsultarParaRelatorio.TaxaRetencao}</td>
                                          <td><b>VALOR DE AQUISIÇÃO: </b> {emprestimoConsultarParaRelatorio.ValorAquisicao}</td>
                                          <td><b>VALOR PAGO: </b> {emprestimoConsultarParaRelatorio.ValorPago}</td>
                                      </tr>
                                  </table>
                              </div>

                              <div class='corpo-emprestimo'>
                                  <table class='table'>
                                      <tbody>
                                      <tr>
                                          <td colspan='7' class='table-td-title'><b>RETENÇÕES</b></td>
                                      </tr>
                                      <tr style='font-size: 10pt'>
                                          <th class='table-th-title'>Cod. Retenção</th>
                                          <th class='table-th-title'>Cod. Pagamento</th>
                                          <th class='table-th-title'>Valor</th>
                                          <th class='table-th-title'>Status</th>
                                          <th class='table-th-title'>Data de integração</th>
                                          <th class='table-th-title'>Data de cadastro</th>
                                          <th class='table-th-title'>Mensagem da integração</th>
                                      </tr>
                                      </tbody>
                                      <tbody>
                                      {retencoes}
                                      </tbody>
                                  </table>
                              </div>
                          </div>";

            return html;
        }

        public static string GetHtmlImpressaoEmprestimoNulo(int emprestimoId)
        {
            return $"<div>EMPRÉSTIMO DE ID {emprestimoId} NÃO ENCONTRADO NA BASE DE DADOS</div>";
        }

        private static string GetCssImpressaoEmprestimo()
        {
            return @"<style>
                        .body {
                            font-family: Calibri, sans-serif;
                        }

                        .header {
                            border-bottom: solid;
                            border-width: 1px;
                            border-color: #D3D3D3;
                            width: 100%;
                            height: 40px;
                        }

                        .header-container-imagem {
                            text-align: left;
                            display: inline-block;
                            vertical-align: middle;
                            width: 15%;
                        }

                        .header-container-titulo {
                            text-align: center;
                            display: inline-block;
                            font-size: 12pt;
                            width: 69%;
                            height: 100%;
                        }

                        .header-container-informacoes {
                            text-align: right;
                            display: inline-block;
                            vertical-align: middle;
                            font-size: 7pt;
                            width: 15%;
                            height: 100%;
                        }

                        .corpo-emprestimo {
                            border-top: solid;
                            border-bottom: solid;
                            border-left: solid;
                            border-right: solid;
                            border-color: #D3D3D3;
                            border-width: 1px;
                            margin-top: 15px;
                        }
                        
                        .img {
                            width: 90px;
                        }
                        
                        .table {
                            width: 100%;
                        }
                        
                        .table-td-title {
                            text-align: center;
                            border-bottom: thin solid #D3D3D3;
                            height: 20px;
                        }
                        
                        .table-th-title {
                            border-bottom: thin solid #D3D3D3
                        }

                        .text-center {
                            text-align: center;
                        }
                    </style>";
        }
    }
}