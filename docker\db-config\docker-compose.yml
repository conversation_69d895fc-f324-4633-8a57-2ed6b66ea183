---
version: "2"
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper
    #network_mode: host
    environment:
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_CLIENT_PORT: 2181
    labels:
      - io.confluent.docker.testing=true
    ports:
      - "2181:2181"
    extra_hosts:
      - "moby:127.0.0.1"

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: kafka
    #network_mode: host
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
      K<PERSON><PERSON>_ADVERTISED_LISTENERS: "PLAINTEXT://kafka:9092"
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    labels:
      - io.confluent.docker.testing=true
    ports:
      - "9092:9092"
    extra_hosts:
      - "moby:127.0.0.1"

  control-center:
    image: confluentinc/cp-enterprise-control-center:latest
    container_name: control-center
    #network_mode: host
    depends_on:      
      - zookeeper
      - kafka
    environment:
      CONTROL_CENTER_ZOOKEEPER_CONNECT: "zookeeper:2181"
      CONTROL_CENTER_BOOTSTRAP_SERVERS: "kafka:9092"
      CONTROL_CENTER_REPLICATION_FACTOR: 1
    labels:
      - io.confluent.docker.testing=true
    ports:
      - "9021:9021"
    extra_hosts:
      - "moby:127.0.0.1"