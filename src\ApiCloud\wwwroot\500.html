<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Página Não Encontrada</title>
    <style>
        @import url('https://fonts.googleapis.com/css?family=Lato&display=swap');

        html, body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100%;
            width: 100%;
            font-family: 'Lato', sans-serif;
            text-align: center;
            background: linear-gradient(135deg, #fbffdf 0%, #060d20 100%);
        }

        #container {
            position: relative;
            z-index: 1;
            margin: auto;
            text-align: center;
            padding: 50px;
        }

        #container img {
            max-width: 100%;
            height: auto;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 48px;
            color: #134F4D;
        }

        p {
            font-size: 24px;
            color: #134F4D;
        }

        a {
            font-size: 20px;
            color: #0066cc;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            background: transparent;
        }
    </style>
</head>
    <body>
        <canvas id="animated-background"></canvas>
        <div id="container">
            <img src="images/500.png" alt="Não foi possível realziar a operação" style="max-width: 50%; height: auto; margin-bottom: 20px;">
            <h1>Erro interno</h1>
            <p>Desculpe, tente novamente mais tarde</p>
            <a href="/">Voltar para a página inicial</a>
        </div>
        <script>
            const canvas = document.getElementById('animated-background');
            const context = canvas.getContext('2d');
        
            function resizeCanvas() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }
        
            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();
        
            const particlesArray = [];
            const numberOfParticles = 200;
        
            class Particle {
                constructor() {
                    this.x = Math.random() * canvas.width;
                    this.y = Math.random() * canvas.height;
                    this.size = Math.random() * 5 + 1;
                    this.speedY = Math.random() * 0.8 + 0.1;
                    this.depth = Math.random() * canvas.height * 0.5;
                    this.alpha = Math.random() * 0.8 + 0.2;
                    this.opacityChangeSpeed = Math.random() * 0.01 + 0.002;
                    this.currentOpacityDirection = 1;
                }
        
                update() {
                    this.y -= this.speedY;
        
                    if (this.alpha <= 0.2) {
                        this.currentOpacityDirection = 0.8;
                    } else if (this.alpha >= 1) {
                        this.currentOpacityDirection = -1;
                    }
                    this.alpha += this.opacityChangeSpeed * this.currentOpacityDirection;
        
                    if (this.y < 0) {
                        this.y = canvas.height + 10;
                        this.x = Math.random() * canvas.width;
                        this.depth = Math.random() * canvas.height * 0.5;
                        this.alpha = Math.random() * 0.8 + 0.2;
                    }
                }
        
                draw() {
                    const sizeFactor = 1 - (this.depth / (canvas.height * 0.5));
                    const scaledSize = this.size * sizeFactor;
        
                    context.fillStyle = `rgba(255, 255, 255, ${this.alpha})`;
                    context.beginPath();
                    context.arc(this.x, this.y, scaledSize, 0, Math.PI * 2);
                    context.closePath();
                    context.fill();
                }
            }
        
            function init() {
                for (let i = 0; i < numberOfParticles; i++) {
                    particlesArray.push(new Particle());
                }
            }
        
            function animate() {
                context.clearRect(0, 0, canvas.width, canvas.height);
        
                for (let i = 0; i < particlesArray.length; i++) {
                    particlesArray[i].update();
                    particlesArray[i].draw();
                }
        
                requestAnimationFrame(animate);
            }
        
            init();
            animate();
        </script>
    </body>

</html>
