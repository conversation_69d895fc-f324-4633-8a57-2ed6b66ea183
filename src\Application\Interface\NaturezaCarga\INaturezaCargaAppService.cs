using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Web.NaturezaCarga;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.NaturezaCarga.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.NaturezaCarga
{
    public interface INaturezaCargaAppService : IAppService<Domain.Models.NaturezaCarga.NaturezaCarga, INaturezaCargaReadRepository, INaturezaCargaWriteRepository>
    {
        ConsultarGridNaturezaCargaResponse ConsultarGridNaturezaCarga(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
    }
}