﻿using SistemaInfo.BBC.Application.Objects.Web.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado;
using SistemaInfo.BBC.Domain.Models.DocumentosProcessoVinculado.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.DocumentosProcessoVinculadoMapper
{
    public class DocumentosProcessoVinculadoMappingProfile : SistemaInfoMappingProfile
    {
        public DocumentosProcessoVinculadoMappingProfile()
        {
            CreateMap<DocumentosProcessoVinculado, ConsultarGridDocumentosProcessoVinculado>()
                .ForMember(x => x.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(x => x.ProcessoVinculadoId, opts => opts.MapFrom(s => s.ProcessoVinculadoId.GetHashCode()))
                .ForMember(x => x.Obrigatorio, opts => opts.MapFrom(s => s.Obrigatorio.GetHashCode()))
                .ForMember(x => x.Tipo, opts => opts.MapFrom(s => s.Tipo.GetHashCode()))
                .ForMember(x => x.UsuarioCadastro, opts => opts.MapFrom(s => s.UsuarioCadastro.Nome));
            
            CreateMap<DocumentosProcessoVinculado, DocumentosProcessoVinculadoResponse>()
                .ForMember(x => x.ProcessoVinculadoId, opts => opts.MapFrom(s => s.ProcessoVinculadoId.GetHashCode()))
                .ForMember(x => x.Obrigatorio, opts => opts.MapFrom(s => s.Obrigatorio.GetHashCode()))
                .ForMember(x => x.Tipo, opts => opts.MapFrom(s => s.Tipo.GetHashCode()));

            CreateMap<DocumentosProcessoVinculado, DocumentosProcessoVinculadoAnexarResponse>()
                .ForMember(x => x.Obrigatorio, opts => opts.MapFrom(s => s.Obrigatorio.GetHashCode()))
                .ForMember(x => x.Tipo, opts => opts.MapFrom(s => s.Tipo.GetHashCode()));
            
            CreateMap<DocumentosProcessoVinculadoRequest, DocumentosProcessoVinculadoSalvarCommand>();
            
            CreateMap<DocumentosProcessoVinculado, DocumentosProcessoVinculadoSalvarCommand>();

            CreateMap<DocumentosProcessoVinculadoRequest, DocumentosProcessoVinculadoSalvarComRetornoCommand>();

            CreateMap<DocumentosProcessoVinculadoSalvarCommand, DocumentosProcessoVinculado>();

            CreateMap<DocumentosProcessoVinculadoSalvarComRetornoCommand, DocumentosProcessoVinculado>();
            
            CreateMap<DocumentosProcessoVinculadoStatusRequest, DocumentosProcessoVinculadoAlterarStatusCommand>();

        }
    }
}