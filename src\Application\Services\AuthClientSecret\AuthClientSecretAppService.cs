using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.Interface.AuthClientSecret;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AuthClientSecret;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret.Commands;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret.Repository;
using SistemaInfo.BBC.Domain.Models.Usuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.Services.AuthClientSecret
{
    public class AuthClientSecretAppService : AppService<Domain.Models.AuthClientSecret.AuthClientSecret,
            IAuthClientSecretReadRepository, IAuthClientSecretWriteRepository>,
        IAuthClientSecretAppService
    {
        private readonly IUsuarioReadRepository _usuarioReadRepository;

        public AuthClientSecretAppService(IAppEngine engine,
            IAuthClientSecretReadRepository readRepository,
            IAuthClientSecretWriteRepository writeRepository,
            IUsuarioReadRepository usuarioReadRepository)
            : base(engine, readRepository, writeRepository)
        {
            _usuarioReadRepository = usuarioReadRepository;
        }

        public async Task<RespPadrao> ConsultarPorId(int idAuthClientSecret)
        {
            try
            {
                var lAuthClientSecret = await Repository.Query
                    .Where(x => x.Id == idAuthClientSecret)
                    .Select(x => new ConsultarAuthClientSecretResponse
                    {
                        Id = x.Id,
                        Login = x.Login,
                        ClientSecret = x.ClientSecret,
                        Descricao = x.Descricao,
                        Ativo = x.Ativo,
                        DataCadastro = x.DataCadastro,
                        DataAlteracao = x.DataAlteracao
                    })
                    .FirstOrDefaultAsync();

                return new RespPadrao
                {
                    sucesso = true,
                    data = lAuthClientSecret
                };
            }
            catch (Exception e)
            {
                var lLog = LogManager.GetCurrentClassLogger();
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível consultar o auth client secret. " + e.Message);
            }
        }

        public async Task<RespPadrao> ConsultarGridAuthClientSecret(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters)
        {
            try
            {
                var lQuery = Repository.Query
                    .Include(x => x.Usuario)
                    .Include(x => x.UsuarioAlteracao)
                    .AsQueryable();

                // Aplicar filtros
                if (filters != null && filters.Any())
                {
                    foreach (var filter in filters)
                    {
                        switch (filter.Campo.ToLower())
                        {
                            case "login":
                                lQuery = lQuery.Where(x => x.Login.Contains(filter.Valor));
                                break;
                            case "descricao":
                                lQuery = lQuery.Where(x => x.Descricao.Contains(filter.Valor));
                                break;
                            case "ativo":
                                if (int.TryParse(filter.Valor, out int ativo))
                                    lQuery = lQuery.Where(x => x.Ativo == ativo);
                                break;
                        }
                    }
                }

                // Aplicar ordenação
                if (orderFilters != null && !string.IsNullOrEmpty(orderFilters.Campo))
                {
                    switch (orderFilters.Campo.ToLower())
                    {
                        case "login":
                            lQuery = orderFilters.Operador.ToString() == "ascending" 
                                ? lQuery.OrderBy(x => x.Login) 
                                : lQuery.OrderByDescending(x => x.Login);
                            break;
                        case "descricao":
                            lQuery = orderFilters.Operador.ToString() == "ascending" 
                                ? lQuery.OrderBy(x => x.Descricao) 
                                : lQuery.OrderByDescending(x => x.Descricao);
                            break;
                        case "datacadastro":
                            lQuery = orderFilters.Operador.ToString() == "ascending" 
                                ? lQuery.OrderBy(x => x.DataCadastro) 
                                : lQuery.OrderByDescending(x => x.DataCadastro);
                            break;
                        default:
                            lQuery = lQuery.OrderByDescending(x => x.DataCadastro);
                            break;
                    }
                }
                else
                {
                    lQuery = lQuery.OrderByDescending(x => x.DataCadastro);
                }

                var totalItems = await lQuery.CountAsync();

                var items = await lQuery
                    .Skip((page - 1) * take)
                    .Take(take)
                    .Select(x => new ConsultarAuthClientSecretGrid
                    {
                        Id = x.Id,
                        Login = x.Login,
                        ClientSecret = x.ClientSecret,
                        Descricao = x.Descricao,
                        Ativo = x.Ativo,
                        DataCadastro = x.DataCadastro.ToString("dd/MM/yyyy HH:mm"),
                        DataAlteracao = x.DataAlteracao.HasValue ? x.DataAlteracao.Value.ToString("dd/MM/yyyy HH:mm") : "",
                        UsuarioCadastro = x.Usuario != null ? x.Usuario.Nome : "",
                        UsuarioAlteracao = x.UsuarioAlteracao != null ? x.UsuarioAlteracao.Nome : ""
                    })
                    .ToListAsync();

                return new RespPadrao
                {
                    sucesso = true,
                    data = new ConsultarGridAuthClientSecretResponse
                    {
                        TotalItems = totalItems,
                        Items = items
                    }
                };
            }
            catch (Exception e)
            {
                var lLog = LogManager.GetCurrentClassLogger();
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível consultar o grid de auth client secret. " + e.Message);
            }
        }

        public async Task<RespPadrao> SaveAuthClientSecret(AuthClientSecretRequest lModel)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (lModel is null)
                    throw new InvalidOperationException("Requisição enviada com dados inválidos.");

                if (string.IsNullOrWhiteSpace(lModel.Login))
                    throw new InvalidOperationException("Login é obrigatório.");

                if (string.IsNullOrWhiteSpace(lModel.Descricao))
                    throw new InvalidOperationException("Descrição é obrigatória.");

                // Hash da senha se fornecida
                if (!string.IsNullOrWhiteSpace(lModel.Senha))
                    lModel.Senha = lModel.Senha.GetHashSha1();

                if (lModel.Id != 0)
                {
                    // Verificar se login já existe para outro registro
                    var existeOutro = await Repository.Query.AnyAsync(x => x.Login == lModel.Login && x.Id != lModel.Id && x.Ativo == 1);
                    if (existeOutro)
                        throw new InvalidOperationException("Já existe um auth client secret ativo com este login.");

                    var lAuthClientSecretUpdate = Mapper.Map<AuthClientSecretEditarCommand>(lModel);
                    await Engine.CommandBus.SendCommandAsync(lAuthClientSecretUpdate);
                    return new RespPadrao()
                    {
                        sucesso = true,
                        mensagem = "Auth client secret editado com sucesso.",
                        data = lModel.ClientSecret
                    };
                }

                // Verificar se login já existe
                if (await Repository.Query.AnyAsync(x => x.Login == lModel.Login && x.Ativo == 1))
                    throw new InvalidOperationException("Já existe um auth client secret ativo com este login.");

                // Gerar client secret se não fornecido
                if (string.IsNullOrWhiteSpace(lModel.ClientSecret))
                {
                    lModel.ClientSecret = GenerateClientSecret(42);
                }

                var command = Mapper.Map<AuthClientSecretAdicionarCommand>(lModel);
                command.DataCadastro = DateTime.Now;
                command.UsuarioCadastroId = Engine.User.Id;

                await Engine.CommandBus.SendCommandAsync(command);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Auth client secret salvo com sucesso.",
                    data = lModel.ClientSecret
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível salvar o auth client secret. " + e.Message);
            }
        }

        public async Task<RespPadrao> AlterarStatus(AuthClientSecretAlterarStatusRequest lModel)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                if (lModel is null)
                    throw new InvalidOperationException("Requisição enviada com dados inválidos.");

                var command = Mapper.Map<AuthClientSecretAlterarStatusCommand>(lModel);
                await Engine.CommandBus.SendCommandAsync(command);

                return new RespPadrao
                {
                    sucesso = true,
                    mensagem = "Status alterado com sucesso."
                };
            }
            catch (Exception e)
            {
                lLog.Error(e);
                return new RespPadrao(false, "Não foi possível alterar o status. " + e.Message);
            }
        }

        private string GenerateClientSecret(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
