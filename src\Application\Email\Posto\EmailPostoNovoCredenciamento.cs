﻿using System;
using System.IO;
using System.Net.Mail;
using SistemaInfo.BBC.Domain.Components.Email;

namespace SistemaInfo.BBC.Application.Email.Usuario
{
    public class EmailPostoNovoCredenciamento
    {
        public static void EnviarEmail(INotificationEmailExecutor notificationEmailExecutor, string nomeUsuario, string CNPJ, string Senha, string destinatario)
        {
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

            var remetente = "<EMAIL>";
            using (var streamReader = new StreamReader(caminhoAplicacao + @"\Content\Email\Credenciamento\enviar-email-novo-posto-credenciado.html"))
            {
                var html = streamReader.ReadToEnd();
                html = html.Replace("{NOME_USUARIO}", nomeUsuario);
                html = html.Replace("{CNPJ}", CNPJ);
                html = html.Replace("{SENHA}", Senha);
                
                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                {
                    To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                    Priority = MailPriority.High,
                    Subject = "DADOS DE ACESSO - NOVO CREDENCIADO",
                    IsBodyHtml = true,
                    AlternateView = view,
                    From = new Domain.Components.Email.Email.EmailAddress {Address = remetente}
                });
            }
        }
    }
}