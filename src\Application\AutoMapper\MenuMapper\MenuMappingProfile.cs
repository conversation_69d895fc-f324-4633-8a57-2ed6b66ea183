﻿using SistemaInfo.BBC.Application.Objects.Api.Menu;
using SistemaInfo.BBC.Application.Objects.Web.Menu;
using SistemaInfo.BBC.Domain.Models.Menu;
using SistemaInfo.BBC.Domain.Models.Menu.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.MenuMapper
{
    public class MenuMappingProfile : SistemaInfoMappingProfile
    {
        public MenuMappingProfile()
        {
            CreateMap<Menu, MenuFilhoResponse>()
                .ForMember(dest => dest.Menu, opt => opt.MapFrom(src => src.Descricao))
                .ForMember(dest => dest.IdMenu, opt => opt.MapFrom(src => src.Id));

            CreateMap<MenuIntegrarApiRequest, MenuSalvarCommand>()
                .ForMember(a => a.IsMenuPai, opts => opts.MapFrom(d => d.IsMenuPai ? 1 : 0))
                .ForMember(a => a.Desc<PERSON>, opts => opts.MapFrom(d => d.NomeMenu));

            CreateMap<MenuIntegrarApiRequest, MenuSalvarComRetornoCommand>()
                .ForMember(a => a.IsMenuPai, opts => opts.MapFrom(d => d.IsMenuPai ? 1 : 0))
                .ForMember(a => a.Descricao, opts => opts.MapFrom(d => d.NomeMenu));

            CreateMap<MenuSalvarCommand, Menu>();

            CreateMap<MenuSalvarComRetornoCommand, Menu>();
        }
    }
}