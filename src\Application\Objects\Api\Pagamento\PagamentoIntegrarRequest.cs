using System;

namespace SistemaInfo.BBC.Application.Objects.Api.Pagamento
{
    public class PagamentoIntegrarRequest 
    {
        public string Id { get; set; }
        public string CiotId { get; set; }
        public int FormaPagamento { get; set; }
        public int Tipo { get; set; }
        public string Valor { get; set; }
        public string Descricao { get; set; }
        
        public int? IdContaDestino { get; set; }
        public string CpfContaDestino { get; set; } // cpf do portador proprietario
        public int? IdContaOrigem { get; set; } // conta da empresa ta certo , obrigatorio ser da empresa

        public double PercentualTransferencia { get; set; } = 100; // quanto que vai trasnfereir para o motorista
        public int? IdContaTransferencia { get; set; } // conta do motorista
        public string CpfContaTransferencia { get; set; } // cpf da conta 
        public DateTime? DataPrevisaoPagamento { get; set; } = DateTime.Today;

        public int? IdPagamentoExterno { get; set; }
        
        public string Agencia { get; set; }
        public string Conta { get; set; }
        public string Banco { get; set; }
        public string TipoConta { get; set; }
        
//        public string CnpjBBC { get; set; }
//        public string SenhaApi { get; set; }
    }
}