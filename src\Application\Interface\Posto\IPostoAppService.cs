﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Posto;
using SistemaInfo.BBC.Application.Objects.Web.Posto.Request;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Posto.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Posto
{
    public interface IPostoAppService : IAppService<Domain.Models.Posto.Posto, IPostoReadRepository,
        IPostoWriteRepository>
    {
        ConsultarGridPostoResponse ConsultarGridPosto(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<PostoResponse> ConsultarPorId(int idPosto);
        Task<RespPadrao> ConsultaCadastroCnpj(string cnpj);
        Task AlterarStatus(PostoStatusRequest lPostoStatus);
        Task<RespPadrao> Save(PostoRequest lPostoReq);
        Task<RespPadrao> SaveNewAccreditation(PostoRequest lPostoReq);
        PostoAutenticacaoViewModel ValidarPosto(string cnpj, string senha);
        PostoAutenticacaoViewModel ValidarUsuarioPosto(string cpf, string senha);
        Task<RespPadrao> RecuperarSenha(RecuperarSenhaPostoRequest request);
        Task<RespPadrao> RecuperarSenhaUsuarioPosto(RecuperarSenhaPostoRequest request);
        Task<RespPadrao> ChangePassword(AlterarSenhaPostoRequest request);
        ConsultaListaPostosResponse ConsultarListaPostos(string latitude, string longitude, int? raio);
        ConsultaPostosIdApiResponse ConsultarPostoIdApi(int idPosto);
        RespPadrao ConsultaPostoAbastecimento (ConsultaPostoAbastecimentoMobileQrRequest qrCode, string portadorCpfCnpj);
        Task<RespPadrao> AprovarCredencimento(PostoStatusRequest request);
        Task<RespPadrao> ReprovarCredencimento(PostoStatusRequest request);
        Task<RespPadrao> ValidarStatusCredenciamentoPosto(int idPosto);

        #region Tarefas
        
        Task ServiceEnviarEmailNotificacaoFarolSla();
        Task ServiceEnviarEmailControleCredenciamentoFarolSla();
        
        #endregion
        

    }
}