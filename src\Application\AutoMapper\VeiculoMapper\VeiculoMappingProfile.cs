﻿using SistemaInfo.BBC.Application.Objects.Api.Veiculo;
using SistemaInfo.BBC.Application.Objects.Mobile.Veiculo;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.Veiculo;
using SistemaInfo.BBC.Application.Objects.Web.VeiculoCombustivel;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Veiculo;
using SistemaInfo.BBC.Domain.Models.Veiculo.Commands;
using SistemaInfo.BBC.Domain.Models.VeiculoCombustivel;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa;
using SistemaInfo.BBC.Domain.Models.VeiculoEmpresa.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.VeiculoMapper
{
    public class VeiculoMappingProfile : SistemaInfoMappingProfile
    {
        public VeiculoMappingProfile()
        {
            CreateMap<VeiculoRequest, VeiculoSalvarCommand>();
            
            CreateMap<VeiculoRequest, VeiculoAlterarStatusCommand>();
            
            CreateMap<Veiculo, VeiculoRequest>();
            
            CreateMap<Veiculo, ConsultarGridVeiculoItem>()
                .ForMember(a => a.Rntrc, opts => opts.MapFrom(d => d.Portador.RNTRC))
                .ForMember(a => a.Placa, opts => opts.MapFrom(d => d.Placa.ToUpper().ToPlacaFormato()))
                ;

            CreateMap<VeiculoRequest, VeiculoSalvarComRetornoCommand>();
            
            CreateMap<VeiculoCombustivelRequest, VeiculoCombustivel>();
            
            CreateMap<VeiculoIntegrarApiRequest, VeiculoCombustivel>();

            CreateMap<VeiculoSalvarCommand, Veiculo>()
                .ForMember(a => a.Placa, opts => opts.MapFrom(d => d.Placa.ToUpper()));

            CreateMap<VeiculoSalvarComRetornoCommand, Veiculo>()
                .ForMember(a => a.DataCadastro, opts => opts.MapFrom(d => d.DataCadastro))
                .ForMember(a => a.Placa, opts => opts.MapFrom(d => d.Placa.ToUpper()));

            CreateMap<VeiculoStatusRequest, VeiculoAlterarStatusCommand>();

            CreateMap<VeiculoEmpresaSalvarCommand, VeiculoEmpresa>();
            
            CreateMap<VeiculoEmpresaRequest, VeiculoEmpresaSalvarCommand>();

            CreateMap<Veiculo, VeiculoResponse>();

            CreateMap<Veiculo, ModeloVeiculoResponse>();

            CreateMap<VeiculoCombustivel, VeiculoCombustivelResponse>()
                .ForMember(dest => dest.CombustivelNome,opts => opts.MapFrom(s => s.Combustivel.Nome));
           
            CreateMap<ConsultaVeiculoAbastecimentoResponse, ConsultaVeiculoAbastecimentoMobileResponse>()
                .ForMember(dest => dest.ListaCombustiveisApi, opts => opts.MapFrom(s => s.ListaCombustiveis));
        }
    }
}