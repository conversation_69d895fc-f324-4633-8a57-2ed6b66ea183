using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using NLog;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Individuo;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Banco.Repository;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;
using SistemaInfo.BBC.Domain.Models.Documento.Repository;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.BBC.Domain.Models.Portador.Commands;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;
using IContaConductorReadRepository = SistemaInfo.BBC.Domain.Models.ContasConductor.Repository.IContaConductorReadRepository;

namespace SistemaInfo.BBC.Application.External.Conductor.Services
{
    public class CartaoAppService : ICartaoAppService
    {
        private ICartaoRepository _cartaoRepository;
        private IEmpresaReadRepository _empresaReadRepository;
        private IPortadorReadRepository _portadorReadRepository;
        private ICidadeReadRepository _cidadeReadRepository;
        private readonly IContaConductorReadRepository _contaConductorReadRepository;
        private readonly IDocumentoWriteRepository _documentoWriteRepository;
        private readonly IDocumentoReadRepository _documentoReadRepository;
        private readonly IAppEngine _appEngine;
        private readonly IUsuarioRepository _usuarioRepository;
        private IBancoReadRepository _bancoReadRepository;
        
        public CartaoAppService(ICartaoRepository cartaoRepository, 
                                IEmpresaReadRepository empresaReadRepository, 
                                IPortadorReadRepository portadorReadRepository,  
                                ICidadeReadRepository cidadeReadRepository,  
                                IContaConductorReadRepository contaConductorReadRepository, 
                                IDocumentoWriteRepository documentoWriteRepository, 
                                IAppEngine appEngine, 
                                IDocumentoReadRepository documentoReadRepository, 
                                IUsuarioRepository usuarioRepository, IBancoReadRepository bancoReadRepository)
        {
            _cartaoRepository = cartaoRepository;
            _empresaReadRepository = empresaReadRepository;
            _portadorReadRepository = portadorReadRepository;
            _cidadeReadRepository = cidadeReadRepository;
            _contaConductorReadRepository = contaConductorReadRepository;
            _documentoWriteRepository = documentoWriteRepository;
            _appEngine = appEngine;
            _documentoReadRepository = documentoReadRepository;
            _usuarioRepository = usuarioRepository;
            _bancoReadRepository = bancoReadRepository;
        }

        public Task<AtribuirTitularResp> AtribuirTitular(int idCartao, string cpfCnpj)
        {
            return _cartaoRepository.AtribuirTitularAsync(idCartao, cpfCnpj);
        }

        public Task<AtribuirCartaoPrePagoResp> AtribuirCartaoPrePago(int idCartao, int idConta, string cpfCnpj)
        {
            return _cartaoRepository.AtribuirCartaoPrePagoAsync(idCartao, idConta, cpfCnpj);
        }

        public Task<ContaDock> BloquearConta(int idAccount, int idStatus)
        {
            return _cartaoRepository.BloquearConta(idAccount, idStatus);
        }

        public Task<ContaDock> ReativarConta(int idAccount)
        {
            return _cartaoRepository.ReativarConta(idAccount);
        }

        public Task<CartaoResp> DesbloquearCartao(int idCartao)
        {
            return _cartaoRepository.DesbloquearCartao(idCartao);
        }

        public Task<CartaoResp> BloquearCartao(int idCartao, int idStatus, string motivo)
        {
            return _cartaoRepository.BloquearCartao(idCartao, idStatus, motivo);
        }

        public Task<CartaoResp> CancelarCartao(int idCartao, int idStatus, string observacao)
        {
            return _cartaoRepository.CancelarCartao(idCartao, idStatus, observacao);
        }

        public Task<UsuarioCartaoResp> ConsultaCartaoPorConta(int? idAccount)
        {
            return _cartaoRepository.ConsultaCartaoPorConta(idAccount);
        }

        public Task<ConsultarContaResp> ConsultarContas(int? requestTake, int? requestPage, OrderFilters requestOrder,
            List<QueryFilters> requestFilters, string cpfcnpj, bool isPortador = false)
        {
            return _cartaoRepository.ConsultarContas(requestTake, requestPage, cpfcnpj, isPortador);
        }

        public Task<ConsultarContaPorIdResp> ConsultarContasPorId(int idAccount)
        {
            return _cartaoRepository.ConsultarContasPorId(idAccount);
        }

        public Task<ConsultarContaResp> ConsultarContasPorIdEmpresa(int? requestTake, int? requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters,
            int idEmpresa)
        {
            var empresa = _empresaReadRepository.GetById(idEmpresa);
            
            return _cartaoRepository.ConsultarContas(requestTake, requestPage, empresa.Cnpj);
        }

        public Task<ConsultarContaResp> ConsultarContasPorIdPortador(int? requestTake, int? requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters,
            int idPortador)
        {
            var portador = _portadorReadRepository.GetById(idPortador);
            
            return _cartaoRepository.ConsultarContas(requestTake, requestPage, portador.CpfCnpj);
        }

        public Task<PessoaFisicaResp> CadastrarPessoaFisica(ContaPessoaFisicaReq conta)
        {
            return _cartaoRepository.CadastrarPessoaFisica(conta);
        }

        public Task<IndividuoResp> ConsultarPessoaFisica(string cpf)
        {
            return _cartaoRepository.ConsultarPessoaFisica(cpf);
        }

        public Task<ContaPessoaJuridicaResp> CadastrarContaPessoaJuridica(ContaPessoaJuridicaReq conta)
        {
            return _cartaoRepository.CadastrarContaPessoaJuridica(conta);
        }

        public Task<IndividuoJuridicoResp> ConsultarPessoaJuridica(string cnpj)
        {
            return _cartaoRepository.ConsultarPessoaJuridica(cnpj);
        }

        public async Task<ConsultarContaAgenciaResp> ConsultarContaAgencia(string cnpj)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            lLog.Info("0 ConsultarContaAgenciaResp");
            var listaConta = new List<ContaResp>();
            var lContas = _cartaoRepository.ConsultarContas(null, null, cnpj);

            if (lContas?.Result?.Code != "404" && lContas?.Result?.Code.ToIntSafe() != 404)
            {
                var lIdConta = lContas?.Result.content.FindAll(x => StatusConta().Contains(x.idStatusConta))
                    .Select(x => x.id).ToList();

                if (lIdConta != null)
                {
                    foreach (var idConta in lIdConta)
                    {
                        var lContaConductor = _cartaoRepository.ConsultarContaAliasBank(idConta.ToInt());

                        if (lContaConductor != null)
                        {
                            var lPortador = _portadorReadRepository.Where(x => x.CpfCnpj == cnpj).Select(x => x.Id)
                                .FirstOrDefault().ToInt32();

                            if (lPortador == 0)
                            {
                                lLog.Info("Portador não cadastrado para CPF/CNPJ: " + cnpj);

                                var individuoPortador =
                                    await _usuarioRepository.ConsultaPortadorConductorAsync(cnpj, 1);

                                var nomePortador = "";

                                if (individuoPortador.content != null)
                                {
                                    nomePortador = individuoPortador.content.Select(x => x.nome).FirstOrDefault()
                                        .ToString();
                                }
                                else
                                {
                                    nomePortador = individuoPortador.items.Select(x => x.name).FirstOrDefault()
                                        .ToString();
                                }

                                var portador = new PortadorRequest();
                                portador.CpfCnpj = cnpj;
                                portador.Bairro = "Bairro Padrão";
                                portador.Nome = nomePortador;
                                portador.CidadeId = _cidadeReadRepository.FirstOrDefault().Id;
                                portador.EstadoId = _cidadeReadRepository.FirstOrDefault().EstadoId;
                                portador.TipoPessoa = cnpj.Length > 11 ? 2 : 1;

                                var command = Mapper.Map<PortadorSalvarComRetornoCommand>(portador);
                                var retorno = _appEngine.CommandBus.SendCommand<Portador>(command);
                                lLog.Info("Portador pre-cadastrado" + retorno.Id);
                            }

                            lLog.Info("1 chegou for aliasbank");
                            lLog.Info(lContaConductor.Result);

                            foreach (var lContasBank in lContaConductor.Result.items)
                            {
                                lLog.Info("2 ENCONTROU for aliasbank");
                                if (lContasBank.bankAccountStatus == "ACTIVE")
                                {
                                    var lNumeroConta = lContasBank.bankAccountNumber;
                                    
                                    var lConta = new ContaResp();
                                    lConta.IdConta = idConta.ToInt();
                                    
                                    lConta.Conta = lContasBank.bankAccountDigit != "" 
                                        ? lNumeroConta
                                        :lNumeroConta.Substring(0, lNumeroConta.Length - 1);
                                    
                                    lConta.DigitoConta = lContasBank.bankAccountDigit != "" 
                                        ? lContasBank.bankAccountDigit 
                                        : lNumeroConta.Substring(lNumeroConta.Length - 1);
                                    
                                    lConta.Agencia = lContasBank.bankBranchNumber;
                                    
                                    lConta.DigitoAgencia = lContasBank.bankBranchDigit != "" 
                                        ? lContasBank.bankBranchDigit 
                                        : "0";
                                    
                                    lConta.StatusConta = lContasBank.bankAccountStatus;

                                    listaConta.Add(lConta);
                                    return new ConsultarContaAgenciaResp
                                    {
                                        contas = listaConta
                                    };
                                }
                            }
                        }
                    }

                    if (cnpj.Length > 11)
                    {
                        var lConta = new ContaResp();
                        lConta.IdConta = lIdConta.FirstOrDefault();
                        lConta.Conta = "1111";
                        lConta.DigitoConta = "1";
                        lConta.Agencia = "1111";
                        lConta.DigitoAgencia = "1";
                        lConta.StatusConta = "ACTIVE";

                        listaConta.Add(lConta);
                        return new ConsultarContaAgenciaResp
                        {
                            contas = listaConta
                        };
                    }
                }
            }

            return new ConsultarContaAgenciaResp
            {
                Sucesso = false
            };
        }

        public async Task<ConsultarContaAgenciaResp> ConsultarContaAgenciaPosto(string cnpj)
        {
            if (cnpj.Length <= 11)
            {
                return new ConsultarContaAgenciaResp
                {
                    Sucesso = false,
                    message = "CNPJ inválido."
                };
            }

            var lLog = LogManager.GetCurrentClassLogger();
            lLog.Info("0 ConsultarContaAgenciaResp");

            var lListaContas = new List<ContaResp>();

            var lContasDoCnpj = _cartaoRepository
                .ConsultarContas(null, null, cnpj)?.Result;

            if (lContasDoCnpj == null || lContasDoCnpj.Code == "404" || lContasDoCnpj.content == null)
            {
                return new ConsultarContaAgenciaResp
                {
                    Sucesso = false,
                    message = "Nenhuma conta encontrada para esse CNPJ."
                };
            }

            var lIdsDasContasDoCnpj = lContasDoCnpj.content
                .FindAll(x => StatusConta().Contains(x.idStatusConta))
                .Select(x => x.id)
                .ToList();

            if (!lIdsDasContasDoCnpj.Any())
            {
                return new ConsultarContaAgenciaResp
                {
                    Sucesso = false,
                    message = "Nenhuma conta encontrada para esse CNPJ."
                };
            }

            var lPortador = await _portadorReadRepository
                .Where(x => x.CpfCnpj == cnpj)
                .FirstOrDefaultAsync();

            if (lPortador == null)
            {
                lLog.Info("Portador não cadastrado para CPF/CNPJ: " + cnpj);

                var lIndividuoPortador = _usuarioRepository
                    .ConsultaPortadorConductorAsync(cnpj, 1).Result;

                if (lIndividuoPortador == null)
                {
                    return new ConsultarContaAgenciaResp
                    {
                        Sucesso = false,
                        message = "Portador não cadastrado no onboarding."
                    };
                }

                string lNomePortador;

                if (lIndividuoPortador.content != null)
                {
                    lNomePortador = lIndividuoPortador.content
                        .Select(x => x.nome)
                        .FirstOrDefault()?
                        .ToString();
                }
                else
                {
                    lNomePortador = lIndividuoPortador.items
                        .Select(x => x.name)
                        .FirstOrDefault()?
                        .ToString();
                }

                var lNovoPortador = new PortadorRequest
                {
                    CpfCnpj = cnpj,
                    Bairro = "Bairro Padrão",
                    Nome = lNomePortador,
                    CidadeId = (await _cidadeReadRepository.FirstOrDefaultAsync()).Id,
                    EstadoId = (await _cidadeReadRepository.FirstOrDefaultAsync()).EstadoId,
                    TipoPessoa = cnpj.Length > 11 ? 2 : 1
                };

                var lPortadorSalvarCommand = Mapper.Map<PortadorSalvarComRetornoCommand>(lNovoPortador);

                var lPortadorSalvo = await _appEngine.CommandBus.SendCommandAsync<Portador>(lPortadorSalvarCommand);

                lLog.Info("Portador pre-cadastrado" + lPortadorSalvo.Id);
            }

            var lIdConta = lIdsDasContasDoCnpj.FirstOrDefault();

            var lContasAliasBank = await _cartaoRepository
                .ConsultarContaAliasBank(lIdConta.ToInt());

            lLog.Info("1 Chegou no for de contas Aliasbank");
            lLog.Info(lContasAliasBank);

            if (lContasAliasBank == null)
            {
                return new ConsultarContaAgenciaResp
                {
                    Sucesso = false,
                    message = "Conta Alias não encontrada para esse CNPJ."
                };
            }

            if (!lContasAliasBank.items.Any())
            {
                return new ConsultarContaAgenciaResp
                {
                    Sucesso = false,
                    message = "Conta Alias não encontrada para esse CNPJ."
                };
            }

            var lContaAliasBank = lContasAliasBank.items.FirstOrDefault();

            if (lContaAliasBank == null)
            {
                return new ConsultarContaAgenciaResp
                {
                    Sucesso = false,
                    message = "Conta Alias não encontrada para esse CNPJ."
                };
            }

            lLog.Info("2 Encontrou uma conta Aliasbank");
            lLog.Info(lContaAliasBank);

            var lNumeroConta = lContaAliasBank.bankAccountNumber;
            var lNumeroAgencia = lContaAliasBank.bankBranchNumber;
            var lBanco = await _bancoReadRepository.GetByIdAsync(lContaAliasBank.bankNumber);

            if (lBanco == null)
            {
                return new ConsultarContaAgenciaResp
                {
                    Sucesso = false,
                    message = "Banco desconhecido."
                };
            }

            var lContaResponse = new ContaResp
            {
                IdConta = lIdConta.ToInt(),
                Conta = lNumeroConta.Substring(0, lNumeroConta.Length - 1),
                DigitoConta = lNumeroConta.Substring(lNumeroConta.Length - 1),
                Agencia = lNumeroAgencia.Substring(0, lNumeroAgencia.Length - 1),
                DigitoAgencia = lNumeroAgencia.Substring(lNumeroAgencia.Length - 1),
                StatusConta = lContaAliasBank.bankAccountStatus,
                BancoId = lBanco.Id,
                BancoNome = lBanco.Nome,
                BancoIsBbc = lBanco.IsBbc
            };

            lListaContas.Add(lContaResponse);

            return new ConsultarContaAgenciaResp
            {
                contas = lListaContas
            };
        }

        public async Task<ConsultarStatusPortadorResp> ConsultarStatus(string cnpj)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            
            lLog.Info("ConsultarStatus");
            
            var lPortador = await _cartaoRepository.ConsultarPortador(cnpj);
            
            lLog.Info("ConsultarStatus" + lPortador?.Code);
            
            if (lPortador?.Code != "404" && lPortador?.Code.ToIntSafe() != 404)
            {
                var statusPortador = new StatusPortadorResp();
                var lRetorno = lPortador;

                #region Portador PJ

                if (lRetorno?.results != null)
                {
                    var lPortadorPj = lRetorno.results.First();
                    statusPortador = new StatusPortadorResp
                    {
                        idRegistration = lPortadorPj.idRegistration,
                        nome = lPortadorPj.company.legalName,
                        status = lPortadorPj.status
                    };
                }

                #endregion

                #region Portador PF

                if (lRetorno?.items != null)
                {
                    lLog.Info("ConsultarStatus itens not null" );
                    bool encontrouPessoaAtiva = false;
                    var lContas = _cartaoRepository.ConsultarContas(null, null, cnpj)?.Result;

                    if (lContas?.Code != "404" && lContas?.Code.ToIntSafe() != 404)
                    {
                        var lConta = lContas?.content.FindAll(x => StatusConta().Contains(x.idStatusConta)).ToList();

                        if (lConta != null)
                        {
                            foreach (var conta in lConta)
                            {
                                var pessoaContaAtiva = lRetorno.items.FirstOrDefault(p => p.id == conta.idPessoa);
                                if (pessoaContaAtiva != null)
                                {
                                    encontrouPessoaAtiva = true;
                                    statusPortador = new StatusPortadorResp
                                    {
                                        idRegistration = pessoaContaAtiva.idRegistration,
                                        nome = pessoaContaAtiva.name,
                                        status = pessoaContaAtiva.status
                                    };
                                }
                                        
                            }
                        }
                    }

                    if (!encontrouPessoaAtiva)
                    {
                        var lPortadorPf = lRetorno.items.First();
                        statusPortador = new StatusPortadorResp
                        {
                            idRegistration = lPortadorPf.idRegistration,
                            nome = lPortadorPf.name,
                            status = lPortadorPf.status
                        };
                    }
                    
                }

                #endregion
                
                if (!string.IsNullOrEmpty(statusPortador.status))
                {
                    return new ConsultarStatusPortadorResp
                    {
                        Sucesso = true,
                        message = "Consulta de status de portador realizada com sucesso!",
                        StatusPortador = statusPortador
                    };
                }
            }
            
            return new ConsultarStatusPortadorResp
            {
                Sucesso = false
            }; 
        }

        public async Task<ResponseCartaoApi> ConsultarCartoes(string cpfcnpj)
        {
            var conta = await ConsultarContas(null,null,null, null, cpfcnpj);

            if (conta != null)
            {
                var primeiraContaPortador = conta.content.FirstOrDefault()?.id;

                var cartaoPortador =  await ConsultaCartaoPorConta(primeiraContaPortador);

                if (cartaoPortador?.content != null)
                {
                    var cartoesAtivos = cartaoPortador.content.FirstOrDefault(c => c.idStatus == 1 || c.idStatus == 2);
                    if (cartoesAtivos != null)
                    {
                        return new ResponseCartaoApi
                        {
                            IdConta = cartoesAtivos?.idConta,
                            IdCartao = cartoesAtivos.id,
                            Sucesso = true,
                            message = "Consulta realizada com sucesso!"
                        };
                    }
                }
            }
            
            return  new ResponseCartaoApi
            {
                Sucesso = false,
                message = "Não foi possível encontrar cartões para esse portador!"
            };
        }

        private List<int> StatusConta()
        {
            return new List<int>()
            {
                0,
                200,
                10,
                109
            };
        }

    }
}
