using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Api.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Pagamentos;
using SistemaInfo.BBC.Application.Objects.Api.Pagamento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Api.Controllers
{
    /// <summary>
    /// Classe da aplicação de pagamento
    /// </summary>
    [Route("Pagamento")]
    public class PagamentoController : ApiControllerBase
    {
        private readonly IPagamentosAppService _pagamentosAppService;

        /// <summary>
        /// Injeção de dependecias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="pagamentosAppService"></param>
        public PagamentoController(IAppEngine engine, IPagamentosAppService pagamentosAppService) : base(engine)
        {
            _pagamentosAppService = pagamentosAppService;
        }


        /// <summary>
        /// Metodo responsavel por integrar os pagamentos enviado dentro do parametro
        /// </summary>
        /// <param name="pagamentoIntegrarRequest"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Integrar")]
        public JsonResult IntegrarPagamento([FromBody] PagamentoIntegrarRequest pagamentoIntegrarRequest)
        {
            try
            {
                // origem jsl
                if (pagamentoIntegrarRequest == null)
                {

                    return ResponseBase.ResponderErro("Dados incorretos para integração.");
                }

                var lIntegrarPag = _pagamentosAppService.IntegrarPagamento(pagamentoIntegrarRequest).Result;

                return ResponseBaseApi.ResponderSucesso(lIntegrarPag);
            }
            catch (Exception e)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
        
        /// <summary>
        /// solicitação de cancelamento de pagamento
        /// </summary>
        /// <param name="pagamentoCancelar"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Cancelar")]
        public JsonResult CancelarPagamento([FromBody]PagamentoCancelarRequest pagamentoCancelar)
        {
            try
            {
                var lCancelPag = _pagamentosAppService.CancelarPagamento(pagamentoCancelar.pagamentoId.ToInt()).Result;
                return !lCancelPag.sucesso
                    ? ResponseBase.ResponderErro(lCancelPag.mensagem)
                    : ResponseBase.ResponderSucesso(lCancelPag);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }

        /// <summary>
        /// valida a aperação de cancelamento
        /// </summary>
        /// <param name="pagamentosId"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("ValidarCancelamento")]
         public JsonResult ValidarCancelamento([FromBody]ValidarPagamentoRequest pagamentosId)
         {
             try
             {
                 var lIntegrarPag = _pagamentosAppService.ValidarCancelamento(pagamentosId.pagamentosId).Result;
        
                 return ResponseBaseApi.BigJson(lIntegrarPag);
             }
             catch (Exception e)
             {
                 return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
             }
         }

        /// <summary>
        /// consulta de pagamento por cpf ou cnpj
        /// </summary>
        /// <param name="cpfCnpj"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("ConsultaContaCPF")]
        public JsonResult ConsultaContaCPF(string cpfCnpj)
        {
            var lIntegrarPag = _pagamentosAppService.ConsultaContaCPF( cpfCnpj);
            return ResponseBaseApi.ResponderSucesso(lIntegrarPag); 
        }
    }
}