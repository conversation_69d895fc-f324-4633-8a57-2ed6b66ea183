﻿using SistemaInfo.BBC.Application.Objects.Api.Cliente;
using SistemaInfo.BBC.Application.Objects.Web.Cliente;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Cliente;
using SistemaInfo.BBC.Domain.Models.Cliente.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.ClienteMapper
{
    public class ClienteMappingProfile : SistemaInfoMappingProfile
    {
        public ClienteMappingProfile()
        {
            CreateMap<Cliente, ConsultarGridCliente>()
                .ForMember(dest => dest.Cnpj, opt => opt.MapFrom(src => FormatUtils.CpfCnpj(src.CpfCnpj)))
                .ForMember(dest => dest.Celular, opt => opt.MapFrom(src => src.Celular.ToTelefoneFormato()));

            CreateMap<ClienteIntegrarApiRequest, ClienteRequest>();

            CreateMap<ClienteRequest, ClienteSalvarComRetornoCommand>();

            CreateMap<ClienteRequest, ClienteSalvarCommand>();

            CreateMap<ClienteSalvarCommand, Cliente>();

            CreateMap<ClienteStatusRequest, ClienteAlterarStatusCommand>();

            CreateMap<ClienteRequest, ClienteSalvarComRetornoCommand>();

            CreateMap<Cliente, ClienteResponse>()
                .ForMember(dest => dest.EstadoId, opt => opt.MapFrom(src => src.Cidade.Estado.Id));
        }
    }
}