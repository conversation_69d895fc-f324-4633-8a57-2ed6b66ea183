using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.PagamentoAbastecimento;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("ServicosAbastecimento")]
    public class ServicosAbastecimentoController : ApiControllerBase
    {
        private readonly IPagamentoAbastecimentoAppService _pagamentoAbastecimentoAppService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="pagamentoAbastecimentoAppService"></param>
        public ServicosAbastecimentoController(IAppEngine engine, 
            IPagamentoAbastecimentoAppService pagamentoAbastecimentoAppService) 
            : base(engine)
        {
            _pagamentoAbastecimentoAppService = pagamentoAbastecimentoAppService;
        }

        /// <summary>
        /// BAT_ABST_01: Gerador de registro e tranferência das receitas referentes as retenções/pagamentos de abastecimento.
        /// Deve ser executado a cada 24h as 02:00.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("GerarRegistroPagamentoReceita")]
        public async Task GerarRegistroPagamentoReceita() => await _pagamentoAbastecimentoAppService.ServiceGerarRegistroPagamentoReceita();
        
        /// <summary>
        /// BAT_ABST_02: Executa os pagamentos das receitas.
        /// Deve ser executado a cada 24h as 02:00.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("RealizarPagamentoReceita")]
        public async Task RealizarPagamentoReceita() => await _pagamentoAbastecimentoAppService.ServiceRealizarPagamentoReceita();
        
        /// <summary>
        /// BAT_ABST_03: Gerador de registro de pagamentos para retenções de abastecimento baseado em protoclos de pagamentos ja aprovados.
        /// Deve ser executado a cada 24h as 03:00.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("GerarRegistroPagamentoAbastecimento")]
        public async Task GerarRegistroPagamentoAbastecimento() => await _pagamentoAbastecimentoAppService.ServiceGerarRegistroPagamentoAbastecimento();
        
        /// <summary>
        /// BAT_ABST_04: Executa os pagamentos de abastecimento.
        /// Deve ser executado a cada hora.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("RealizarPagamentoAbastecimento")]
        public async Task RealizarPagamentoAbastecimento() => await _pagamentoAbastecimentoAppService.ServiceRealizarPagamentoAbastecimento();
        
        /// <summary>
        /// BAT_ABST_05: Responsavel pela geração dos lotes de retenção que não foram gerados durante a
        /// aprovação dos abastecimentos.
        /// Deve ser executado a cada hora.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("GerarRegistroRetencao")]
        public async Task GerarRegistroRetencao() => await _pagamentoAbastecimentoAppService.ServiceGerarRegistroRetencao();
    }
}