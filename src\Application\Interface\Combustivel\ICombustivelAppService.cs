using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Combustivel;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Combustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Combustivel
{
    public interface ICombustivelAppService : IAppService<ICombustivelReadRepository, ICombustivelWriteRepository>
    {
        ConsultarGridCombustivelResponse ConsultarGridCombustivel(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarGridCombustivelResponse ConsultarGridPostoCombustivel(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        CombustivelResponse ConsultarPorId(int idCombustivel);
        List<CombustivelResponse> ConsultarCombustiveis();
        Task<RespPadrao> Save(CombustivelRequest request);
        Task AlterarStatus(CombustivelStatusRequest lCombustivelStatus);
    }
}