﻿using System;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json;
using SistemaInfo.BBC.Application.Objects.Mobile.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Mobile.Veiculo;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.AutorizacaoContingecia;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Application.Objects.Web.ManutencaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ProtocoloAbastecimento;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Movida.DTO;
using SistemaInfo.BBC.Domain.External.SAP.DTO.SAP;
using SistemaInfo.BBC.Domain.Models.Abastecimento;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Commands;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.AbastecimentoMapper
{
    public class AbastecimentoMappingProfile : SistemaInfoMappingProfile
    {
        public AbastecimentoMappingProfile()
        {
            CreateMap<PostoCombustivel, CombustiveisList>()
                .ForMember(dest => dest.UnidadeMedida, opts => opts.MapFrom(s => s.Combustivel.UnidadeMedida));

            CreateMap<Abastecimento, ImpressaoComprovanteAbastecimentoMobileResponse>()
                .ForMember(dest => dest.DataAbastecimento, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.PostoApi, opts => opts.MapFrom(s => s.Posto))
                .ForMember(dest => dest.PortadorApi, opts => opts.MapFrom(s => s.Portador))
                .ForMember(dest => dest.CombustivelApi, opts => opts.MapFrom(s => s.Combustivel))
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2)))
                .ForMember(dest => dest.ValorUnitario, opts => opts.MapFrom(s => Math.Round(s.ValorUnitario, 2)))
                .ForMember(dest => dest.VeiculoApi, opts => opts.MapFrom(s => s.Veiculo));

            CreateMap<Abastecimento, ConsultarGridAbastecimento>()
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Litragem.ToString("0.000")))
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => s.ValorAbastecimento.ToString("0.000")))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.CombustivelNome, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2).ToString()))
                .ForMember(dest => dest.Status, opts =>
                    opts.MapFrom(s => s.Status == (EStatusAbastecimento) 1 ? "Aprovado" :
                        s.Status == (EStatusAbastecimento) 2 ? "Reprovado" :
                        s.Status == (EStatusAbastecimento) 3 ? "Aguardando Aprovação" :
                        "Cancelado"))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa));

            CreateMap<Abastecimento, AbastecimentoResponse>()
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2)))
                .ForMember(dest => dest.ValorUnitario, opts => opts.MapFrom(s => Math.Round(s.ValorUnitario, 2)));

            
            CreateMap<Abastecimento, AbastecimentoResponse>()
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2)))
                .ForMember(dest => dest.ValorUnitario, opts => opts.MapFrom(s => Math.Round(s.ValorUnitario, 2)));

            CreateMap<Abastecimento, AbastecimentosResponse>()
                .ForMember(ar => ar.Combustivel, opts => opts.MapFrom(a => a.Combustivel.Nome))
                .ForMember(ar => ar.Nome, opts => opts.MapFrom(a => a.Portador.Nome))
                .ForMember(ar => ar.Placa, opts => opts.MapFrom(a => a.Veiculo.Placa))
                .ForMember(ar => ar.RazaoSocial, opts => opts.MapFrom(a => a.Posto.RazaoSocial))
                .ForMember(d => d.Data,
                    opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)));

            
            CreateMap<CombustiveisList, CombustiveisListMobile>()
                .ForMember(dest => dest.ValorBomba, opts => opts.MapFrom(s => s.ValorCombustivelBomba))
                .ForMember(dest => dest.ValorBBC, opts => opts.MapFrom(s => s.ValorCombustivelBBC));

            CreateMap<Abastecimento, ConsultarGridLote>()
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2)))
                .ForMember(dest => dest.ValorMdr, opts => opts.MapFrom(s => s.ProtocoloAbastecimento.LotePagamento.PagamentoAbastecimento.FirstOrDefault().DescontoMDR))
                .ForMember(dest => dest.ValorPagamento, opts => opts.MapFrom(s => s.ProtocoloAbastecimento.LotePagamento.ValorPagamento))
                .ForMember(dest => dest.NotaXml, opts => opts.MapFrom(s => s.ProtocoloAbastecimento.NotaXml))
                .ForMember(dest => dest.ValorAcrecimoTaxa, opts => opts.MapFrom(s => s.PagamentoAbastecimentos.FirstOrDefault(x => x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Retencao).ValorAbastecimentoAcrescimoTaxa))
                .ForMember(dest => dest.Combustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.ValorTaxaEmpresa, opts => opts.MapFrom(s => s.ValorAbastecimento * s.Empresa.TaxaAbastecimento / 100));

            CreateMap<Abastecimento, ConsultarGridGridAbastecimentoPainelFinanceiro>(MemberList.None)
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.Combustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.NotaFiscal, opts => opts.MapFrom(s => s.ProtocoloAbastecimento != null ? s.ProtocoloAbastecimento.NotaFiscal : null))
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Litragem))
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => "R$ " + s.ValorAbastecimento.ToString("N2")))
                .ForMember(dest => dest.ProtocoloAbastecimentoId, opts => opts.MapFrom(s => s.ProtocoloAbastecimento != null ? s.ProtocoloAbastecimento.Id : s.ProtocoloAbastecimentoId))
                .ForMember(dest => dest.PagamentoAbastecimentoId, opts => opts.MapFrom(s => s.ProtocoloAbastecimento.LotePagamento != null ? s.ProtocoloAbastecimento.LotePagamento.PagamentoAbastecimento.First().Id : 0))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.ValorPagamento, opts => opts.MapFrom(s => s.ValorAbastecimento))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa));

            CreateMap<Abastecimento, ConsultarGridAbastecimentoProtocolo>()
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2)))
                .ForMember(dest => dest.Combustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.ItemNotaFiscal, opts => opts.MapFrom(s => s.NumeroItemXmlNota))
                .ForMember(dest => dest.PedidoSap, opts => opts.MapFrom(s => s.ProtocoloAbastecimento.NumeroPedidoSap));

            CreateMap<Abastecimento, ProtocoloAbastecimentoControleItemExport>()
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => "R$ " + s.ValorAbastecimento.ToString("F3")))
                .ForMember(dest => dest.CombustivelNome, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.ItemNf, opts => opts.MapFrom(s => s.NumeroItemXmlNota))
                .ForMember(dest => dest.CodigoAbastecimento, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.PedidoSap, opts => opts.MapFrom(s => s.ProtocoloAbastecimento.NumeroPedidoSap));

            CreateMap<Abastecimento, ConsultarGridPainelAbastecimento>()
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => Math.Round(s.ValorAbastecimento, 2)))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataBaixa, opts => opts.MapFrom(s => s.PagamentoAbastecimentos.FirstOrDefault(x => x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento && x.Status == StatusPagamentoAbastecimento.Baixado).DataBaixa.ToStringBr(FormatDateTimeMethod.ShortDateTime) ?? ""))
                .ForMember(dest => dest.CombustivelNome, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status == (EStatusAbastecimento) 1 ? "Realizado" : s.Status == (EStatusAbastecimento) 3 ? "Pendente" : "Cancelado"))
                .ForMember(dest => dest.RazaoSocial, opts => opts.MapFrom(s => s.Posto.RazaoSocial ?? s.Posto.NomeFantasia))
                .ForMember(dest => dest.Cnpj, opts => opts.MapFrom(s => s.Posto.Cnpj))
                .ForMember(dest => dest.MediaRealizada, opts => opts.MapFrom(s => s.Veiculo.ControlaAutonomia == 1 ? ((s.Odometro - s.OdometroAnterior) / s.Litragem) : 0))
                .ForMember(dest => dest.MediaSugerida, opts => opts.MapFrom(s => s.Veiculo.Modelo != null ? s.Veiculo.Modelo.MediaSugerida : 0))
                .ForMember(dest => dest.MediaMinima, opts => opts.MapFrom(s => s.Veiculo.Modelo != null ? s.Veiculo.Modelo.MediaMinima : 0))
                .ForMember(dest => dest.StatusFinanceiro, opts => opts.MapFrom(s => s.PagamentoAbastecimentos.FirstOrDefault(x => 
                        x.TipoOperacao == TipoOperacaoPagamentoAbastecimento.Pagamento).Status == StatusPagamentoAbastecimento.Baixado ? "Baixado" : 
                        s.PagamentoAbastecimentos.FirstOrDefault().Status == StatusPagamentoAbastecimento.Erro ? "Erro" : 
                        s.PagamentoAbastecimentos.FirstOrDefault().Status == StatusPagamentoAbastecimento.Reprovado ? "Reprovado" : 
                        s.PagamentoAbastecimentos.FirstOrDefault().Status == StatusPagamentoAbastecimento.PendenteAprovacao ? "Pendente" : 
                        "Processando"))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa));

            CreateMap<Abastecimento, AbastecimentoRequest>();

            CreateMap<IntegrarAbastecimentoMobileRequest, AbastecimentoRequest>();

            CreateMap<AbastecimentoRequest, AbastecimentoSalvarCommand>();

            CreateMap<Abastecimento, AbastecimentoSalvarCommand>();

            CreateMap<AbastecimentoRequest, AbastecimentoSalvarComRetornoCommand>();

            CreateMap<AbastecimentoSalvarCommand, Abastecimento>();

            CreateMap<AbastecimentoSalvarComRetornoCommand, Abastecimento>()
                .ForMember(x => x.Odometro, opts => opts.MapFrom(d => d.OdometroAtual));

            CreateMap<Abastecimento, AbastecimentoSalvarComRetornoCommand>()
                .ForMember(x => x.OdometroAtual, opts => opts.MapFrom(d => d.Odometro));
            
            CreateMap<Abastecimento, AbastecimentoExport>()
                .ForMember(dest => dest.Codigo, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Nf, opt => opt.MapFrom(src => src.ProtocoloAbastecimento != null ? src.ProtocoloAbastecimento.NotaFiscal : null))
                .ForMember(dest => dest.CodigoProtocolo, opt => opt.MapFrom(s => s.ProtocoloAbastecimento != null ? s.ProtocoloAbastecimento.Id : s.ProtocoloAbastecimentoId))
                .ForMember(dest => dest.Combustivel, opt => opt.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.Litragem, opt => opt.MapFrom(s => s.Litragem.ToString()))
                .ForMember(dest => dest.ValorAbastecimento, opt => opt.MapFrom(s => "R$ "+s.ValorAbastecimento.ToString("N3")))
                .ForMember(dest => dest.DataAbastecimento, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa));
            
            CreateMap<Abastecimento, ConsultarGridHistoricoManutencaoAbastecimento>()
                .ForMember(dest => dest.DataCadastro,opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao,opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa))
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Litragem))
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => s.ValorAbastecimento))
                .ForMember(dest => dest.Status, 
                    opts => opts.MapFrom(s => 
                        s.Status == (EStatusAbastecimento) 1 ? "Aprovado" : 
                        s.Status == (EStatusAbastecimento) 2 ? "Reprovado" : 
                        s.Status == (EStatusAbastecimento) 3 ? "Aguardando aprovação" : "Cancelado"))
                .ForMember(dest => dest.UsuarioAlteracao, opts => opts.MapFrom(s => s.UsuarioAlteracao.Nome));

            CreateMap<Abastecimento, ManutencaoAbastecimentoResponse>()
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa))
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Litragem))
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => s.ValorAbastecimento))
                .ForMember(dest => dest.Status, 
                    opts => opts.MapFrom(s => 
                        s.Status == (EStatusAbastecimento) 1 ? "Aprovado" : 
                        s.Status == (EStatusAbastecimento) 2 ? "Reprovado" : 
                        s.Status == (EStatusAbastecimento) 3 ? "Aguardando aprovação" : "Cancelado"));
            
            CreateMap<Abastecimento, ConsultarGridHistoricoAutorizacaoContingecia>()
                .ForMember(dest => dest.DataCadastro,opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao,opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.NomePosto, opts => opts.MapFrom(s => s.Posto.NomeFantasia != null ? s.Posto.NomeFantasia : s.Posto.RazaoSocial))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa))
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Litragem))
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => s.ValorAbastecimento))
                .ForMember(dest => dest.Status, 
                    opts => opts.MapFrom(s => 
                        s.Status == (EStatusAbastecimento) 1 ? "Aprovado" : 
                        s.Status == (EStatusAbastecimento) 2 ? "Reprovado" : 
                        s.Status == (EStatusAbastecimento) 3 ? "Aguardando aprovação" : "Cancelado"))
                .ForMember(dest => dest.UsuarioAlteracao, opts => opts.MapFrom(s => s.UsuarioAlteracao.Nome));
            
            CreateMap<Abastecimento, AutorizacaoContingeciaResponse>()
                .ForMember(dest => dest.DataCadastro,opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao,opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.NomeCombustivel, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.NomePosto, opts => opts.MapFrom(s => s.Posto.NomeFantasia != null ? s.Posto.NomeFantasia : s.Posto.RazaoSocial))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa))
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Litragem))
                .ForMember(dest => dest.Valor, opts => opts.MapFrom(s => s.ValorAbastecimento))
                .ForMember(dest => dest.Status, 
                    opts => opts.MapFrom(s => 
                        s.Status == (EStatusAbastecimento) 1 ? "Aprovado" : 
                        s.Status == (EStatusAbastecimento) 2 ? "Reprovado" : 
                        s.Status == (EStatusAbastecimento) 3 ? "Aguardando aprovação" : "Cancelado"))
                .ForMember(dest => dest.UsuarioAprovacaoNome, opts => opts.MapFrom(s => s.UsuarioAlteracao.Nome));

            CreateMap<Abastecimento, Pedido>()
                .ForMember(dest => dest.cnpj_forn, opts => opts.MapFrom(s => s.Posto.Cnpj))
                .ForMember(dest => dest.cnpj_filial, opts => opts.MapFrom(s => string.IsNullOrWhiteSpace(s.Posto.CnpjFaturamento) ? "" : s.Posto.CnpjFaturamento))
                .ForMember(dest => dest.cod_material, opts => opts.MapFrom(s => (s.Combustivel.CodigoExterno??"").PadLeft(18,'0')))
                .ForMember(dest => dest.nItem, opts => opts.MapFrom(s => s.NumeroItemXmlNota))
                .ForMember(dest => dest.data_abastecimento, opts => opts.MapFrom(s => s.DataCadastro.ToString("yyyyMMdd")))
                .ForMember(dest => dest.quantidade, opts => opts.MapFrom(s => s.Litragem.ToStringSafe().Replace(",",".")))
                .ForMember(dest => dest.unidade_medida, opts => opts.MapFrom(s => s.Combustivel.UnidadeMedida??""))
                .ForMember(dest => dest.protocolo, opts => opts.MapFrom(s => s.ProtocoloAbastecimentoId))
                .ForMember(dest => dest.placa, opts => opts.MapFrom(s => s.Veiculo.Placa??""))
                .ForMember(dest => dest.AbastecimentoID, opts => opts.MapFrom(s => s.Id.ToString()))
                .ForMember(dest => dest.CentroCusto, opts => opts.MapFrom(s => s.Veiculo.CentroCusto.CodigoExterno??""));

            CreateMap<ItensXmlSap, PedidoXML>()
                .ForMember(dest => dest.cod_material, opts => opts.MapFrom(s => s.CodigoProduto.PadLeft(18,'0')))
                .ForMember(dest => dest.nItem, opts => opts.MapFrom(s => Convert.ToString(s.nItem)))
                .ForMember(dest => dest.val_unit, opts => opts.MapFrom(s => Convert.ToString(s.ValorUnitarioTributo).Replace(",",".")))
                .ForMember(dest => dest.val_desc, opts => opts.MapFrom(s => Convert.ToString(s.ValorDesconto).Replace(",",".")))
                .ForMember(dest => dest.val_icms, opts => opts.MapFrom(s => s.ValorIcms.IsNullOrWhiteSpace() ? "" : Convert.ToString(s.ValorIcms).Replace(",",".")))
                .ForMember(dest => dest.quantidade, opts => opts.MapFrom(s => Convert.ToString(s.Litros).Replace(",",".")));
            
            CreateMap<ManutencaoAbastecimentoCancelamentoRequest, AbastecimentoSalvarCommand>();

            CreateMap<ManutencaoAbastecimentoCancelamentoRequest, AbastecimentoSalvarComRetornoCommand>();

            CreateMap<Abastecimento, ConsultarGridCentralPendenciasMovidaItem>()
                .ForMember(dest => dest.Litragem, opts => opts.MapFrom(s => s.Litragem.ToString("0.000")))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss")))
                .ForMember(dest => dest.PostoRazaoSocial, opts => opts.MapFrom(s => s.Posto.RazaoSocial))
                .ForMember(dest => dest.PostoCnpj, opts => opts.MapFrom(s => s.Posto.Cnpj))
                .ForMember(dest => dest.CombustivelNome, opts => opts.MapFrom(s => s.Combustivel.Nome))
                .ForMember(dest => dest.EmpresaNomeFantasia, opts => opts.MapFrom(s => s.Empresa.NomeFantasia))
                .ForMember(dest => dest.ValorAbastecimento, opts => opts.MapFrom(s => "R$ " + s.ValorAbastecimento.ToString("0.000")))
                .ForMember(dest => dest.ValorTaxaBbc, opts => opts.MapFrom(s => s.Empresa.TaxaAbastecimento.HasValue ? "R$ " + s.Empresa.TaxaAbastecimento.Value.ToString("0.000") : null))
                .ForMember(dest => dest.FuncionarioCpf, opts => opts.MapFrom(s => s.UsuarioCadastro.Cpf))
                .ForMember(dest => dest.OdometroInformado, opts => opts.MapFrom(s => s.Odometro))
                .ForMember(dest => dest.MotivoPendenciaMovida, opts => opts.MapFrom(s => JsonConvert.DeserializeObject<MovidaBase>(s.RetornoMovida).Msg))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status.GetHashCode()))
                .ForMember(dest => dest.Placa, opts => opts.MapFrom(s => s.Veiculo.Placa));
        }
    }
}