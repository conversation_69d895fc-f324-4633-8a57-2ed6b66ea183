using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Retencao;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// class retenção
    /// </summary>
    [Route("Retencao")]
    public class RetencaoController : ApiControllerBase
    {
        
        private readonly IRetencaoAppService _retencaoAppService;
        
        /// <summary>
        /// injeção de dependencias
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="retencaoAppService"></param>
        public RetencaoController(IAppEngine engine, IRetencaoAppService retencaoAppService) : base(engine)
        {
            _retencaoAppService = retencaoAppService;
        }
    }
}