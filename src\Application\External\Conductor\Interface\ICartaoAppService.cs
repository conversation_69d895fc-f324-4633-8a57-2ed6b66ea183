using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Cartao;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Individuo;
using SistemaInfo.BBC.Domain.Grid;

namespace SistemaInfo.BBC.Application.External.Conductor.Interface
{
    public interface ICartaoAppService
    {
        Task<AtribuirTitularResp> AtribuirTitular(int idCartao, string cpfCnpj);

        Task<AtribuirCartaoPrePagoResp> AtribuirCartaoPrePago(int idCartao, int idConta, string cpfCnpj);
        
        Task<ContaDock> BloquearConta(int idAccount, int idStatus);
        
        Task<ContaDock> ReativarConta(int idAccount);

        Task<CartaoResp> DesbloquearCartao(int idCartao);
        
        Task<CartaoResp> BloquearCartao(int idCartao, int idStatus, string motivo);
        
        Task<CartaoResp> CancelarCartao(int idCartao, int idStatus, string observacao);
        
        Task<UsuarioCartaoResp> ConsultaCartaoPorConta(int? idAccount);

        Task<ConsultarContaResp> ConsultarContas(int? requestTake, int? requestPage, OrderFilters requestOrder,
            List<QueryFilters> requestFilters, string cpfcnpj, bool isPortador = false);

        Task<ConsultarContaPorIdResp> ConsultarContasPorId(int idAccount);
        
        Task<ConsultarContaResp> ConsultarContasPorIdEmpresa(int? requestTake, int? requestPage, OrderFilters requestOrder,
            List<QueryFilters> requestFilters, int idEmpresa);
        
        Task<ConsultarContaResp> ConsultarContasPorIdPortador(int? requestTake, int? requestPage, OrderFilters requestOrder,
            List<QueryFilters> requestFilters, int idPortador);
    
        Task<PessoaFisicaResp> CadastrarPessoaFisica(ContaPessoaFisicaReq conta);

        Task<IndividuoResp> ConsultarPessoaFisica(string cpf);
        
        Task<ContaPessoaJuridicaResp> CadastrarContaPessoaJuridica(ContaPessoaJuridicaReq conta);
        
        Task<IndividuoJuridicoResp> ConsultarPessoaJuridica(string cnpj);
        Task<ConsultarContaAgenciaResp> ConsultarContaAgenciaPosto(string cnpj);
        Task<ConsultarContaAgenciaResp> ConsultarContaAgencia(string cnpj);
        Task<ConsultarStatusPortadorResp> ConsultarStatus(string cnpj);
        Task<ResponseCartaoApi> ConsultarCartoes(string cpfcnpj);
    }
}