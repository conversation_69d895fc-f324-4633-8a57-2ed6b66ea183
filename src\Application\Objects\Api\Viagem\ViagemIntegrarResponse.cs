namespace SistemaInfo.BBC.Application.Objects.Api.Viagem
{
    public class ViagemIntegrarResponse
    {
        public ViagemIntegrarResponse(bool sucesso, string mensagem)
        {
            Sucesso = sucesso;
            Mensagem = mensagem;
        }

        public ViagemIntegrarResponse()
        {
        }
        
        public ViagemIntegrarResponse(string mensagem) : this()
        {
            Mensagem = mensagem;
        }

        public ViagemIntegrarResponse(bool sucesso, int viagemId, int? statusViagem, string mensagem, PagamentoViagemResponse pagamento) : this()
        {
            Sucesso = sucesso;
            ViagemId = viagemId;
            StatusViagem = statusViagem;
            Mensagem = mensagem;
            Pagamento = pagamento;
        }

        public ViagemIntegrarResponse(int viagemId, string mensagem) : this()
        {
            ViagemId = viagemId;
            Mensagem = mensagem;
        }

        public bool Sucesso { get; set; }
        public int ViagemId { get; set; }
        public int? ViagemExternoId { get; set; }
        public int? StatusViagem { get; set; }
        public string Mensagem { get; set; }
        public PagamentoViagemResponse Pagamento { get; set; }
        public CiotResponse Ciot { get; set; }
    }
    
    public class PagamentoViagemResponse
    {
        public int? PagamentoEventoId { get; set; }
        public int? PagamentoExternoId { get; set; }
        public decimal? ValorParcela { get; set; }
        public decimal? ValorMotorista { get; set; }
        public int? StatusPagamento { get; set; }
        public string CódTransacao { get; set; }
        public int? FormaPagamento { get; set; }
        public string Mensagem { get; set; }
    }
    
    public class CiotResponse
    {
       
    }
}