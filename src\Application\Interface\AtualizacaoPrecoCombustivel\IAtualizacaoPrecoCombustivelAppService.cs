using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AtualizacaoPrecoCombustivel;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.AtualizacaoPrecoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.AtualizacaoPrecoCombustivel
{
    public interface IAtualizacaoPrecoCombustivelAppService : IAppService<Domain.Models.AtualizacaoPrecoCombustivel.AtualizacaoPrecoCombustivel, IAtualizacaoPrecoCombustivelReadRepository, IAtualizacaoPrecoCombustivelWriteRepository>
    {
        ConsultarGridHistoricoAtualizacaoPrecoCombustivelResponse ConsultarGridHistoricoSolicitacoesPendentes(int postoId, DateTime dtInicial, DateTime dtFinal, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> SaveSolicitacao(AtualizacaoPrecoCombustivelRequest request);
        Task<RespPadrao> SaveAprovacao(AtualizacaoPrecoCombustivelRequest request);
        Task<RespPadrao> CancelarSolicitacao(AtualizacaoPrecoCombustivelCancelarRequest request);
        List<AtualizacaoPrecoCombustivelResponse> SolicitacoesPendentes(int idPosto, DateTime startDate, DateTime endDate);
    }
}