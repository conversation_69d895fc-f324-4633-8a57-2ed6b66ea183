using System;
using System.IO;
using System.Net.Mail;
using System.Threading.Tasks;
using NLog;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Components.Email;

namespace SistemaInfo.BBC.Application.Email.Usuario
{
    public class EmailUsuarioPrimeiroAcesso
    {
        public static async Task<RespPadrao> EnviarEmail(INotificationEmailExecutor notificationEmailExecutor, string nomeUsuario, string novaSenha, string destinatario, string novoUsuario)
        {
            try
            {
                var lCaminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

                using (var lStreamReader =
                    new StreamReader(lCaminhoAplicacao + @"Content\Email\Usuario\primeiro-acesso-usuario.html"))
                {
                    var lEmailHtml = await lStreamReader.ReadToEndAsync();

                    lEmailHtml = lEmailHtml
                        .Replace("{EMAIL_TITLE}", "PRIMEIRO ACESSO")
                        .Replace("{EMAIL_BODY}",
                            $"{(string.IsNullOrWhiteSpace(nomeUsuario) ? "Olá!" : $"Olá, {nomeUsuario}!")}" +
                            "<br>Bem vindo(a) ao sistema BBC Controle! Suas informações de acesso se encontram abaixo. " +
                            "<br>Essa senha é provisória e será expirada em breve. " +
                            "<br>Ao realizar o primeiro login, será solicitado automaticamente a mudança da senha. " +
                            "<br>Ao cadastrar sua nova senha siga as regras abaixo: " +
                            "<br>Quantidade mínima de 8 caracteres, ao menos uma letra maiúscula, uma minúscula, um número e um caractere especial.")
                        .Replace("{STYLE_USUARIO}", $"style='display: {(string.IsNullOrEmpty(novoUsuario) ? "none" : "block")}'")
                        .Replace("{NOVO_USUARIO}", novoUsuario)
                        .Replace("{NOVA_SENHA}", novaSenha);

                    var lEmailView = AlternateView.CreateAlternateViewFromString(lEmailHtml, null, "text/html");

                    await notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                    {
                        To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                        Priority = MailPriority.High,
                        Subject = "PRIMEIRO ACESSO",
                        IsBodyHtml = true,
                        AlternateView = lEmailView
                    });

                    return new RespPadrao
                    {
                        sucesso = true,
                        mensagem = "Email enviado com sucesso!"
                    };
                }
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e);
                return new RespPadrao
                {
                    sucesso = false,
                    mensagem = "Houve um erro ao enviar o e-mail ao usuário."
                };
            }
        }
    }
}