using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Cliente;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Cliente;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Cliente.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Cliente
{
    public interface IClienteAppService : IAppService<Domain.Models.Cliente.Cliente, IClienteReadRepository, IClienteWriteRepository>
    {
        ConsultarGridClienteResponse ConsultarGridCliente(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        
        ConsultarGridClienteResponse ConsultarComboCliente(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> Save(ClienteRequest lClienteReq);
        ClienteResponse ConsultarPorId(int idCliente);
        Task AlterarStatus(ClienteStatusRequest lClienteStatus);
        Task<ClienteConsultarApiResponse> Consultar(ClienteConsultarApiRequest request);
        Task<RespPadraoApi> Integrar(ClienteIntegrarApiRequest request);
    }
}