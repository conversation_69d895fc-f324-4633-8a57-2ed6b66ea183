using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Pedagio;
using SistemaInfo.BBC.Application.Objects.Api.Pedagio;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// Classe da aplicação de pagamento
    /// </summary>
    [Route("Pedagio")]
    public class PedagioController : ApiControllerBase
    {
        private readonly IPedagioIntegracaoAppService _pedagioIntegracaoAppService;

        /// <summary>
        /// Injeção de dependecias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="pedagioIntegracaoAppService"></param>
        public PedagioController(IAppEngine engine, IPedagioIntegracaoAppService pedagioIntegracaoAppService) : base(engine)
        {
            _pedagioIntegracaoAppService = pedagioIntegracaoAppService;
        }

        /// <summary>
        /// Método responsável por realizar o pagamento de pedagio
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("IntegrarPagamentoPedagio")]
        public async Task<JsonResult> IntegrarPagamentoPedagio(
            [FromBody] IntegrarPagamentoPedagioRequest request) =>
            ResponseBase.Responder(await _pedagioIntegracaoAppService.IntegrarPagamentoPedagio(request));

        /// <summary>
        /// Método responsável por realizar o pagamento de pedagio
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("ComplementarPagamentoPedagio")]
        public async Task<JsonResult> ComplementarPagamentoPedagio(
            [FromBody] ComplementarPagamentoPedagioRequest request) =>
            ResponseBase.Responder(await _pedagioIntegracaoAppService.ComplementarPagamentoPedagio(request));
        
        /// <summary>
        /// Método responsável por realizar o cancelamento do pagamento de pedagio
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("CancelarPagamentoPedagio")]
        public async Task<JsonResult> CancelarPagamentoPedagio(
            [FromBody] CancelarPagamentoPedagioRequest request) =>
            ResponseBase.Responder(await _pedagioIntegracaoAppService.CancelarPagamentoPedagio(request)); 
        /// <summary>
        /// Método responsável por realizar o cancelamento do pagamento de pedagio
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("CancelarComplementoPagamentoPedagio")]
        public async Task<JsonResult> CancelarComplementoPagamentoPedagio(
            [FromBody] CancelarComplementoPagamentoPedagioRequest request) =>
            ResponseBase.Responder(await _pedagioIntegracaoAppService.CancelarComplementoPagamentoPedagio(request));
    }
}