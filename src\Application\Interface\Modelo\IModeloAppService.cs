using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Modelo;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Modelo.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Modelo
{
    public interface IModeloAppService : IAppService<Domain.Models.Modelo.Modelo, IModeloReadRepository, IModeloWriteRepository>
    {
        ConsultarGridModeloResponse ConsultarGridModelo(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ModeloResponse ConsultarPorId(int idModelo);
        Task<RespPadrao> Save(ModeloRequest lModeloReq, bool integracao = false);
        Task AlterarStatus(ModeloStatusRequest lModeloStatus);
    }
}