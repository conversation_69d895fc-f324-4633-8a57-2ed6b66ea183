﻿using SistemaInfo.BBC.Application.Objects.Web.CentroCusto;
using SistemaInfo.BBC.Domain.Models.CentroCusto;
using SistemaInfo.BBC.Domain.Models.CentroCusto.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.CentroCustoMapper
{
    public class CentroCustoMappingProfile : SistemaInfoMappingProfile
    {
        public CentroCustoMappingProfile()
        {
            CreateMap<CentroCustoRequest, CentroCustoSalvarCommand>();
            
            CreateMap<CentroCustoRequest, CentroCustoAlterarStatusCommand>();
            
            CreateMap<CentroCustoRequest, CentroCustoAlterarVinculoFilialCommand>();

            CreateMap<CentroCustoRequest, CentroCustoSalvarComRetornoCommand>();

            CreateMap<CentroCustoSalvarCommand, CentroCusto>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<CentroCustoSalvarComRetornoCommand, CentroCusto>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<CentroCustoStatusRequest, CentroCustoAlterarStatusCommand>();
            
            CreateMap<CentroCustoAlterarVinculoFilialRequest, CentroCustoAlterarVinculoFilialCommand>();
            
            CreateMap<CentroCustoExcluirVinculoCommand, CentroCusto>()
                .ForMember(a => a.Id, opts => opts.MapFrom(d => d.Id))
                .ForMember(a => a.Descricao, opts => opts.MapFrom(d => d.Descricao))
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo))
                .ForMember(a => a.CodigoExterno, opts => opts.MapFrom(d => d.CodigoExterno))
                .ForMember(a => a.Descricao, opts => opts.MapFrom(d => d.Descricao));

            CreateMap<CentroCusto, CentroCustoResponse>()
                .ForMember(dest => dest.Filial, opts => opts.MapFrom(s => s.Empresa.NomeFantasia.IsNullOrWhiteSpace()? s.Filial.RazaoSocial : s.Filial.NomeFantasia))
                .ForMember(dest => dest.Empresa, opts => opts.MapFrom(s => s.Empresa.NomeFantasia.IsNullOrWhiteSpace()? s.Empresa.RazaoSocial : s.Empresa.NomeFantasia));
        }
    }
}