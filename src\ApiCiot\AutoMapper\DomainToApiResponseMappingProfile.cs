using AutoMapper;
namespace SistemaInfo.BBC.ApiCiot.AutoMapper
{
    /// <summary>
    /// Class responsavel pela resposta de profile do dominio para a api
    /// </summary>
    public class DomainToApiResponseProfile : Profile
    {
        /// <summary>
        /// Regras para resposta da Profile
        /// </summary>
        public DomainToApiResponseProfile()
        {
            AllowNullCollections = false;
            AllowNullDestinationValues = false;
            EmpresaConfig();
        }

        private void EmpresaConfig()
        {//LUI
         /*   CreateMap<EmpresaConfig, EmpresaConfigApiResponse>()
                .ForMember(d => d.Cnpj, opts => opts.MapFrom(s => s.Empresa.Cnpj))
                .ForMember(d => d.Nome, opts => opts.MapFrom(s => s.Empresa.Nome));

            CreateMap<MessageBroker, EmpresaConfigApiResponse.MessageBrokerApiResponse>()
                .ForMember(d => d.Server, opts => opts.MapFrom(s => s.ExternalServer)); // SIM, é isso mesmo! Só existe o endereço externo para os consumidores*/
        }
    }
}