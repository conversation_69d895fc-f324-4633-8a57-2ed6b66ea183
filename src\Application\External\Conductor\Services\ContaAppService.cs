using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Objects.External.Conductor.Conta;
using SistemaInfo.BBC.Domain.External.Conductor.Interface;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.External.Conductor.Services
{
    public class ContaAppService : IContaAppService
    {
        
        private ICartaoRepository _cartaoRepository;
        private IUsuarioRepository _usuarioRepository;
        
        
        public ContaAppService(ICartaoRepository cartaoRepository, IUsuarioRepository usuarioRepository)
        {
            _cartaoRepository = cartaoRepository;
            _usuarioRepository = usuarioRepository;
        }
        
        public async Task<GridExtratoResponse> ConsultarExtrato(int requestTipoExtrato, int requestTake,
            int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters, int idconta, string dataInicio, string dataFim)
        {
            var retorno = await _cartaoRepository.ConsultarExtrato(
                requestTipoExtrato, 
                requestTake, 
                requestPage - 1, 
                idconta, dataInicio, dataFim);
            var result = new GridExtratoResponse() ;
            
            if(retorno.content == null)
                result.Registros = new List<GridRegistrosExtratoResponse>();
            else
                result.Registros = retorno.content.Select(c => new GridRegistrosExtratoResponse()
                {
                    dataOrigem = !c.transaction_date.IsNullOrWhiteSpace() ?  c.transaction_date.ToDateTime().ToString("dd/MM/yyyy") : c.origin_date.ToDateTime().ToString("dd/MM/yyyy"),
                    valorBRL = c.BRL_value.FormatMoney(),
                    descricaoAbreviada = c.short_description,
                    flagCredito = c.credit_flag
                }).ToList();

            result.TotalRegistros = retorno.Metadata.Pagination.total_elements_count;
            return result;
        }

        public SaldoResponse ConsultarSaldo(int idConta)
        {
            var retorno = _cartaoRepository.ConsultarSaldo(idConta.ToString()).Result;
            
            return new SaldoResponse()
            {
                Saldo = retorno.saldoDisponivelGlobal.FormatMoney()
            };
        }
        
        // public PessoaFisicaResp ConsultaUsuarioConductorAsync(string usuario)
        // {
        //     var lusuario = ConsultaUsuarioConductor(usuario).GetAwaiter().GetResult();
        //     
        //     if (lusuario == null) return null;
        //     
        //     //PF
        //     if (lusuario.items != null)
        //         return lusuario;
        //         
        //     //PJ
        //     if(lusuario.content != null)
        //         lusuario.items = Mapper.Map<List<ItensUsuarioConductor>>(lusuario.content);
        //
        //     return lusuario.items == null ? null : lusuario;
        //
        // }
    }
}