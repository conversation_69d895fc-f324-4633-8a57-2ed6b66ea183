﻿using System;
using System.Globalization;
using Newtonsoft.Json;
using SistemaInfo.BBC.Application.Objects.Api.Pagamento;
using SistemaInfo.BBC.Application.Objects.Web.Pagamentos;
using SistemaInfo.BBC.Application.Objects.Web.PainelPagamento;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Pagamentos;
using SistemaInfo.BBC.Domain.Models.Pagamentos.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.PagamentosMapper
{
    public class PagamentosMappingProfile : SistemaInfoMappingProfile
    {
        public PagamentosMappingProfile()
        {


            CreateMap<PagamentosAdicionarCommand, PagamentoIntegrar>();
            CreateMap<PagamentosRequest, PagamentosAdicionarCommand>()
                .ForMember(dest => dest.valor, opt => opt.MapFrom(src => Decimal.Parse(src.Val<PERSON>, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.DataPrevisaoPagamento) ? DateTime.Now : src.DataPrevisaoPagamento.ToDateTime()))
                .ForMember(dest => dest.formaPagamento, opt => opt.MapFrom(src => src.FormaPagamento))
                .ForMember(dest => dest.tipo, opt => opt.MapFrom(src => src.Tipo))
                .ForMember(dest => dest.ContaOrigem, opt => opt.MapFrom(src => src.contaOrigem))
                .ForMember(dest => dest.ContaDestino, opt => opt.MapFrom(src => src.contaDestino))
                .ForMember(dest => dest.ciotId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.CiotId) ? (int?) null : Convert.ToInt32(src.CiotId)))
                .ForMember(dest => dest.idcontaTransferencia, opt => opt.MapFrom(src => src.contaTransferencia));

            CreateMap<PagamentosAdicionarCommand, Pagamentos>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.id))
                .ForMember(dest => dest.IdContaTransferencia, opt => opt.MapFrom(src => src.idcontaTransferencia))
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.tipo))
                .ForMember(dest => dest.BancoId, opt => opt.MapFrom(src => src.BancoId))
                .ForMember(dest => dest.Conta, opt => opt.MapFrom(src => src.conta))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.valor))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.CiotId, opt => opt.MapFrom(src => src.ciotId))
                .ForMember(dest => dest.Agencia, opt => opt.MapFrom(src => src.agencia))
                .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(src => src.dataBaixa))
                .ForMember(dest => dest.EmpresaId, opt => opt.MapFrom(src => src.empresaId))
                .ForMember(dest => dest.PortadorId, opt => opt.MapFrom(src => src.portadorId))
                .ForMember(dest => dest.TipoConta, opt => opt.MapFrom(src => src.tipoConta))
                .ForMember(dest => dest.ContaOrigem, opt => opt.MapFrom(src => src.ContaOrigem))
                .ForMember(dest => dest.ContaDestino, opt => opt.MapFrom(src => src.ContaDestino))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.formaPagamento))
                .ForMember(dest => dest.PercentualTransferencia, opt => opt.MapFrom(src => src.percentualTransferencia))
                .ForMember(dest => dest.EtapaErroIntegracao, opt => opt.MapFrom(src => src.etapaErroIntegracao))
                .ForMember(dest => dest.IdPagamentoExterno, opt => opt.MapFrom(src => src.idPagamentoExterno))
                .ForMember(dest => dest.CpfContaTransferencia, opt => opt.MapFrom(src => src.cpfcontaTransferencia));

            CreateMap<PagamentosRequest, PagamentosAtualizarCommand>()
                .ForMember(dest => dest.valor, opt => opt.MapFrom(src => Decimal.Parse(src.Valor, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.formaPagamento, opt => opt.MapFrom(src => src.FormaPagamento))
                .ForMember(dest => dest.tipo, opt => opt.MapFrom(src => src.Tipo))
                .ForMember(dest => dest.ciotId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.CiotId) ? (int?) null : Convert.ToInt32(src.CiotId)))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.DataPrevisaoPagamento) ? DateTime.Now : src.DataPrevisaoPagamento.ToDateTime()))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.idcontaTransferencia, opt => opt.MapFrom(src => src.contaTransferencia));

            CreateMap<PagamentosAtualizarCommand, Pagamentos>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.id))
                .ForMember(dest => dest.IdContaTransferencia, opt => opt.MapFrom(src => src.idcontaTransferencia))
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.tipo))
                .ForMember(dest => dest.BancoId, opt => opt.MapFrom(src => src.BancoId))
                .ForMember(dest => dest.Conta, opt => opt.MapFrom(src => src.conta))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.valor))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.CiotId, opt => opt.MapFrom(src => src.ciotId))
                .ForMember(dest => dest.Agencia, opt => opt.MapFrom(src => src.agencia))
                .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(src => src.dataBaixa))
                .ForMember(dest => dest.EmpresaId, opt => opt.MapFrom(src => src.empresaId))
                .ForMember(dest => dest.PortadorId, opt => opt.MapFrom(src => src.portadorId))
                .ForMember(dest => dest.TipoConta, opt => opt.MapFrom(src => src.tipoConta))
                .ForMember(dest => dest.ContaOrigem, opt => opt.MapFrom(src => src.ContaOrigem))
                .ForMember(dest => dest.ContaDestino, opt => opt.MapFrom(src => src.ContaDestino))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.formaPagamento))
                .ForMember(dest => dest.PercentualTransferencia, opt => opt.MapFrom(src => src.percentualTransferencia))
                .ForMember(dest => dest.EtapaErroIntegracao, opt => opt.MapFrom(src => src.etapaErroIntegracao))
                .ForMember(dest => dest.IdPagamentoExterno, opt => opt.MapFrom(src => src.idPagamentoExterno))
                .ForMember(dest => dest.CpfContaTransferencia, opt => opt.MapFrom(src => src.cpfcontaTransferencia));

            CreateMap<PagamentosStatusRequest, PagamentosAlterarStatusCommand>();

            CreateMap<PagamentosAlterarStatusCommand, Pagamentos>();


            CreateMap<PagamentoIntegrar, PagamentosAdicionarCommand>()
                .ForMember(dest => dest.id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.valor, opt => opt.MapFrom(src => Decimal.Parse(src.Valor, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.formaPagamento, opt => opt.MapFrom(src => src.FormaPagamento))
                .ForMember(dest => dest.tipo, opt => opt.MapFrom(src => src.Tipo))
                .ForMember(dest => dest.ciotId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.CiotId) ? (int?) null : Convert.ToInt32(src.CiotId)))
                .ForMember(dest => dest.conta, opt => opt.MapFrom(src => src.Conta))
                .ForMember(dest => dest.agencia, opt => opt.MapFrom(src => src.Agencia))
                .ForMember(dest => dest.BancoId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.Banco) ? null : src.Banco))
                .ForMember(dest => dest.tipoConta, opt => opt.MapFrom(src => src.TipoConta))
                .ForMember(dest => dest.idcontaTransferencia, opt => opt.MapFrom(src => src.IdContaTransferencia.HasValue ? src.IdContaTransferencia.ToString() : ""))
                .ForMember(dest => dest.idPagamentoExterno, opt => opt.MapFrom(src => src.IdPagamentoExterno))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento == null ? DateTime.Today : src.DataPrevisaoPagamento));

            CreateMap<PagamentoIntegrar, PagamentosAtualizarCommand>()
                .ForMember(dest => dest.id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.valor, opt => opt.MapFrom(src => Decimal.Parse(src.Valor, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.formaPagamento, opt => opt.MapFrom(src => src.FormaPagamento))
                .ForMember(dest => dest.tipo, opt => opt.MapFrom(src => src.Tipo))
                .ForMember(dest => dest.ciotId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.CiotId) ? (int?) null : Convert.ToInt32(src.CiotId)))
                .ForMember(dest => dest.conta, opt => opt.MapFrom(src => src.Conta))
                .ForMember(dest => dest.agencia, opt => opt.MapFrom(src => src.Agencia))
                .ForMember(dest => dest.BancoId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.Banco) ? null : src.Banco))
                .ForMember(dest => dest.tipoConta, opt => opt.MapFrom(src => src.TipoConta))
                .ForMember(dest => dest.idcontaTransferencia, opt => opt.MapFrom(src => src.IdContaTransferencia.HasValue ? src.IdContaTransferencia.ToString() : "")).ForMember(dest => dest.idPagamentoExterno, opt => opt.MapFrom(src => src.IdPagamentoExterno))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento == null ? DateTime.Today : src.DataPrevisaoPagamento));

            CreateMap<PagamentoIntegrar, PagamentosAlterarEtapaCommand>()
                .ForMember(dest => dest.id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.valor, opt => opt.MapFrom(src => Decimal.Parse(src.Valor, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.formaPagamento, opt => opt.MapFrom(src => src.FormaPagamento))
                .ForMember(dest => dest.tipo, opt => opt.MapFrom(src => src.Tipo))
                .ForMember(dest => dest.ciotId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.CiotId) ? (int?) null : Convert.ToInt32(src.CiotId)))
                .ForMember(dest => dest.conta, opt => opt.MapFrom(src => src.Conta))
                .ForMember(dest => dest.agencia, opt => opt.MapFrom(src => src.Agencia))
                .ForMember(dest => dest.BancoId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.Banco) ? null : src.Banco))
                .ForMember(dest => dest.tipoConta, opt => opt.MapFrom(src => src.TipoConta))
                .ForMember(dest => dest.etapaErroIntegracao, opt => opt.MapFrom(src => src.EtapaErroIntegracao));

            CreateMap<PagamentosAlterarEtapaCommand, Pagamentos>();

            CreateMap<PagamentoIntegrarRequest, PagamentoIntegrar>()
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento == null ? DateTime.Today : src.DataPrevisaoPagamento));

            CreateMap<Pagamentos, ConsultaGridPagamentoItem>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Ciot,
                    opt => opt.MapFrom(src => src.Ciot != null ? (src.Ciot.Ciot + "/" + src.Ciot.Verificador) : ""))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.Portador.Nome))
                .ForMember(dest => dest.CpfCnpj, opt => opt.MapFrom(src => FormatUtils.CpfCnpj(src.Portador.CpfCnpj)))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.Tipo))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.FormaPagamento))
                .ForMember(dest => dest.DataCadastro,
                    opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => StringHelper.FormatMoney(src.Valor)))
                .ForMember(dest => dest.Descricao, opts => opts.MapFrom(s => !string.IsNullOrWhiteSpace(s.Descricao)
                            ? JsonConvert.DeserializeObject<IntegrarPagamentoDescricaoRequest>(s.Descricao).description
                                .Replace("Número de Referência : ", "")
                            : null))
                .ForMember(dest => dest.OrigemPagamento, opt => opt.MapFrom(src => src.OrigemPagamento))
                .ForMember(dest => dest.NomeEmpresa, opt => opt.MapFrom(src => src.Empresa.RazaoSocial));

            CreateMap<Pagamentos, ConsultarPorIdPagamentoResponse>()
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.FormaPagamento))
                .ForMember(dest => dest.nomeEmpresa, opt => opt.MapFrom(src => src.Empresa.RazaoSocial))
                .ForMember(dest => dest.nomePortador, opt => opt.MapFrom(src => src.Portador.Nome))
                .ForMember(dest => dest.Tipo, opt => opt.MapFrom(src => src.Tipo))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.Valor.ToString("N2")))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.descricaoBanco, opt => opt.MapFrom(src => src.Banco.Nome));

            CreateMap<Pagamentos, ConsultarPagamentos>()
                .ForMember(d => d.Ciot, opts => opts.MapFrom(s => s.Ciot.Ciot + "/" + s.Ciot.Verificador))
                .ForMember(d => d.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.DataInicio, opts => opts.MapFrom(s => s.Ciot.DataInicio))
                .ForMember(d => d.DataFim, opts => opts.MapFrom(s => s.Ciot.DataFim))
                .ForMember(d => d.Tipo, opts => opts.MapFrom(s => s.Tipo))
                .ForMember(d => d.Status, opts => opts.MapFrom(s => s.Status))
                .ForMember(d => d.FormaPagamento, opts => opts.MapFrom(s => s.FormaPagamento))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.Valor)));

            CreateMap<Pagamentos, PagamentoIntegrar>()
                .ForMember(d => d.Id, opts => opts.MapFrom(s => s.Id.ToString()))
                .ForMember(d => d.Tipo, opts => opts.MapFrom(s => Convert.ToInt32(s.Tipo)))
                .ForMember(d => d.Banco, opts => opts.MapFrom(s => s.BancoId))
                .ForMember(d => d.Valor, opts => opts.MapFrom(s => s.Valor.ToString()))
                .ForMember(d => d.CiotId, opts => opts.MapFrom(s => s.CiotId.ToString()))
                .ForMember(d => d.FormaPagamento, opts => opts.MapFrom(s => Convert.ToInt32(s.FormaPagamento)))
                .ForMember(d => d.IdContaOrigem, opts => opts.MapFrom(s => Convert.ToInt32(s.ContaOrigem)))
                .ForMember(d => d.IdContaDestino, opts => opts.MapFrom(s => Convert.ToInt32(s.ContaDestino)))
                .ForMember(d => d.IdContaTransferencia, opts => opts.MapFrom(s => string.IsNullOrEmpty(s.IdContaTransferencia) ? null : s.IdContaTransferencia))
                .ForMember(dest => dest.IdPagamentoExterno, opt => opt.MapFrom(src => src.IdPagamentoExterno))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento ?? DateTime.Today))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status));

            CreateMap<Pagamentos, PagamentoIntegrarRequest>()
                .ForMember(d => d.Id, opts => opts.MapFrom(s => s.Id.ToString()))
                .ForMember(d => d.Tipo, opts => opts.MapFrom(s => Convert.ToInt32(s.Tipo)))
                .ForMember(d => d.Banco, opts => opts.MapFrom(s => s.BancoId))
                .ForMember(d => d.Valor, opts => opts.MapFrom(s => s.Valor.ToString()))
                .ForMember(d => d.CiotId, opts => opts.MapFrom(s => s.CiotId.ToString()))
                .ForMember(d => d.FormaPagamento, opts => opts.MapFrom(s => Convert.ToInt32(s.FormaPagamento)))
                .ForMember(d => d.IdContaOrigem, opts => opts.MapFrom(s => Convert.ToInt32(s.ContaOrigem)))
                .ForMember(d => d.CpfContaDestino, opts => opts.MapFrom(s => s.Portador.CpfCnpj))
                .ForMember(d => d.CpfContaTransferencia, opts => opts.MapFrom(s => s.CpfContaTransferencia))
                .ForMember(d => d.PercentualTransferencia, opts => opts.MapFrom(s => s.PercentualTransferencia))
                .ForMember(d => d.IdContaDestino, opts => opts.MapFrom(s => Convert.ToInt32(s.ContaDestino)))
                .ForMember(d => d.IdContaTransferencia, opts => opts.MapFrom(s => string.IsNullOrEmpty(s.IdContaTransferencia) ? null : s.IdContaTransferencia))
                .ForMember(dest => dest.IdPagamentoExterno, opt => opt.MapFrom(src => src.IdPagamentoExterno))
                .ForMember(dest => dest.DataPrevisaoPagamento, opt => opt.MapFrom(src => src.DataPrevisaoPagamento ?? DateTime.Today));
        }
    }
}