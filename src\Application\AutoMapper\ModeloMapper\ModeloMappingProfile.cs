﻿using System;
using System.Globalization;
using SistemaInfo.BBC.Application.Objects.Web.Modelo;
using SistemaInfo.BBC.Domain.Models.Modelo;
using SistemaInfo.BBC.Domain.Models.Modelo.Commands;

namespace SistemaInfo.BBC.Application.AutoMapper.ModeloMapper
{
    public class ModeloMappingProfile : SistemaInfoMappingProfile
    {
        public ModeloMappingProfile()
        {
            CreateMap<Modelo, ModeloResponse>();

            CreateMap<Modelo, ConsultarGridModelo>();

            CreateMap<ModeloRequest, ModeloSalvarCommand>()
                .ForMember(dest => dest.MediaMinima, opt => opt.MapFrom(src => Decimal.Parse(src.MediaMinima, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.MediaSugerida, opt => opt.MapFrom(src => Decimal.Parse(src.MediaSugerida, NumberStyles.Any, new CultureInfo("pt-BR"))));
            
            CreateMap<ModeloRequest, ModeloAlterarStatusCommand>()
                .ForMember(dest => dest.MediaMinima, opt => opt.MapFrom(src => Decimal.Parse(src.MediaMinima, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.MediaSugerida, opt => opt.MapFrom(src => Decimal.Parse(src.MediaSugerida, NumberStyles.Any, new CultureInfo("pt-BR"))));
            
            CreateMap<ModeloRequest, ModeloSalvarComRetornoCommand>()
                .ForMember(dest => dest.MediaMinima, opt => opt.MapFrom(src => Decimal.Parse(src.MediaMinima, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.MediaSugerida, opt => opt.MapFrom(src => Decimal.Parse(src.MediaSugerida, NumberStyles.Any, new CultureInfo("pt-BR"))));
            
            CreateMap<ModeloSalvarCommand, Modelo>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<ModeloSalvarComRetornoCommand, Modelo>()
                .ForMember(a => a.Ativo, opts => opts.MapFrom(d => d.Ativo));

            CreateMap<ModeloStatusRequest, ModeloAlterarStatusCommand>();
        }
    }
}