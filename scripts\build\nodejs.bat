:: Init
echo off
set npmpath="C:\Program Files\nodejs\npm.cmd"
if not exist %npmpath% (
	call :EndError npm.cmd nao localizado
)
set gulppath="C:\Program Files\nodejs\gulp.cmd"
if not exist %gulppath% (
	set gulppath=%userprofile%"\AppData\Roaming\npm\gulp.cmd"
)
if not exist %gulppath% (
	call %npmpath% install -g gulp -y
	call %npmpath% install -g gulp-cli
	call %npmpath% fund
	call %npmpath% install -g bower
	set gulppath=%userprofile%"\AppData\Roaming\npm\gulp.cmd"
)
if not exist %gulppath% (
	call :EndError gulp.cmd nao localizado
)
call :log Diretorio atual: %~dp0
call :log Alterando para raiz do repositorio
cd "%~dp0%\..\.."
call :log Diretorio atual: %cd%
set root_dir=%cd%

echo.
call :log ==========================================================================================
call :log Build Front
call :log ==========================================================================================

call :Build "%root_dir%\src\" "Front"
call :Build "%root_dir%\src\" "Front.Posto"

echo.
goto :EndSuccess
exit %errorlevel%

:Build
	cd %~1%~2
	call :log Diretorio atual: %cd%
	if not exist gulp (
		call %npmpath% install gulp
	)
	call %gulppath% clean
	call %gulppath% build
	if not %errorlevel% == 0 (
		call :EndError Falha no build %~2
		exit 1
	)
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo %~n0%~x0^> %*
	exit /B 0
:EndError
    echo.
    call :log ==========================================================================================
    call :log %*
    call :log ::::: %0 end :::::
    call :log ==========================================================================================
    echo.
    exit 1
:EndSuccess
    echo.
    call :log ==========================================================================================
    call :log Build conluído
    call :log ::::: %0 end :::::
    call :log ==========================================================================================
    echo.
	exit 0