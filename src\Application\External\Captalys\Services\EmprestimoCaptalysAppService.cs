using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.Application.External.Captalys.Interface;
using SistemaInfo.BBC.Application.Interface.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;
using SistemaInfo.BBC.Domain.External.Captalys.DTO.Emprestimo;
using SistemaInfo.BBC.Domain.External.Captalys.Interface;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Repository;
using SistemaInfo.BBC.Domain.Models.Parametros;
using SistemaInfo.BBC.Domain.Models.Parametros.Repository;

namespace SistemaInfo.BBC.Application.External.Captalys.Services
{
    public class EmprestimoCaptalysAppService : IEmprestimoCaptalysAppService
    {
        private readonly IEmprestimoCaptalysRepository _emprestimoCaptalysRepository;
        private readonly IEmprestimoReadRepository _emprestimoReadRepository;
        private readonly IEmprestimoAppService _emprestimoService;
        private readonly IParametrosReadRepository _parametrosReadRepository;

        public EmprestimoCaptalysAppService(IEmprestimoCaptalysRepository emprestimoCaptalysRepository,
            IEmprestimoReadRepository emprestimoReadRepository, IEmprestimoAppService emprestimoService,
            IParametrosReadRepository parametrosReadRepository)
        {
            _emprestimoCaptalysRepository = emprestimoCaptalysRepository;
            _emprestimoReadRepository = emprestimoReadRepository;
            _emprestimoService = emprestimoService;
            _parametrosReadRepository = parametrosReadRepository;
        }

        public async Task ServiceIntegrarEmprestimosCaptalys()
        {
            try
            {
                var retorno = await _emprestimoCaptalysRepository.ConsultaEmprestimos();
                if (retorno == null) return;
                await IntegrarEmprestimosCaptalys(retorno);
            }
            catch (Exception e)
            {
                LogManager.GetCurrentClassLogger().Error(e, "BAT_CPLY_01 ERRO");
            }
        }

        private async Task IntegrarEmprestimosCaptalys(List<EmprestimoResp> emprestimosCaptalys)
        {
            var lLog = LogManager.GetCurrentClassLogger();
            try
            {
                foreach (var emprestimo in emprestimosCaptalys)
                {
                    var lEmprestimoReq = Mapper.Map<EmprestimoRequest>(emprestimo);

                    await _emprestimoService.IntegrarEmprestimo(lEmprestimoReq);

                    var lLink = await _parametrosReadRepository
                        .GetParametrosListAsync(Parametros.ParametroGeralId, Parametros.TipoDoParametro.CodigoLinkEmpresa, Parametros.TipoDoValor.String);

                    foreach (var link in lLink)
                    {
                        var request = JsonConvert.SerializeObject(new
                        {
                            cpfCnpjMotProp = lEmprestimoReq.CpfCnpjPortador
                        });

                        var response = new HttpClient();
                        var content = new StringContent(request, Encoding.UTF8, "application/json");

                        var result = response.PostAsync(link.Valor, content).Result;
                        
                        if(!result.IsSuccessStatusCode)
                        {
                            lLog.Error($"BAT_CPLY_01 ERRO: Falha em realizar o envio para {link.Valor}. Response: {await result.Content.ReadAsStringAsync()}");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                lLog.Error(e, "BAT_CPLY_01 ERRO");
            }
        }
    }
}