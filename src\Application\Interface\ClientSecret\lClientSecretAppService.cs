using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecret;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.ClientSecret.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.ClientSecret
{
    public interface IClientSecretAppService : IAppService<Domain.Models.ClientSecret.ClientSecret, IClientSecretReadRepository,
                      IClientSecretWriteRepository>    
    {
        Task<RespPadrao> ConsultarPorId(int idClientSecret);
        Task DesativarClientSecretExpirada();
        Task<RespPadrao> ConsultarGridClientSecret(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);
        Task<RespPadrao> SaveClientSecret(ClientSecretRequest lModel);    
        Task<RespPadrao> AlterarStatus(ClientSecretAlterarStatusRequest lModel);    
    }
}