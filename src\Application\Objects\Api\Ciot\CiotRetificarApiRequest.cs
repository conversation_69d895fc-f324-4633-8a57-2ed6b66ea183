using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Api.Ciot
{
    public class CiotRetificarApiRequest
    {
        public string Numero { get; set; }
        public string Verificador { get; set; }
        public List<CiotRetificarVeiculoApiRequest> Veiculos { get; set; }
        public List<CiotRetificarViagemApiRequest> Viagens { get; set; }
    }

    public class CiotRetificarVeiculoApiRequest
    {        
        public string Placa { get; set; }
        public string Renavam { get; set; }        
    }
    
    public class CiotRetificarViagemApiRequest
    {
        public int IbgeCidadeOrigem { get; set; }
        public int IbgeCidadeDestino { get; set; }
        public string CpfCnpjClienteRemetente { get; set; }
        public string CpfCnpjClienteConsignatario { get; set; }
        public string CpfCnpjClienteDestinatario { get; set; }        
        public int CodigoNaturezaCarga { get; set; }
        public decimal? PesoCarga { get; set; }
        public decimal? ValorFrete { get; set; }
        public decimal? ValorImposto { get; set; }
        public decimal? ValorDespesas { get; set; }
        public decimal? ValorCombustivel { get; set; }
        public decimal? ValorPedagio { get; set; }
    }        
}