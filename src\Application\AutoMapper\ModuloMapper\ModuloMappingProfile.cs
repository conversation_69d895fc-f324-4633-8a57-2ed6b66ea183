﻿using SistemaInfo.BBC.Application.Objects.Api.Modulo;
using SistemaInfo.BBC.Application.Objects.Api.ModuloMenu;
using SistemaInfo.BBC.Application.Objects.Web.Modulo;
using SistemaInfo.BBC.Domain.Models.Modulo;
using SistemaInfo.BBC.Domain.Models.Modulo.Commands;
using SistemaInfo.BBC.Domain.Models.ModuloMenu;
using SistemaInfo.BBC.Domain.Models.ModuloMenu.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.ModuloMapper
{
    public class ModuloMappingProfile : SistemaInfoMappingProfile
    {
        public ModuloMappingProfile()
        {
            CreateMap<Modulo, ConsultarModuloResponse>()
                .ForMember(dest => dest.Descricao, opt => opt.MapFrom(src => src.Descricao))
                .ForMember(dest => dest.IdModulo, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Sequencia, opt => opt.MapFrom(src => src.Sequencia.ToDecimalSafe(0)))
                .ForMember(dest => dest.ClassIcon, opt => opt.MapFrom(src => src.Icone));
            
            CreateMap<ModuloIntegrarApiRequest, ModuloSalvarCommand>()
                .ForMember(a => a.Descricao, opts => opts.MapFrom(d => d.NomeModulo));

            CreateMap<ModuloIntegrarApiRequest, ModuloSalvarComRetornoCommand>()
                .ForMember(a => a.Descricao, opts => opts.MapFrom(d => d.NomeModulo));

            CreateMap<ModuloSalvarCommand, Modulo>();

            CreateMap<ModuloSalvarComRetornoCommand, Modulo>();
            
            CreateMap<ModuloMenuIntegrarApiRequest, ModuloMenuSalvarCommand>()
                .ForMember(a => a.MenuId, opts => opts.MapFrom(d => d.NovoMenuId))
                .ForMember(a => a.ModuloId, opts => opts.MapFrom(d => d.NovoModuloId));
            
            CreateMap<ModuloMenuIntegrarApiRequest, ModuloMenuSalvarComRetornoCommand>()
                .ForMember(a => a.MenuId, opts => opts.MapFrom(d => d.NovoMenuId))
                .ForMember(a => a.ModuloId, opts => opts.MapFrom(d => d.NovoModuloId));

            CreateMap<ModuloMenuSalvarCommand, ModuloMenu>();

            CreateMap<ModuloMenuSalvarComRetornoCommand, ModuloMenu>();
        }
    }
}