﻿using System;
using System.Collections.Generic;
using System.Globalization;
using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;
using SistemaInfo.BBC.Domain.External.Captalys.DTO.Emprestimo;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Emprestimo;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Commands;
using SistemaInfo.BBC.Domain.Models.Retencao;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.EmprestimoMapper
{
    public class EmprestimoMappingProfile : SistemaInfoMappingProfile
    {
        public EmprestimoMappingProfile()
        {
            CreateMap<Emprestimo, ConsultarEmprestimos>()
                .ForMember(d => d.DataEmprestimo, opts => opts.MapFrom(s => s.DataEmprestimo.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.Status, opts => opts.MapFrom(s => s.Status))
                .ForMember(d => d.ValorPago, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.ValorPago ?? 0)))
                .ForMember(d => d.ValorAquisicao, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.ValorAquisicao)))
                .ForMember(d => d.TaxaRetencao, opt => opt.MapFrom(s => s.TaxaRetencao.FormatMonetario()))
                .ForMember(d => d.CpfCnpjPortador, opt => opt.MapFrom(s => $"{s.CpfCnpjPortador.FormatarCpfCnpj(true)}"))
                .ForMember(d => d.Portador, opt => opt.MapFrom(src => $"{src.Portador.Nome}"));

            CreateMap<Emprestimo, EmprestimoConsultarParaRelatorio>()
                .ForMember(o => o.Id, opt => opt.MapFrom(o => o.Id.ToString()))
                .ForMember(o => o.DataEmprestimo, opt => opt.MapFrom(o => o.DataEmprestimo.Value.ToString("dd/MM/yyyy")))
                .ForMember(o => o.PortadorNome, opt => opt.MapFrom(o => o.Portador.Nome))
                .ForMember(o => o.CpfCnpjPortador, opt => opt.MapFrom(o => o.CpfCnpjPortador.ToCpfOrCnpj()))
                .ForMember(o => o.Status, opt => opt.MapFrom(o => o.Status.GetDescription()))
                .ForMember(o => o.TaxaRetencao, opt => opt.MapFrom(o => $"{o.TaxaRetencao.FormatMonetario()} %"))
                .ForMember(o => o.ValorAquisicao, opt => opt.MapFrom(o => StringHelper.FormatMoney(o.ValorAquisicao)))
                .ForMember(o => o.ValorPago, opt => opt.MapFrom(o => StringHelper.FormatMoney(o.ValorPago ?? 0)));

            CreateMap<Emprestimo, EmprestimoConsultarParaEdicao>()
                .ForMember(o => o.ValorPago, opts => opts.MapFrom(o => (o.ValorPago ?? 0).FormatMonetario()))
                .ForMember(o => o.TaxaRetencao, opts => opts.MapFrom(o => o.TaxaRetencao.FormatMonetario()))
                .ForMember(o => o.ValorAquisicao, opts => opts.MapFrom(o => o.ValorAquisicao.FormatMonetario()))
                .ForMember(o => o.Status, opts => opts.MapFrom(o => o.Status.ToInt()))
                .ForMember(o => o.PortadorNome, opts => opts.MapFrom(o => o.Portador.Nome))
                .ForPath(o => o.Retencoes, opts => opts.MapFrom(o => new List<EmprestimoRetencaoConsultarParaEdicao>()));
            
            CreateMap<Retencao, EmprestimoRetencaoConsultarParaEdicao>()
                .ForMember(o => o.Status, opts => opts.MapFrom(o => o.Status.ToInt()))
                .ForMember(o => o.StatusStr, opts => opts.MapFrom(o => o.Status.DescriptionAttr()))
                .ForMember(o => o.DataCadastro, opts => opts.MapFrom(o => o.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDate)))
                .ForMember(o => o.DataIntegracao, opts => opts.MapFrom(o => o.DataIntegracao.ToStringBr(FormatDateTimeMethod.ShortDate)))
                .ForMember(o => o.Valor, opts => opts.MapFrom(o => o.Valor.FormatMonetario()));

            CreateMap<EmprestimoRequest, EmprestimoSalvarCommand>();

            CreateMap<EmprestimoSalvarCommand, Emprestimo>();

            CreateMap<EmprestimoResp, EmprestimoRequest>()
                .ForMember(a => a.IdState, opts => opts.MapFrom(d => d.idState))
                .ForMember(a => a.Conta, opts => opts.MapFrom(d => d.conta))
                .ForMember(a => a.Status, opts => opts.MapFrom(d => d.status))
                .ForMember(a => a.Agencia, opts => opts.MapFrom(d => d.agencia))
                .ForMember(a => a.ValorPago, opts => opts.MapFrom(d => d.valorPago))
                .ForMember(a => a.TaxaRetencao, opts => opts.MapFrom(d => d.taxaRetencao))
                .ForMember(a => a.DataEmprestimo, opts => opts.MapFrom(d => d.dataCessao))
                .ForMember(a => a.ValorAquisicao, opts => opts.MapFrom(d => d.valorAquisicao))
                .ForMember(a => a.CpfCnpjPortador, opts => opts.MapFrom(d => d.cnpj));

            CreateMap<EmprestimoCadastrarRequest, EmprestimoSalvarCommand>()
                .ForMember(dest => dest.ValorPago, opts => opts.MapFrom(s => Decimal.Parse(s.ValorPago, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.ValorAquisicao, opts => opts.MapFrom(s => Decimal.Parse(s.ValorAquisicao, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.TaxaRetencao, opts => opts.MapFrom(s => Decimal.Parse(s.TaxaRetencao, NumberStyles.Any, new CultureInfo("pt-BR"))));
        }
    }
}