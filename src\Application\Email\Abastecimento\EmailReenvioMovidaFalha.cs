using System;
using System.IO;
using System.Net.Mail;
using System.Threading.Tasks;
using SistemaInfo.BBC.Domain.Components.Email;
using SistemaInfo.BBC.Domain.External.Email;

namespace SistemaInfo.BBC.Application.Email.Abastecimento
{
    public class EmailReenvioMovidaFalha
    {
        public static async Task<NotificacaoExecutorResult> EnviarEmailReenvioMovidaFalha(INotificationEmailExecutor notificationEmailExecutor, string destinatario, string reenvios, int qtdAbastecimentos)
        {
            var caminhoAplicacao = Environment.CurrentDirectory;

            using (var streamReader = new StreamReader(caminhoAplicacao + @"\Content\Email\Abastecimento\reenvio-movida-falha.html"))
            {
                var mensagem = qtdAbastecimentos == 1
                    ? "Olá! Foi identificado um registro de abastecimento com erro na integração. Segue abaixo o abastecimento pendente, para ser tratado de forma manual:"
                    : $"Olá! Foram identificados {qtdAbastecimentos} registros de abastecimentos com erro na integração. Segue abaixo os abastecimentos pendentes, para serem tratados de forma manual:";
                
                var html = await streamReader.ReadToEndAsync();
                html = html.Replace("{MENSAGEM}", mensagem);
                html = html.Replace("{REENVIOS}", reenvios);
                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");
                
                var result = await notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                {
                    To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                    Priority = MailPriority.High,
                    Subject = "Abastecimentos não integrados à Movida",
                    IsBodyHtml = true,
                    AlternateView = view
                });

                return result;
            }
        }
    }
}