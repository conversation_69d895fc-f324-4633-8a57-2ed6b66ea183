﻿using System;
using System.Collections.Generic;
using System.Globalization;
using SistemaInfo.BBC.Application.Objects.Api.Ciot;
using SistemaInfo.BBC.Application.Objects.Web.DeclaracaoCiot;
using SistemaInfo.BBC.Application.Objects.Web.Veiculo;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.CiotVeiculo;
using SistemaInfo.BBC.Domain.Models.CiotViagem;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Commands;
using SistemaInfo.BBC.Domain.Models.Filial;
using SistemaInfo.BBC.Domain.Models.OperacaoTransporteCiot.Commands;
using SistemaInfo.BBC.Domain.Models.Portador;
using SistemaInfo.Framework.Utils;
using Veiculo = SistemaInfo.BBC.Domain.Models.Veiculo.Veiculo;

namespace SistemaInfo.BBC.Application.AutoMapper.CiotMapper
{
    public class CiotMappingProfile : SistemaInfoMappingProfile
    {
        public CiotMappingProfile()
        {
            //Caruana
            CreateMap<DeclararOperacaoTransporteReq, Infra.Data.External.Caruana.CaruanaFrete.DeclararOperacaoTransporteReq>();
            
            CreateMap<ConsultarSituacaoTransportadorReq, Infra.Data.External.Caruana.CaruanaFrete.ConsultarSituacaoTransportadorReq>();
            
            CreateMap<RetificarOperacaoTransporteReq, Infra.Data.External.Caruana.CaruanaFrete.RetificarOperacaoTransporteReq>();
            
            CreateMap<CancelarOperacaoTransporteReq, Infra.Data.External.Caruana.CaruanaFrete.CancelarOperacaoTransporteReq>();
            
            CreateMap<EncerrarOperacaoTransporteReq, Infra.Data.External.Caruana.CaruanaFrete.EncerrarOperacaoTransporteReq>();
            
            CreateMap<DeclaracaoCiotSalvarCommand, DeclaracaoCiot>();
            
            CreateMap<CiotRequest, DeclaracaoCiotSalvarCommand>()
                .ForPath(a => a.veiculosList, opts => opts.MapFrom(d => d.veiculosList))
                .ForPath(a => a.viagensList, opts => opts.MapFrom(d => d.viagensList))
                .ForMember(a => a.FormaPagamento, opts => opts.MapFrom(d => d.FormaPagamento.ToEnum<FormaPagamento>()))
                .ForMember(a => a.ValorFrete, opts => opts.MapFrom(d => Decimal.Parse(d.ValorFrete, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(a => a.ValorImposto, opts => opts.MapFrom(d => Decimal.Parse(d.ValorImposto, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(a => a.ValorPedagio, opts => opts.MapFrom(d => Decimal.Parse(d.ValorPedagio, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(a => a.ValorTarifas, opts => opts.MapFrom(d => Decimal.Parse(d.ValorTarifas, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(a => a.ValorDespesas, opts => opts.MapFrom(d => Decimal.Parse(d.ValorDespesas, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(a => a.ValorCombustivel, opts => opts.MapFrom(d => Decimal.Parse(d.ValorCombustivel, NumberStyles.Any, new CultureInfo("pt-BR"))));

            CreateMap<CiotVeiculoRequest, CiotVeiculoCommand>();

            CreateMap<CiotViagemRequest, CiotViagemCommand>()
                .ForMember(dest => dest.Peso, opts => opts.MapFrom(d => Decimal.Parse(d.Peso, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.ValorFrete, opts => opts.MapFrom(d => Decimal.Parse(d.ValorFrete, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.ValorPedagio, opts => opts.MapFrom(d => Decimal.Parse(d.ValorPedagio, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.ValorDespesas, opts => opts.MapFrom(d => Decimal.Parse(d.ValorDespesas, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.ValorImposto, opts => opts.MapFrom(d => Decimal.Parse(d.ValorImposto, NumberStyles.Any, new CultureInfo("pt-BR"))))
                .ForMember(dest => dest.ValorCombustivel, opts => opts.MapFrom(d => Decimal.Parse(d.ValorCombustivel, NumberStyles.Any, new CultureInfo("pt-BR"))));

            CreateMap<CiotStatusRequest, DeclaracaoCiotAlterarStatusCommand>();

            CreateMap<DeclaracaoCiotAlterarStatusCommand, DeclaracaoCiot>();

            CreateMap<DeclaracaoCiot, ConsultarGridCiot>()
                .ForMember(d => d.Ciot, opts => opts.MapFrom(s => s.Ciot + "/" + s.Verificador))
                .ForMember(d => d.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.DataInicio, opts => opts.MapFrom(s => s.DataInicio.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.DataFim, opts => opts.MapFrom(s => s.DataFim.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.Tipo, opts => opts.MapFrom(s => s.Tipo))
                .ForMember(d => d.Status, opts => opts.MapFrom(s => s.Status))
                .ForMember(d => d.TipoEmissao, opts => opts.MapFrom(s => s.TipoEmissao))
                .ForMember(d => d.Rntrc, opts => opts.MapFrom(s => s.PortadorProp.RNTRC))
                .ForMember(dest => dest.ValorFrete, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.ValorFrete)));
            
            
            CreateMap<ConsultaGridOperacaoTransporteMessageItem, ConsultarGridOperacaoTransporteItem>()
                .ForMember(d => d.Id, opts => opts.MapFrom(s => s.Id))
                .ForMember(d => d.Protocolo, opts => opts.MapFrom(s => $"{s.Ciot}/{s.VerificadorCiot}"))
                .ForMember(d => d.Ciot, opts => opts.MapFrom(s => s.Ciot))
                .ForMember(d => d.VerificadorCiot, opts => opts.MapFrom(s => s.VerificadorCiot))
                .ForMember(d => d.DtInicioFrete, opts => opts.MapFrom(s => s.DtInicioFrete.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.DtTerminoFrete, opts => opts.MapFrom(s => s.DtTerminoFrete.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.DtEncerramento, opts => opts.MapFrom(s => s.DtEncerramento.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.DtEmissao, opts => opts.MapFrom(s => s.DtEmissao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(d => d.Status, opts => opts.MapFrom(s => s.Status))
                .ForMember(d => d.Contingencia, opts => opts.MapFrom(s => s.Contingencia))
                .ForMember(d => d.Encerrado, opts => opts.MapFrom(s => s.Encerrado))
                .ForMember(d => d.Contratante, opts => opts.MapFrom(s => s.Contratante.FormatarCpfCnpj(true)))
                .ForMember(d => d.Proprietario, opts => opts.MapFrom(s => s.Proprietario.FormatarCpfCnpj(true)))
                .ForMember(d => d.Motorista, opts => opts.MapFrom(s => s.Motorista.FormatarCpfCnpj(true)))
                .ForMember(d => d.CpfCnpjCliente, opts => opts.MapFrom(s => s.CpfCnpjCliente.FormatarCpfCnpj(true)))
                .ForMember(d => d.TipoCarga, opts => opts.MapFrom(s => s.TipoCarga))
                .ForMember(d => d.Rntrc, opts => opts.MapFrom(s => s.Rntrc))
                .ForMember(d => d.ValorFrete, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.ValorFrete)))
                .ForMember(d => d.Servidor, opts => opts.MapFrom(s => s.Servidor))
                .ForMember(d => d.QuantidadeTarifas, opt => opt.MapFrom(s => s.QuantidadeTarifas))
                .ForMember(d => d.ValorTarifas, opts => opts.MapFrom(s =>  s.ValorTarifas));

            CreateMap<ConsultaGridOperacaoTransporteHistoricoMessageItem, ConsultarGridOperacaoTransporteHistoricoItem>();
            

            CreateMap<ConsultarGridOperacaoTransporteRequest, OperacaoTransporteConsultarMsCommand>()
                .ForMember(d => d.DtInicial, opts => opts.MapFrom(s => s.DtInicial.ToDateTime()))
                .ForMember(d => d.DtFinal, opts => opts.MapFrom(s => s.DtFinal.ToDateTime().AddDays(1).AddSeconds(-1)));
            
            CreateMap<ConsultarGridOperacaoTransporteHistoricoRequest, OperacaoTransporteConsultarHistoricoMsCommand>();

            CreateMap<DeclaracaoCiot, ConsultarPorIdCiotResponse>()
                .ForMember(d => d.NomeProprietario, opts => opts.MapFrom(s => s.PortadorProp.Nome))
                .ForMember(d => d.Banco, opts => opts.MapFrom(s => s.Banco))
                .ForMember(d => d.NomeMotorista, opts => opts.MapFrom(s => s.PortadorMot.Nome))
                .ForMember(d => d.CpfCnpjProp, opts => opts.MapFrom(s => s.PortadorProp.CpfCnpj.FormatarCpfCnpj(true)))
                .ForMember(d => d.CpfCnpjMot, opts => opts.MapFrom(s => s.PortadorMot.CpfCnpj.FormatarCpfCnpj(true)))
                .ForMember(d => d.FormaPagamento, opts => opts.MapFrom(s => s.FormaPagamento.GetHashCode()))
                .ForMember(d => d.ValorTarifas, opts => opts.MapFrom(s => (s.ValorTarifas ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorFrete, opts => opts.MapFrom(s => s.ValorFrete.FormatMonetario()))
                .ForMember(d => d.ValorCombustivel, opts => opts.MapFrom(s => (s.ValorCombustivel ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorDespesas, opts => opts.MapFrom(s => (s.ValorDespesas ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorImposto, opts => opts.MapFrom(s => s.ValorImposto.FormatMonetario()))
                .ForMember(d => d.ValorPedagio, opts => opts.MapFrom(s => (s.ValorPedagio ?? 0).FormatMonetario()))
                .ForMember(d => d.DataFim, opts => opts.MapFrom(s => s.DataFim.ToString("yyyy-MM-dd")))
                .ForPath(d => d.veiculosList, opts => opts.MapFrom(s => new List<CiotVeiculoResponse>()))
                .ForPath(d => d.viagensList, opts => opts.MapFrom(s => new List<CiotViagemResponse>()));

            CreateMap<Veiculo, CiotVeiculoResponse>()
                .ForMember(d => d.Placa, opts => opts.MapFrom(s => s.Placa.ToPlacaFormato()));

            CreateMap<CiotViagem, CiotViagemResponse>()
                .ForMember(d => d.CodigoNaturezaCarga, opts => opts.MapFrom(s => s.NaturezaCarga.Codigo))
                .ForMember(d => d.NomeConsignatario, opts => opts.MapFrom(s => s.ClienteConsignatario.NomeFantasia))
                .ForMember(d => d.NomeDestinatario, opts => opts.MapFrom(s => s.ClienteDestinatario.NomeFantasia))
                .ForMember(d => d.NomeRemetente, opts => opts.MapFrom(s => s.ClienteRemetente.NomeFantasia))
                .ForMember(d => d.NomeDestino, opts => opts.MapFrom(s => s.CidadeDestino.Nome))
                .ForMember(d => d.NomeOrigem, opts => opts.MapFrom(s => s.CidadeOrigem.Nome))
                .ForMember(d => d.Peso, opts => opts.MapFrom(s => (s.Peso ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorFrete, opts => opts.MapFrom(s => (s.ValorFrete ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorCombustivel, opts => opts.MapFrom(s => (s.ValorCombustivel ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorDespesas, opts => opts.MapFrom(s => (s.ValorDespesas ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorImposto, opts => opts.MapFrom(s => (s.ValorImposto ?? 0).FormatMonetario()))
                .ForMember(d => d.ValorPedagio, opts => opts.MapFrom(s => (s.ValorPedagio ?? 0).FormatMonetario()));
            
            CreateMap<Veiculo, VeiculoResponse>();

            CreateMap<Filial, CiotConsultarApiResponseFilial>()
                .ForMember(dest => dest.Codigo, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NomeFantasia));

            CreateMap<Portador, CiotConsultarApiResponsePortador>()
                .ForMember(dest => dest.Codigo, opt => opt.MapFrom(src => src.Id));

            CreateMap<CiotViagem, CiotConsultarApiResponseViagem>()
                .ForMember(dest => dest.CidadeOrigem, opt => opt.MapFrom(src => src.CidadeOrigem))
                .ForMember(dest => dest.CidadeDestino, opt => opt.MapFrom(src => src.CidadeDestino))
                .ForMember(dest => dest.PesoCarga, opt => opt.MapFrom(src => src.Peso));

            CreateMap<CiotVeiculo, CiotConsultarApiResponseVeiculo>()
                .ForMember(dest => dest.Codigo, opt => opt.MapFrom(src => src.Veiculo.Id))
                .ForMember(dest => dest.Placa, opt => opt.MapFrom(src => src.Veiculo.Placa))
                .ForMember(dest => dest.Renavam, opt => opt.MapFrom(src => src.Veiculo.Renavam));

            CreateMap<DeclaracaoCiot, CiotConsultarApiResponseItem>()
                .ForMember(dest => dest.PortadorProprietario, opt => opt.MapFrom(src => src.PortadorProp))
                .ForMember(dest => dest.PortadorMotorista, opt => opt.MapFrom(src => src.PortadorMot))
                .ForMember(dest => dest.Viagens, opt => opt.MapFrom(src => src.CiotViagem))
                .ForMember(dest => dest.Veiculos, opt => opt.MapFrom(src => src.CiotVeiculo))
                .ForMember(dest => dest.DataFinalContrato, opt => opt.MapFrom(src => src.DataFim));

            CreateMap<ConsultarPorIdCiotResponse, CiotRequest>();
            
            //CiotViagem
            CreateMap<DeclaracaoCiot, CiotRequest>()
                .ForMember(dest => dest.ValorTarifas, opt => opt.MapFrom(src => src.ValorTarifas.ToString()))
                .ForMember(dest => dest.ValorFrete, opt => opt.MapFrom(src => src.ValorFrete.ToString()))
                .ForMember(dest => dest.ValorCombustivel, opt => opt.MapFrom(src => src.ValorCombustivel.ToString()))
                .ForMember(dest => dest.ValorDespesas, opt => opt.MapFrom(src => src.ValorDespesas.ToString()))
                .ForMember(dest => dest.ValorImposto, opt => opt.MapFrom(src => src.ValorImposto.ToString()))
                .ForMember(dest => dest.ValorPedagio, opt => opt.MapFrom(src => src.ValorPedagio.ToString()));

            CreateMap<CiotVeiculo, CiotVeiculoRequest>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.VeiculoId))
                .ForMember(dest => dest.Placa, opt => opt.MapFrom(src => src.Veiculo.Placa))
                .ForMember(dest => dest.Renavam, opt => opt.MapFrom(src => src.Veiculo.Renavam));

            CreateMap<CiotViagem, CiotViagemRequest>();
        }
    }
}