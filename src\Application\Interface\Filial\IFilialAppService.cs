using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Filial;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Filial;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Filial.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Filial
{
    public interface IFilialAppService : IAppService<IFilialReadRepository, IFilialWriteRepository>
    {
        Task<RespPadrao> CadastrarFilialParaEmpresaAsync(FilialParaEmpresaRequest request);
        ConsultarGridFilialResponse ConsultarGridFilial(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> Save(FilialRequest lFilialReq);
        FilialResponse ConsultarPorId(int idFilial);
        Task AlterarStatus(FilialStatusRequest lFilialStatus);
        Task<FilialConsultarApiResponse> Consultar(FilialConsultarApiRequest request);
        Task<RespPadraoApi> Integrar(FilialIntegrarApiRequest request);
        Task<RespPadrao> ExcluirCentroCusto(List<Domain.Models.CentroCusto.CentroCusto> lCentroCusto);

    }
}