using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.TipoEmpresa;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.TipoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.TipoEmpresa
{
    public interface ITipoEmpresaAppService : IAppService<Domain.Models.TipoEmpresa.TipoEmpresa, ITipoEmpresaReadRepository, ITipoEmpresaWriteRepository>
    {
        ConsultarGridTipoEmpresaResponse ConsultarGridTipoEmpresa(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool isModal);
        TipoEmpresaResponse ConsultarPorId(int TipoEmpresaID);
        Task<RespPadrao> Save(TipoEmpresaRequest lTipoEmpresaReq, bool integracao = false);
        Task<bool> AlterarStatus(TipoEmpresaStatusRequest lTipoEmpresaStatus);
    }
}