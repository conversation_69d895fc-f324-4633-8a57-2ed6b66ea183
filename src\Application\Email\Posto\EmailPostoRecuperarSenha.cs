﻿using System;
using System.IO;
using System.Net.Mail;
using SistemaInfo.BBC.Domain.Components.Email;

namespace SistemaInfo.BBC.Application.Email.Posto
{
    public class EmailPostoRecuperarSenha
    {
        public static void EnviarEmail(INotificationEmailExecutor notificationEmailExecutor, string nomeUsuario, string novaSenha, string destinatario)
        {
            var caminhoAplicacao = AppDomain.CurrentDomain.BaseDirectory;

            var remetente = "<EMAIL>";
            using (var streamReader = new StreamReader(caminhoAplicacao + @"\Content\Email\Usuario\recuperar-senha-usuario.html"))
            {
                var html = streamReader.ReadToEnd();
                html = html
                    .Replace("{EMAIL_TITLE}","RECUPERAÇÃO DE SENHA" )
                    .Replace("{CORPO}", $"class='corpo'")
                    .Replace("{EMAIL_BODY}", $"&nbsp;&nbsp;&nbsp; <PERSON><PERSON><PERSON>, {nomeUsuario}! Sua senha foi redefinida automaticamente. " +
                          "Para sua segurança, altere-a novamente ao realizar login no sistema.")
                    .Replace("{STYLE_USUARIO}", $"style='display:none'")
                    .Replace("{NOVO_USUARIO}", "")
                    .Replace("{NOVA_SENHA}", novaSenha);
                
                var view = AlternateView.CreateAlternateViewFromString(html, null, "text/html");

                notificationEmailExecutor.ExecuteAsync(new Domain.Components.Email.Email
                {
                    To = new[] {new Domain.Components.Email.Email.EmailAddress {Address = destinatario}},
                    Priority = MailPriority.High,
                    Subject = "RECUPERAÇÃO DE SENHA",
                    IsBodyHtml = true,
                    AlternateView = view,
                    From = new Domain.Components.Email.Email.EmailAddress {Address = remetente}
                });
            }
        }
    }
}