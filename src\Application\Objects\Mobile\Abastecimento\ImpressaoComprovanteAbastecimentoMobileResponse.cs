﻿namespace SistemaInfo.BBC.Application.Objects.Mobile.Abastecimento
{
    public class ImpressaoComprovanteAbastecimentoMobileResponse
    {   
        public int Id { get; set; }
        public string DataAbastecimento { get; set; }
        public int CombustivelId { get; set; }
        public int PortadorId { get; set; }
        public int VeiculoId { get; set; }
        public int PostoId { get; set; }
        public decimal Litragem { get; set; }
        public int OdometroAnterior { get; set; }
        public int OdometroAtual { get; set; }
        public decimal ValorUnitario { get; set; }
        public decimal ValorAbastecimento { get; set; }
        public int Status { get; set; }

        public PostoApiResponse PostoApi { get; set; }
        public CombustivelApiResponse CombustivelApi { get; set; }
        public VeiculoApiResponse VeiculoApi { get; set; }
        public PortadorApiResponse PortadorApi { get; set; }
    }

    public class PostoApiResponse
    {
        public string RazaoSocial { get; set; }
        public string NomeFantasia { get; set; }
        public string Cnpj { get; set; }
        public string InscricaoEstadual { get; set; }
        public string EmailPosto { get; set; }
        public string Bandeira { get; set; }
        public string Endereco { get; set; }
        public int? EnderecoNumero { get; set; }
        public string Bairro { get; set; }
        public string Cep { get; set; }
        public string CidadeNome { get; set; }
        public string CidadeUf { get; set; }
    }

    public class CombustivelApiResponse
    {
        public string Nome { get; set; }
        public string UnidadeMedida { get; set; }
        public string CodigoExterno { get; set; }
    }

    public class VeiculoApiResponse
    {
        public string Placa { get; set; }
        public int Ano { get; set; }
        public string Renavam { get; set; }
        public string Chassi { get; set; }     
        public string Cor { get; set; }
        public string NumeroFrota { get; set; }
    }

    public class PortadorApiResponse
    {
        public string CpfCnpj { get; set; }
        public string Nome { get; set; }
        public string Celular { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public int? Sexo { get; set; }
        public string RNTRC { get; set; }
    }

}