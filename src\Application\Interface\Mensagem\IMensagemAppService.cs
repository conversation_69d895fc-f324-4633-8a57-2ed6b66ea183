﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Mensagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Mensagem;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Mensagem.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Mensagem
{
    public interface IMensagemAppService : IAppService<Domain.Models.Mensagem.Mensagem, IMensagemReadRepository, IMensagemWriteRepository>
    {
        ConsultarGridMensagemResponse ConsultarGridMensagem(ConsultarGridMensagemRequest request);
        MensagemResponse ConsultarPorId(int idMensagem);
        Task<RespPadrao> Save(MensagemRequest lMensagemReq, bool integracao = false);
        Task<RespRegistroMensagem> RegistraMensagem(MensagemRequest request);
        Task<RespPadrao> AlterarStatus(MensagemStatusRequest request);
    }
}