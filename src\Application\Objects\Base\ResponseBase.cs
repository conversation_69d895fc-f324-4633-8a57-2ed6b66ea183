using System;
using Microsoft.AspNetCore.Mvc;
using NLog;
using SistemaInfo.BBC.Application.Objects.Api.Pedagio;
using SistemaInfo.BBC.Domain.Contracts.Pedagio;

namespace SistemaInfo.BBC.Application.Objects.Base
{
    public static class ResponseBase
    {
        public static Logger _logger = LogManager.GetCurrentClassLogger();

        public static JsonResult Responder(bool sucesso, string msg, object data, string error = null)
        {
            return BigJson(new
            {
                message = msg?.Length > 250 ? msg.Substring(0, 250) : msg ?? "",
                success = sucesso,
                data,
                error
            });
        }

        public static JsonResult ResponderSucesso(object data)
        {
            return BigJson(new
            {
                message = "Operação realizada com sucesso!",
                success = true,
                data = data
            });
        }

        public static JsonResult JsonGrid(object data, decimal qtdItens)
        {
            return ResponderSucesso(new
            {
                totalItems = qtdItens,
                items = data
            });
        }

        public static JsonResult ResponderErro(string message)
        {
            return Responder(false, message?.Length > 250 ? message.Substring(0, 250) : message, null);
        }
        public static JsonResult Responder(RespPadrao resp)
        {
            return Responder(resp.sucesso, resp.mensagem, resp.data);
        }
        public static JsonResult Responder(IntegrarPagamentoPedagioResponse resp)
        {
            return Responder(resp.sucesso, resp.mensagem, resp.data);
        }
        public static JsonResult Responder(ComplementarPagamentoPedagioResponse resp)
        {
            return Responder(resp.sucesso, resp.mensagem, resp.data);
        }
        public static JsonResult Responder(CancelarComplementoPagamentoPedagioResponse resp)
        {
            return Responder(resp.sucesso, resp.mensagem, resp.data);
        }
        public static JsonResult Responder(CancelarPagamentoPedagioResponse resp)
        {
            return Responder(resp.sucesso, resp.mensagem, resp.data);
        }

        public static JsonResult ResponderErro(Exception ex)
        {
            _logger.Error(ex);

            var msg = ex.Message.Length > 250 ? ex.Message.Substring(0, 250) : ex.Message;

            if (!string.IsNullOrWhiteSpace(ex.InnerException?.Message))
                msg += " / " + ex.InnerException;

            return Responder(false, msg, null);
        }

        public static JsonResult BigJson(object data)
        {
            return new JsonResult(data);
        }
    }
}