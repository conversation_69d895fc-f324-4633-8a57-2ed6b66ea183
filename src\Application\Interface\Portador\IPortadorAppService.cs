using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Interface.Base;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Portador;
using SistemaInfo.BBC.Application.Objects.Web.PortadorCentroCusto;
using SistemaInfo.BBC.Application.Objects.Web.Transportador;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Portador.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Portador
{
    public interface IPortadorAppService : IAppService<Domain.Models.Portador.Portador, 
        IPortadorReadRepository, 
        IPortadorWriteRepository>,
        IBaseGetAppService<ConsultarPorIdPortadorResponse>
    {
        ConsultarGridPortadorResponse ConsultarGridPortador(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarGridPortadorResponse ConsultarGridPortadorCombo(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridPortadorResponse> ConsultarGridPortadorEmpresaCombo(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridPortadorResponse> ConsultarGridPortadorPessoaFisica(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> Save(PortadorRequest lmodel);
        Task<RespPadrao> AlterarStatus(PortadorStatusRequest lPortadorStatus);
        Task<RespPadrao> AdicionarCartao(PortadorRequest lPortador);
        Domain.Models.Portador.Portador ConsultarPorCpfCnpj(String cpfCnpj);
        Task<RespPadraoApi> IntegrarPortador(IntegrarPortadorRequest lmodel);
        Task<RespPadrao> SalvarPortadorEmpresa(PortadorEmpresaRequest lPortadorEmpReq);
        Task<RespPadrao> SalvarPortadorCentroCusto(List<PortadorCentroCustoRequest> lPortadorCentroCustoReq, int portadorId);
        Task<VerificaPortadorCadastradoResponse> VerificaPortadorCadastradoEmOutraEmp(string cpfCnpj);
        Task<RespPadrao> RecuperarSenha(PortadorRecuperarSenhaRequest model);
        RespPadrao ConsultarMobilePortadorCpfCnpj(string cpfCnpj);
        Task<RespPadrao> CriarNovaSenha(CriarNovaSenhaRequest request);
        Task<RespPadrao> ValidarPortadorLogin(string cpfCnpj, string tokenSenha);
        Task<RespPadrao> AtualizarUltimoAcesso(int id);
        
        ConsultarGridTransportadorResponse ConsultarGridTransportador(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<RespPadrao> SaveTransportador(TransportadorRequest lmodel);
        TransportadorResponse ConsultarTransportadorPorId(int id);
        TransportadorResponse ConsultarTransportadorPorCpfCnpj(string cpfCnpj);
        Task<RespPadrao> CadastrarPortadorAutomatico(CadastroPortadorPainelCiotRequest request);
    }
}