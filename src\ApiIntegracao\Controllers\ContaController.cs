using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.External.Conductor.Interface;
using SistemaInfo.BBC.Application.Interface.ContaConductor;
using SistemaInfo.BBC.Application.Interface.Pix;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// class conposta de todos os metodos utilizados pela aplicação de conta
    /// </summary>
    [Route("Conta")]
    public class ContaController : ApiControllerBase
    {
        private readonly ICartaoAppService _cartaoAppService;
        private readonly IPixAppService _pixAppService;
        private readonly IContaConductorAppService _contaConductorAppService;
        
        /// <summary>
        /// Injeção de dependeicas e heranças
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="cartaoAppService"></param>
        /// <param name="portadorAppService"></param>
        /// <param name="pixAppService"></param>
        public ContaController(IAppEngine engine, ICartaoAppService cartaoAppService, IPortadorAppService portadorAppService, IContaConductorAppService contaConductorAppService,  IPixAppService pixAppService) : base(engine)
        {
            _cartaoAppService = cartaoAppService;
            _pixAppService = pixAppService;
            _contaConductorAppService = contaConductorAppService;
        }

        /// <summary>
        /// Consulta de agencia vinculada a conta
        /// </summary>
        /// <param name="cnpj"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("ConsultarContaAgenciaPosto")]
        public JsonResult ConsultarContaAgenciaPosto(string cnpj)
        {
            try
            {
                var retornoConta = _cartaoAppService.ConsultarContaAgenciaPosto(cnpj).Result;
    
                return !retornoConta.Sucesso ? ResponseBase.ResponderErro(retornoConta.message) : ResponseBase.ResponderSucesso(retornoConta.contas[0]);
            }
            catch (Exception e)
            {
                return ResponseBase.ResponderErro("Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }

        [HttpGet("VerificarContaBBC")]
        public async Task<JsonResult> VerificarContaBbc(string documento) =>
            ResponseBase.Responder(await _contaConductorAppService.VerificarContaBbc(documento));
        
        /// <summary>
        /// Consulta de agencia vinculada a conta
        /// </summary>
        /// <param name="documento">CPF ou CNPJ da conta a ser consultada</param>
        /// <param name="chave">Chave Pix a ser valdiada</param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpGet("VerificarChavePix")]
        public async Task<JsonResult> ValidarChave(string documento, string chave) =>
            ResponseBase.Responder(await _pixAppService.ConsultarChave(documento, chave));
    }
}