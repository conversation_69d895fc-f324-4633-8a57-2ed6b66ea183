using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.AutorizacaoAbastecimento
{
    public class ConsultarGridAutorizacaoAbastecimento
    {
        public int id { get; set; }
        public string placa { get; set; }
        public string litragemAbastecida { get; set; }
        public string combustivelNome { get; set; }
        public string litragemDisponivel { get; set; }
        /// <summary>
        /// 0 = Nenhum (Usa o tipo de abastecimento do veículo), 1 = Orçamento, 2 = Autorização, 3 = Extra
        /// </summary>
        public int metodo { get; set; }
        public int status { get; set; }
        public string dataCadastro { get; set; }
        public string usuarioCadastro { get; set; }
    }
    
    public class ConsultarGridAutorizacaoAbastecimentoResponse
    {
        public int totalItems { get; set; }
        public List<ConsultarGridAutorizacaoAbastecimento> items{ get; set; }
    }
}