using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.Abastecimento
{
    public class ConsultarGridLote
    {
        public int Id { get; set; }
        public string ValorAbastecimento { get; set; }
        public string ValorTaxaEmpresa { get; set; }
        public string ValorPagamento { get; set; }
        public string ValorMdr { get; set; }
        public string ValorAcrecimoTaxa { get; set; }
        public int? PagamentoAbastecimentoId { get; set; }
        public string ProtocoloAbastecimentoId { get; set; }
        public int? LotePagamentoId { get; set; }
        public string Combustivel { get; set; }
        public string DataCadastro { get; set; }
        public string NotaXml { get; set; }
    }

    
    public class ConsultarGridLoteAbastecimentoResponse
    {
        public int totalItems { get; set; }
        public List<ConsultarGridLote> items{ get; set; }
    }
    
    
    public class ConsultarGridGridAbastecimentoPainelFinanceiro
    {
        public int Id { get; set; }
        public string ValorAbastecimento { get; set; }
        public string ValorTaxaEmpresa { get; set; }
        public string ValorPagamento { get; set; }
        public string ValorMdr { get; set; }
        public string ValorAcrecimoTaxa { get; set; }
        public string Combustivel { get; set; }
        public int? PagamentoAbastecimentoId { get; set; }
        public int? ProtocoloAbastecimentoId { get; set; }
        public string NotaFiscal { get; set; }
        public decimal? Litragem { get; set; }
        public string DataCadastro { get; set; }
        public string Placa { get; set; }
    }

    
    public class ConsultarGridAbastecimentoPainelFinanceiroResponse
    {
        public int totalItems { get; set; }
        public List<ConsultarGridGridAbastecimentoPainelFinanceiro> items{ get; set; }
    }
}