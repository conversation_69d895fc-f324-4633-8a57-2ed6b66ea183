﻿using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Api.Viagem;

public class PagamentoDiaGraficoAngular
{
    public PagamentoDiaGraficoAngular(List<PagamentoDiaGrafico> dados)
    {
        foreach (var dia in dados)
        {
            Labels.Add(dia.Dia);
            ValorSemanaAtual.Add(dia.ValorSemanaAtual??0m);
            ValorSemanaAnterior.Add(dia.ValorSemanaAnterior);
            ValorMesmaHoraSemanaAnterior.Add(dia.ValorMesmaHoraSemanaAnterior);
        }
    }

    public PagamentoDiaGraficoAngular()
    {
    }

    public List<string> Labels { get; set; } = new List<string>();
    public List<decimal?> ValorSemanaAtual { get; set; } = new List<decimal?>();
    public List<decimal> ValorSemanaAnterior { get; set; } = new List<decimal>();
    public List<decimal?> ValorMesmaHoraSemanaAnterior { get; set; } = new List<decimal?>();
}