﻿using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivel;
using SistemaInfo.BBC.Application.Objects.Web.PostoCombustivel.Request;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.PostoCombustivel.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.PostoCombustivel
{
    public interface IPostoCombustivelAppService : IAppService<Domain.Models.PostoCombustivel.PostoCombustivel, IPostoCombustivelReadRepository,
        IPostoCombustivelWriteRepository>
    {
        ConsultarGridPostoCombustivelResponse ConsultarGridPostoCombustivel(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);
        PostoCombustivelResponse ConsultarPorId(int idPostoCombustivel);
        Task<RespPadrao> Save(PostoCombustivelRequest request);
    }
}