using System.Collections.Generic;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Veiculo
{
    public class VeiculoIntegrarApiRequest
    {
        public string Placa { get; set; }
        public string Renavam { get; set; }
        public string ProprietarioCpfCnpj { get; set; }
        public string Frota { get; set; }
        public string CentroCusto { get; set; }
        public int Ano { get; set; }
        public int? Odometro { get; set; }
        public int? QuantidadeEixos { get; set; }
        public bool ControlaAutonomia { get; set; }
        public TipoAbastecimento TipoAbastecimento { get; set; }

        public List<VeiculoCombustivelApiRequest> VeiculoCombustiveis { get; set; }
    }
    public class VeiculoCombustivelApiRequest
    {
        public string Combustivel { get; set; }
        public decimal Capacidade { get; set; }
    }
}