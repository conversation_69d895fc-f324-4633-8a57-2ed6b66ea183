using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using SistemaInfo.BBC.ApiIntegracao.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.AuthSessionApi;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.Empresa.Repository;

namespace SistemaInfo.BBC.ApiIntegracao.Controllers
{
    /// <summary>
    /// Class de authsession dispoem de todos os metodos responsaveis pela auth do sistema
    /// </summary>
    [Route("AuthSession")]
    public class AuthSessionController : ApiControllerBase<IAuthSessionApiAppService>
    {
        /// <summary>
        /// Injeção de interface
        /// </summary>
        public IEmpresaReadRepository EmpresaReadRepository { get; }
        private readonly IConfiguration _config;

        /// <summary>
        /// Injeção de dependencias
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="config"></param>
        /// <param name="empresaReadRepository"></param>
        /// <param name="appService"></param>
        public AuthSessionController(IAppEngine engine, IAuthSessionApiAppService appService, IConfiguration config,IEmpresaReadRepository empresaReadRepository) : base(engine, appService)
        {
            EmpresaReadRepository = empresaReadRepository;
            _config = config;
        }
        
        /// <summary>
        /// Metodo responsavel por gerar o token de acesso
        /// </summary>
        /// <param name="tokenRequest"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [Produces("application/json")]
        [HttpPost("GerarToken")]
        public async Task<JsonResult> GerarToken([FromBody] TokenRequestIntegracao tokenRequest)
        {
            try
            {
                var response =  await AppService.GerarToken(tokenRequest);
                return response.sucesso ? ResponseBaseApi.ResponderSucesso(response.data) : ResponseBaseApi.ResponderErro(response.mensagem);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível efetuar a autenticação. Erro interno.");
            }         
        }
        
        /// <summary>
        /// Metodo responsavel por gerar o token de acesso
        /// </summary>
        /// <param name="tokenRequest"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [Produces("application/json")]
        [HttpPost("Login")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<JsonResult> Login([FromBody] TokenRequestIntegracao tokenRequest)
        {
            try
            {
                var response =  await AppService.Login(tokenRequest);
                return response.sucesso ? ResponseBaseApi.ResponderSucesso(response.data) : ResponseBaseApi.ResponderErro(response.mensagem);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível efetuar a autenticação. Erro interno.");
            }         
        }
    }
}