﻿using SistemaInfo.BBC.Application.Objects.Api.Pix;
using SistemaInfo.BBC.Application.Objects.Api.Pix.Transferencia;
using SistemaInfo.BBC.Domain.Models.Pix.Requests;
using SistemaInfo.BBC.Domain.Models.Pix.Responses.Transferencia;

namespace SistemaInfo.BBC.Application.AutoMapper.PixMapper
{
    public class PixMappingProfile : SistemaInfoMappingProfile
    {
        public PixMappingProfile()
        {
            CreateMap<ValidarChavePixBaaSResponse, CriarTransferenciaPixRequest>();
            
            CreateMap<ValidarChavePixBaaSResponse, InformacoesBeneficiarioPixRequest>()
                .ForMember(dest => dest.PayeeName, opt => opt.MapFrom(src => src.Name));
            
            CreateMap<ValidarChavePixBaaSResponse, InformacoesBeneficiarioPixBaaSRequest>()
                .ForMember(dest => dest.payeeName, opt => opt.MapFrom(src => src.Name));
        }
    }
}