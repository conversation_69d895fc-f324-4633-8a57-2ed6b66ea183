﻿using System;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.PagamentoAbastecimento
{
    public class AbastecimentoTransacaoIntegrarApiRequest
    {
        public int Id { get; set; }
        public int AbastecimentoId { get; set; }
        public int? LotePagamentoId { get; set; }
        public int? LoteRetencaoId { get; set; }
        public decimal TaxaAbastecimento { get; set; }
        public decimal DescontoMDR { get; set; }
        public decimal ValorAbastecimento { get; set; }
        public decimal ValorAbastecimentoDesconto { get; set; }
        public decimal ValorAbastecimentoAcrescimoTaxa { get; set; }
        public int ContaOrigem { get; set; }
        public int ContaDestino { get; set; }
        public string Observacao { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public DateTime? DataBaixa { get; set; }
        public DateTime? DataCancelamento { get; set; }
        public DateTime? DataPrevisaoPagamento { get; set; }
        public int UsuarioCadastroId { get; set; }
        public int UsuarioAlteracaoId { get; set; }
        public int UsuarioCancelamentoId { get; set; }
        public TipoOperacaoPagamentoAbastecimento TipoOperacao { get; set; }
        public StatusPagamentoAbastecimento Status { get; set; }
        public int ContadorTentativas { get; set; } = 0;
        
        public decimal? ImpostoIRRFTransacao { get; set; } = 0;
        public decimal? ImpostoCSLLTransacao { get; set; } = 0;
        public decimal? ImpostoCOFINSTransacao { get; set; } = 0;
        public decimal? ImpostoPISTransacao { get; set; } = 0;
        public decimal? CashbackTransacao { get; set; } = 0;

    }
    
    public class LoteTransacaoIntegrarApiRequest
    {
        public int Id { get; set; }
        public int? LotePagamentoId { get; set; }
        public int LoteReceitaId { get; set; }
        public decimal TaxaAbastecimento { get; set; }
        public decimal DescontoMDR { get; set; }
        public decimal ValorAbastecimento { get; set; }
        public decimal ValorAbastecimentoDesconto { get; set; }
        public decimal ValorAbastecimentoAcrescimoTaxa { get; set; }
        public int ContaOrigem { get; set; }
        public int ContaDestino { get; set; }
        public string Observacao { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public DateTime? DataBaixa { get; set; }
        public DateTime? DataCancelamento { get; set; }
        public DateTime? DataPrevisaoPagamento { get; set; }
        public int UsuarioCadastroId { get; set; }
        public int UsuarioAlteracaoId { get; set; }
        public int UsuarioCancelamentoId { get; set; }
        public TipoOperacaoPagamentoAbastecimento TipoOperacao { get; set; }
        public StatusPagamentoAbastecimento Status { get; set; }
        public int ContadorTentativas { get; set; } = 0;
        
        public decimal? ImpostoIRRFTransacao { get; set; } = 0;
        public decimal? ImpostoCSLLTransacao { get; set; } = 0;
        public decimal? ImpostoCOFINSTransacao { get; set; } = 0;
        public decimal? ImpostoPISTransacao { get; set; } = 0;

    }
}