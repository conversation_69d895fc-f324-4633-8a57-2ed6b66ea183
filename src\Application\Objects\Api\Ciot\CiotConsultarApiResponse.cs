using System;
using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Base;

namespace SistemaInfo.BBC.Application.Objects.Api.Ciot
{
    public class CiotConsultarApiResponse : BaseConsultarApiResponse<CiotConsultarApiResponseItem>
    {
        
    }

    public class CiotConsultarApiResponseItem
    {
        public string Ciot { get; set; }
        public string Verificador { get; set; }
        public string Tipo { get; set; }        
        public int QuantidadeTarifas { get; set; }
        public decimal ValorTarifas { get; set; }
        public decimal ValorFrete { get; set; }
        public decimal? ValorCombustivel { get; set; }
        public decimal? ValorDespesas { get; set; }
        public decimal ValorImposto { get; set; }
        public decimal? ValorPedagio { get; set; }
        public DateTime DataFinalContrato { get; set; }
        public CiotConsultarApiResponsePortador PortadorProprietario { get; set; }
        public CiotConsultarApiResponsePortador PortadorMotorista { get; set; }
        public CiotConsultarApiResponseFilial Filial { get; set; }
        public List<CiotConsultarApiResponseViagem> Viagens { get; set; } 
        public List<CiotConsultarApiResponseVeiculo> Veiculos { get; set; }
    }

    public class CiotConsultarApiResponsePortador
    {
        public string Codigo { get; set; }
        public string CpfCnpj { get; set; }
        public string Nome { get; set; }        
    }

    public class CiotConsultarApiResponseFilial
    {
        public int Codigo { get; set; }
        public string Cnpj { get; set; }
        public string RazaoSocial { get; set; }    
        public string Nome { get; set; }
    }

    public class CiotConsultarApiResponseViagem
    {
        public CiotConsultarApiResponseViagemCidade CidadeOrigem { get; set; }
        public CiotConsultarApiResponseViagemCidade CidadeDestino { get; set; }
        public CiotConsultarApiResponseViagemCliente ClienteRemetente { get; set; }
        public CiotConsultarApiResponseViagemCliente ClienteConsignatario { get; set; }
        public CiotConsultarApiResponseViagemCliente ClienteDestinatario { get; set; }
        public CiotConsultarApiResponseViagemNaturezaCarga NaturezaCarga { get; set; }
        public decimal? PesoCarga { get; set; }
        public decimal? ValorFrete { get; set; }
        public decimal? ValorImposto { get; set; }
        public decimal? ValorDespesas { get; set; }
        public decimal? ValorCombustivel { get; set; }
        public decimal? ValorPedagio { get; set; }
    }

    public class CiotConsultarApiResponseViagemCliente
    {
        public string CpfCnpj { get; set; }
        public string RazaoSocial { get; set; }
    }

    public class CiotConsultarApiResponseViagemCidade
    {
        public int Ibge { get; set; }
        public string Nome { get; set; }
    }
    
    public class CiotConsultarApiResponseVeiculo
    {
        public string Codigo { get; set; }
        public string Placa { get; set; }
        public string Renavam { get; set; }        
    }

    public class CiotConsultarApiResponseViagemNaturezaCarga
    {
        public string Codigo { get; set; }
        public string Descricao { get; set; }
    }
}