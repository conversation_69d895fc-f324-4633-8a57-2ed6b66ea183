

using System;
using SistemaInfo.BBC.Domain.External.Conductor.DTO;

namespace SistemaInfo.BBC.Application.Objects.Api.Documento
{
    public class DocumentoResponse : ResponseBase
    {
        public int Id { get; set; }
        public int Tipo { get; set; }
        public string Foto { get; set; }
        public int? PortadorId { get; set; }
        public int? PostoId { get; set; }
        public int? UsuarioCadastroId { get; set; }
        public DateTime DataCadastro { get; set; }
        public bool EnviadoBBC { get; set; }
        public string Descricao { get; set; }
        public string Motivo { get; set; }
        public int? Status { get; set; }
        public int? Acao { get; set; } = 0;
        public int? DocumentosProcessoVinculadoId { get; set; }
    }
}