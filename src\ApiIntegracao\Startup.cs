﻿using AutoMapper;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SistemaInfo.BBC.Infra.CrossCutting.IoC;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Filters;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Secutiry.UserSession.Swagger;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Swagger;
using SistemaInfo.Framework.Web.Filters;
using Swashbuckle.AspNetCore.Swagger;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json.Converters;
using NLog;
using SistemaInfo.BBC.Infra.Bus;
using SistemaInfo.Framework.Utils;
using SistemaInfo.Framework.Utils.AppConfiguration;
using SistemaInfo.Framework.Web.Utils;

namespace SistemaInfo.BBC.ApiIntegracao
{
    /// <summary>
    ///
    /// </summary>
    public class Startup
    {
        /// <inheritdoc />
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        ///
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        ///
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            services
                .AddMvc(opts =>
                {
                    opts.Filters.Add<ApiActionExceptionFilter>();
                    opts.Filters.Add<ApiFaultResultCodeFilter>();
                    opts.Filters.Add<ApiIntegracao.Filters.ApiBeforeActionFilter>();
                    opts.Filters.Add(new AuthorizeFilter("AppToken"));
                    opts.OutputFormatters.Add(new XmlDataContractSerializerOutputFormatter());
                    opts.InputFormatters.Add(new XmlDataContractSerializerInputFormatter());
                })
                .AddJsonOptions(opts => opts.SerializerSettings.Converters.Add(new StringEnumConverter()));

            services.AddAuthorization(opts =>
                opts.AddPolicy("AppToken", policy =>
                    policy.Requirements.Add(new AppTokenAuthorizationRequirement())));


            services.AddAutoMapper();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Info {Title = "/BBC/ApiIntegracao", Version = "v1"});
                c.OperationFilter<FileUploadOperation>();
                c.OperationFilter<WebSessionTokenHeaderParameter>();
                //c.OperationFilter<AuditUserDocHeaderParameter>(); // AuditUserDocHeaderParameter está momentaneamente, o correto é gerar o web session token para idnetificação da sessão do usuário, e envia-lo no campo acima
                c.SchemaFilter<SwaggerExcludeFilter>();
                c.DescribeAllEnumsAsStrings();

                var basePath = AppContext.BaseDirectory;
                var lXmlPath = Path.Combine(basePath, "App_Data",
                    System.Reflection.Assembly.GetExecutingAssembly().GetName().Name + ".xml");
                c.IncludeXmlComments(lXmlPath);
            });

            var secretKey = Encoding.UTF8.GetString(Convert.FromBase64String(Configuration["Authentication:SecretKey"]));
            
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidIssuer = "MyServer",

                        ValidateAudience = true,
                        ValidAudience = "EveryApplication",

                        ValidateIssuerSigningKey = true,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
                        

                        RequireExpirationTime = true,
                        ValidateLifetime = true,
                        ClockSkew = TimeSpan.Zero
                    };
                });
            //
            RegisterServices(services, Configuration);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="httpAccessor"></param>
        /// <param name="appConfiguration"></param>
        /// <param name="serviceProvider"></param>
        public void Configure(IApplicationBuilder app, IHostingEnvironment env, IHttpContextAccessor httpAccessor, AppConfiguration appConfiguration, IServiceProvider serviceProvider)
        {
            app.UseLogForApplicationEvents();
            app.UseSistemaInfoFramework(() => httpAccessor?.HttpContext?.RequestServices ?? serviceProvider);

            app.UsePathBase("/BBC/ApiIntegracao");

            #region Swagger Autenticacao
            
            app.UseStaticFiles();
            
            app.Use(async (context, next) =>
            {
                var caminho = Configuration["Swagger:SwaggerEndpoint"];
                string caminhoBase;
                if (string.IsNullOrWhiteSpace(caminho))
                {
                    caminhoBase = string.Empty;
                }
                else
                {
                    caminhoBase = caminho.Substring(0, caminho.IndexOf("/swagger", StringComparison.Ordinal));
                }
                
                try
                {
                    if (context.Request.Path.Value.Contains("/swagger"))
                    {
                        var handler = new JwtSecurityTokenHandler();
                        var sessionToken = context.Request.Cookies["SessionKey"].ToStringSafe();
                        if (sessionToken.IsNullOrWhiteSpace())
                        {
                            context.Response.Redirect(caminhoBase + "/login/login.html");
                            return;
                        }
                        
                        var tokenS = handler.ReadToken(sessionToken) as JwtSecurityToken;

                        var expDate = tokenS.ValidTo;

                        if (expDate < DateTime.UtcNow)
                        {
                            context.Response.Redirect(caminhoBase + "/login/login.html");
                            return;
                        }
                    }
                    await next.Invoke();
                }
                catch (Exception ex)
                {
                    LogManager.GetCurrentClassLogger().Error(ex);
                    context.Response.StatusCode = 500;
                    await context.Response.WriteAsync("Erro interno do servidor");
                }
            });

            #endregion
            
            app.UseCustomStatusCodePages();
            app.UseCustomDefaultFiles();
            app.UseCustomStaticFiles();
            
            app.UseMiddleware<AuthenticationExceptionHandlingMiddleware>();
            app.UseMvc(routes =>
            {
                routes.MapRoute("ApiIntegracao", "BBC/ApiIntegracao/{controller}/{action}/{id}");
            });
            
            app.UseAuthentication();
            SetSwagger(app);
        }

        private void SetSwagger(IApplicationBuilder app)
        {
            app.UseSwagger();

            var swaggerEndpoint = "/BBC/ApiIntegracao/swagger/v1/swagger.json";

            // appsettings 
            // "Swagger": {
            //     "SwaggerEndpoint": "/BBC/ApiIntegracao/swagger/v1/swagger.json"
            // }
            if (!string.IsNullOrWhiteSpace(Configuration["Swagger:SwaggerEndpoint"]))
                swaggerEndpoint = Configuration["Swagger:SwaggerEndpoint"];

            app.UseSwaggerUI(c => { c.SwaggerEndpoint(swaggerEndpoint, "/BBC/ApiIntegracao"); });
        }

        private static void RegisterServices(IServiceCollection serviceCollection, IConfiguration configuration)
        {
            DependencyInjector.RegisterServices(serviceCollection);
            MessageBusDependencyInjector.RegisterServices(serviceCollection, configuration);
            DependencyInjectorCartaoApi.RegisterServices(serviceCollection);
        }
    }
}