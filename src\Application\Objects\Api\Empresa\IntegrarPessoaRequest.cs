﻿using System;
using System.Linq;
using FluentValidation;
using Newtonsoft.Json;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Support;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Swagger;
using SistemaInfo.Framework.Utils;
using SistemaInfo.BBC.Domain.Languages;
using SistemaInfo.BBC.Domain.Models.Cidade.Repository;

namespace SistemaInfo.BBC.Application.Objects.Api.Empresa
{
    public class IntegrarPessoaRequest : ValidatedObject<IntegrarPessoaRequest, IntegrarPessoaRequestValidator>,
        IOldRequestFormatSupport
    {
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public string NomeFantasia { get; set; }
                
        public IntegrarPessoaEnderecoRequest Endereco { get; set; }
        public IntegrarPessoaInfoRequest Info { get; set; }
        public IntegrarPessoaTipoFlagsRequest Flags { get; set; } = new IntegrarPessoaTipoFlagsRequest();

        [JsonIgnore]
        public int CidadeId { get; set; }
        
        #region Obsoletas - Propriedades com nomes antigos, renomeadas para um novo padrão, mas que ainda necessitam estar presentes por clientes em produção
        
        [Obsolete("Sexo - Obsoleta, indicar este dado na tag \"Info.Sexo\"")]
        [SwaggerExclude]
        public string Sexo { get; set; }
        
        /// <summary>
        /// Código IBGE da cidade - Obsoleta, indicar este dado na taag "Endereco"
        /// </summary>        
        [Obsolete("Código IBGE da cidade - Obsoleta, indicar este dado na tag \"Endereco.Cidade\"")]
        [SwaggerExclude]
        public int Cidade { get; set; }
        
        #endregion

        public string[] GetNomeSobreNome()
        {
            var resultado = new string[2];

            var nomeSobreNome = Nome.Split(' ');

            resultado[0] = nomeSobreNome.First();

            for (var i = 1; i < nomeSobreNome.Length; i++)
                resultado[1] += $"{nomeSobreNome[i]} ";

            if (!string.IsNullOrWhiteSpace(resultado[1]))
            {
                resultado[1] = resultado[1].Trim();
            }

            return resultado;
        }
        
        public void AjustarCompatibilidadeRequisicoesObsoletas()
        {
#pragma warning disable 618
            if (Cidade != 0)
            {
                if (Endereco == null)
                    Endereco = new IntegrarPessoaEnderecoRequest();
                if (Endereco.Cidade == 0)
                    Endereco.Cidade = Cidade;
            }

            if (Sexo.HasValue())
            {
                if (Info == null)
                    Info = new IntegrarPessoaInfoRequest();
                if (Info.Sexo.IsNullOrWhiteSpace())
                    Info.Sexo = Sexo;
            }

            Endereco?.AjustarCompatibilidadeRequisicoesObsoletas();
#pragma warning restore 618
        }              
    }
    
    #region Classes agregadas
        
    public class IntegrarPessoaTipoFlagsRequest
    {
        public bool? PontoDistribuicaoCartao { get; set; }
        public bool? PontoCargaMoedeiro { get; set; }
    }
        
    public class IntegrarPessoaInfoRequest
    {            
        public string Sexo { get; set; }
        public string RG { get; set; }
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string Telefone { get; set; }
        public string Celular { get; set; }
        public string Email { get; set; }
    }

    public class IntegrarPessoaEnderecoRequest : IOldRequestFormatSupport
    {
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string Bairro { get; set; }
        public string CEP { get; set; }
        public string Complemento { get; set; }
        
        /// <summary>
        /// Código IBGE da cidade
        /// </summary>
        public int Cidade { get; set; }
        
        /// <summary>
        /// Código IBGE da cidade - Obsoleta, indicar este dado na tag "Cidade"
        /// </summary>        
        [Obsolete("Código IBGE da cidade - Obsoleta, indicar este dado na tag \"Cidade\"")]
        [SwaggerExclude]
        public int? OldCidadeIbge { get; set; }
        
        public void AjustarCompatibilidadeRequisicoesObsoletas()
        {
#pragma warning disable 618
            if (OldCidadeIbge.HasValue && OldCidadeIbge.Value > 0 && Cidade == 0)
                Cidade = OldCidadeIbge.Value;
#pragma warning restore 618
        }
    }

    #endregion       
    
    public class IntegrarPessoaRequestValidator : ObjectValidator<IntegrarPessoaRequest>
    {
        private readonly ICidadeReadRepository _cidadeRepository;

        public IntegrarPessoaRequestValidator(IServiceProvider serviceProvider, ICidadeReadRepository cidadeRepository) : base(serviceProvider)
        {
            _cidadeRepository = cidadeRepository;
        }

        public IntegrarPessoaRequestValidator(IServiceProvider serviceProvider, IntegrarPessoaRequest instance, ICidadeReadRepository cidadeRepository) : base(serviceProvider, instance)
        {
            _cidadeRepository = cidadeRepository;
        }

        protected override void CreateRules()
        {            
            RuleForCpfCnpj();
            RuleForNome();
            RuleForEndereco();
            RuleForInfo();
        }

        private void RuleForInfo()
        {
//            if (Instance.CpfCnpj.Length <= 11 && Instance.Info != null)
//                RuleFor(p => p.Sexo)
//                    .NotNull()
//                    .NotEmpty()
//                    .WithMessage(PessoaMensagens.SexoObrigatorio);
        }

        private void RuleForCpfCnpj()
        {            
            RuleFor(p => p.CpfCnpj)
                .Must(p => p == p.OnlyNumbers())
                .WithMessage(PessoaMensagens.CpfCnpjSomenteNumeros);
        }

        private void RuleForEndereco()
        {
            RuleFor(p => p.Endereco)
                .NotNull()
                .WithMessage(PessoaMensagens.CidadeInvalida);                       
            
            if (Instance.Endereco != null)
                RuleFor(p => p.Endereco.Cidade)
                    .Must(p => _cidadeRepository.Any(c => c.Ibge == p))
                    .WithMessage(PessoaMensagens.CidadeInvalida);
        }

        private void RuleForNome()
        {
            RuleFor(p => p)
                .Must(p => !string.IsNullOrWhiteSpace(p.GetNomeSobreNome()[1]))
                .WithMessage(PessoaMensagens.NomeInválido);
        }
    }    
}