namespace SistemaInfo.BBC.Application.Objects.Api.Menu
{  
    public class ConsultaGridMenu
    {
        public int Id { get; set; }
        public string Descricao { get; set; }
        public int? MenuPaiId { get; set; }
        public int? IsMenuPai { get; set; }
        public string Link { get; set; }
        public int Sequencia { get; set; }
        public int IsMostraApenasAdmin { get; set; } = 0;
    }
    
    public class MenuIntegrarApiRequest
    {
        public bool NovoMenu { get; set; } = true;
        public string NomeMenu { get; set; }
        public int? MenuPaiId { get; set; }
        public bool IsMenuPai { get; set; } = false;
        public string Link { get; set; }
        public int Sequencia { get; set; }
    }
}