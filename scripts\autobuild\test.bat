echo off
echo %~dp0
set pastaOrigem=%~dp0
call %pastaOrigem%variaveis.bat

cd %projeto%

cmd /c dotnet test "./tests/BBC.Test/BBC.Test.csproj" -v:d --logger "trx;LogFileName=%publish_dir%/ResultadoTestesUnitarios.trx"

if %errorlevel%==1 (goto :erro) else (goto :sucesso)

:erro
echo.
echo ===================================================================================================
echo.
echo erro
echo.
echo ===================================================================================================
echo.
goto :fim

:sucesso
echo.
echo ===================================================================================================
echo.
echo sucesso
echo.
echo ===================================================================================================
echo.
goto :fim

:fim
if %NoStop%==False (pause)