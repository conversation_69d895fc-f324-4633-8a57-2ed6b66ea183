using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Fabricante;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Fabricante.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Fabricante
{
    public interface IFabricanteAppService : IAppService<Domain.Models.Fabricante.Fabricante, IFabricanteReadRepository, IFabricanteWriteRepository>
    {
        ConsultarGridFabricanteResponse ConsultarGridFabricante(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        FabricanteResponse ConsultarPorId(int idFabricante);
        Task<RespPadrao> Save(FabricanteRequest request);
        Task<RespPadrao> AlterarStatus(FabricanteStatusRequest request);
    }
}