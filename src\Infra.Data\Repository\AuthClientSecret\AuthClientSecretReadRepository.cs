using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret.Repository;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;

namespace SistemaInfo.BBC.Infra.Data.Repository.AuthClientSecret
{
    
    public abstract class AuthClientSecretBaseReadRepository <TContext, TAuthClientSecretEntity> : ReadOnlyRepository<TAuthClientSecretEntity, TContext>, IAuthClientSecretBaseReadRepository<TAuthClientSecretEntity>
        where TContext : DbContext
        where TAuthClientSecretEntity : Domain.Models.AuthClientSecret.AuthClientSecret
    {
        public AuthClientSecretBaseReadRepository(TContext context) : base(context)
        {
        }
    }
    public class AuthClientSecretReadRepository : AuthClientSecretBaseReadRepository<ConfigContext, Domain.Models.AuthClientSecret.AuthClientSecret>, IAuthClientSecretReadRepository
    {

        public AuthClientSecretReadRepository(ConfigContext context) : base(context)
        {
        }

        public async Task<bool> AnyAtivoAsync(string login)
        {
           return await AnyAtivoAsync(login);
        }

        public async Task<Domain.Models.AuthClientSecret.AuthClientSecret> ObterPorLoginAsync(string login)
        {
            return await ObterPorLoginAsync(login);
        }
    }
}
