﻿using System;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Mobile.Posto
{
    public class PortadorConsultarCpfCnpjApiResponse
    {
        public int Id { get; set; }
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public string Email { get; set; }
        public string Endereco { get; set; }
        public string CidadeUf { get; set; }
        public string CidadeNome { get; set; }
        public string Cep { get; set; }
        public int? EnderecoNumero { get; set; }
        public string Telefone { get; set; }
        public string Celular { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public string RNTRC { get; set; }
        public string NumeroCNH { get; set; }
        public string RazaoSocial { get; set; }
        public string NomePai { get; set; }
        public string NomeMae { get; set; }
        public ESexo? Sexo { get; set; }
        public string NumeroIdentidade { get; set; }
        public string OrgaoEmissor { get; set; }
        public string UfEmissao { get; set; }
        public DateTime? EmissaoIdentidade { get; set; }
        public string InscricaoEstadual { get; set; }
        public DateTime? DataAberturaEmpresa { get; set; }
        public string FormaConstituicao { get; set; }
        public string Cnae { get; set; }
        public ETipoPessoa TipoPessoa { get; set; }
        public DateTime? DataNascimento { get; set; }
        public string NaturezaJuridica{ get; set; }
        public EAtividade? Atividade { get; set; }
        public bool ControlaAbastecimentoCentroCusto { get; set; }
    }
}