using System.Collections.Generic;
using System.Threading.Tasks;
using DinkToPdf.Contracts;
using SistemaInfo.BBC.Application.Objects.Api.Emprestimo;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Emprestimo.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Emprestimo
{
    public interface IEmprestimoAppService : IAppService<Domain.Models.Emprestimo.Emprestimo, 
        IEmprestimoReadRepository, IEmprestimoWriteRepository>
    {
        ConsultarEmprestimosResponse ConsultarEmprestimos(int take, int page, string cpfCnpj);

        ConsultarEmprestimosResponse ConsultarGridEmprestimos(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);

        List<ConsultarEmprestimos> DadosRelatorioGridPagamentos(OrderFilters orderFilters, List<QueryFilters> filters);
        
        Task<RespPadrao> IntegrarEmprestimo(EmprestimoRequest lEmprestimoReq);

        Task<RespPadrao> Cadastrar(EmprestimoCadastrarRequest emprestimoCadastrarRequest);

        EmprestimoConsultarParaEdicao ConsultarParaEdicao(int id);

        Task<RespPadrao> SinalizaClienteEmprestimo(SinalizarEmprestimoRequest lEmprestimoReq);
       
        IDocument GerarRelatorioEmprestimo(int emprestimoId);
    }
}