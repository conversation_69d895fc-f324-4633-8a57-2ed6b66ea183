using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.CentralPendencias
{
    public class ConsultarGridCentralPendenciasMovidaItem
    {
        public int? Id { get; set; }
        public string PostoCnpj { get; set; }
        public int? PostoId { get; set; }
        public string PostoRazaoSocial { get; set; }
        public string Placa { get; set; }
        public int? CombustivelId { get; set; }
        public string CombustivelNome { get; set; }
        public string Litragem { get; set; }
        public string ValorAbastecimento { get; set; }
        public string EmpresaNomeFantasia { get; set; }
        public int? AutorizacaoAbastecimentoId { get; set; }
        public int? ProtocoloAbastecimentoId { get; set; }
        public string MotivoPendenciaMovida { get; set; }
        public string ValorTaxaBbc { get; set; }
        public string FuncionarioCpf { get; set; }
        public int? OdometroInformado { get; set; }
        /// <summary>
        /// 0: <PERSON>rro, 1: Executado, 2: Não concluído
        /// </summary>
        public int? IntegracaoMovida { get; set; }
        /// <summary>
        /// Define o número de tentativas de integração pelo serviço.
        /// </summary>
        public int? ContadorIntegracaoMovida { get; set; }
        public DateTime? DataIntegracaoMovida { get; set; }
        public int? Status { get; set; }
        public string DataCadastro { get; set; }
    }
    
    public class ConsultarGridCentralPendenciasMovidaResponse
    {
        public int TotalItems { get; set; }
        public List<ConsultarGridCentralPendenciasMovidaItem> Items{ get; set; }
    }
}