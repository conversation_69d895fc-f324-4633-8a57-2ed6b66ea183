using System;
using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Api.Portador
{
    public class IntegrarPortadorRequest
    {
        public string Id { get; set; }
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public string Celular { get; set; }
        public string Telefone { get; set; }
        public string Email { get; set; }
        public string Endereco { get; set; }
        public int CidadeIbgeId { get; set; }
        public string Cep { get; set; }
        public string EnderecoNumero { get; set; }
        public string Bairro { get; set; }
        public string Complemento { get; set; }
        public int TipoPessoa { get; set; }
        public string UfEstado { get; set; }
        public string NomeCidade { get; set; }

        //PF
        public string NomeMae { get; set; }
        public string NomePai { get; set; }
        public int? Sexo { get; set; }
        public string NumeroIdentidade { get; set; }
        public string OrgaoEmissor { get; set; }
        public string UfEmissao { get; set; }
        public DateTime? EmissaoIdentidade { get; set; }
        public string DataNascimento { get; set; }
        public bool? IsPep { get; set; }

        //PJ
        public string RazaoSocial { get; set; }
        public string InscricaoEstadual { get; set; }
        public DateTime? DataAberturaEmpresa { get; set; }
        public string FormaConstituicao { get; set; }
        public string Cnae { get; set; }
        public string RNTRC { get; set; }

        public bool CriarPessoaDock { get; set; } = false;

        public List<PortadorRepLegalRequestApi> RepLegaisList { get; set; }
        
        public int? EmpresaId { get; set; }
        public string NaturezaJuridica { get; set; }
    }
    
    public class PortadorRepLegalRequestApi
    {
        public int? Id { get; set; }
        public string Nome { get; set; }
        public string CpfCnpj { get; set; }
        public DateTime DataNascimento { get; set; }
        public string NomeMae { get; set; }
        public bool? IsPep { get; set; }
    
    }
}