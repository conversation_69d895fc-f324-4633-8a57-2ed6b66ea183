using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.MDRPrazos;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.MDRPrazos.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.MDRPrazos
{
    public interface IMDRPrazosAppService : IAppService<Domain.Models.MDRPrazos.MDRPrazos, IMDRPrazosReadRepository, IMDRPrazosWriteRepository>
    {
        ConsultarGridMDRPrazosResponse ConsultarGridMDRPrazos(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        List<MDRPrazosResponse> ConsultarMDRPrazosPorBancoId(string lBancoId);
        MDRPrazosResponse ConsultarPorId(int idMDRPrazos);
        Task<RespPadrao> Save(MDRPrazosRequest request);
        Task<RespPadrao> AlterarStatus(MDRPrazosStatusRequest request);
    }
}