using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.ContaConductor;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.IntegrarConta;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.ContasConductor.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using IContaConductorReadRepository = SistemaInfo.BBC.Domain.Models.ContasConductor.Repository.IContaConductorReadRepository;

namespace SistemaInfo.BBC.Application.Interface.ContaConductor
{
    public interface IContaConductorAppService : IAppService<IContaConductorReadRepository, IContaConductorWriteRepository>
    {
        Task<RespPadraoApi> Integrar(ContaConductorRequest request);
        GridContasConductorResponse DadosGridContaConductor(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters, bool ativo);
        Task<RespPadrao> ImportarContasExcel(List<ImportarContaExcelRequest> request);
        RespPadrao EnviarContas(EnviarContasRequest request);
        Task<RespPadrao> VerificarContaBbc(string documento);
    }
}