﻿using System.Collections.Generic;
using SistemaInfo.BBC.Application.Objects.Web.CFOP;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.CFOP.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.CFOP
{
    public interface ICFOPAppService : IAppService<Domain.Models.CFOP.CFOP, ICFOPReadRepository, ICFOPWriteRepository>
    {
        ConsultarGridCFOPResponse ConsultarGridCFOP(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        
    }
}