﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Application.Objects.Api.Token;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;
using SistemaInfo.Framework.DomainDrivenDesign.Web.Controllers;

namespace SistemaInfo.BBC.ApiCiot.Controllers.Base
{
    /// <inheritdoc />
    /// <summary>ApiCiot de operações da ANTT.</summary>
    [Route("Operacoes")]
    public class ApiControllerBase : SistemaController
    {
        /// <inheritdoc />
        public ApiControllerBase(IAppEngine engine) : base(engine)
        {
        }
    }

    /// <inheritdoc />
    public class ApiControllerBase<TAppService> : SistemaController<TAppService>
    {
        /// <inheritdoc />
        public ApiControllerBase(IAppEngine engine, TAppService appService) : base(engine, appService)
        {
        }
        
        /// <summary>
        /// Metodo responsavel por gerar o token de acesso
        /// </summary>
        /// <example></example>
        /// <returns></returns>
        [AllowAnonymous]
        [Produces("application/json")]
        [HttpPost("GerarToken")]
        public RespPadrao GerarToken([FromBody] TokenRequest tokenRequest)
        {
            try
            {
                return new RespPadrao(true, "");
            }
            catch (Exception e)
            {
                return new RespPadrao(false, "Não foi possível realizar a operação. Mensagem: " + e.Message);
            }
        }
    }
}