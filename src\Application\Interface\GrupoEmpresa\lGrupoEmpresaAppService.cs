using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.GrupoEmpresa;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.GrupoEmpresa.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.GrupoEmpresa
{
    public interface IGrupoEmpresaAppService : IAppService<Domain.Models.GrupoEmpresa.GrupoEmpresa, IGrupoEmpresaReadRepository,
                      IGrupoEmpresaWriteRepository>    
    {
        ConsultarGrupoEmpresaResponse ConsultarPorId(int? idGrupoEmpresa);
        Task<RespPadrao> ConsultarGrupoEmpresaUsuario();
        ConsultarGridGrupoEmpresaResponse ConsultarGridGrupoEmpresa(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters, bool ativos = false);
        Task<ConsultarGridGrupoEmpresaResponse> ConsultarModalGrupoEmpresa(int take, int page, OrderFilters orderFilters,
            List<QueryFilters> filters);
        Task<RespPadrao> SaveGrupoEmpresa(GrupoEmpresaRequest lModel);
        Task AtivarInativar(int idGrupoEmpresa);

    }
}