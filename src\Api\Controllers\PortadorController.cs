using System;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.Api.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Portador;
using SistemaInfo.BBC.Application.Objects.Api.Portador;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.Api.Controllers
{
    /// <summary>
    /// Class responsavel por conter todos os metodos utilizados pela aplicação portador
    /// </summary>
    [Route("Portador")]
    public class PortadorController : ApiControllerBase
    {
        
        private readonly IPortadorAppService _portadorAppService;
        
        /// <summary>
        /// Injeção de dependencias e herança
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="portadorAppService"></param>
        public PortadorController(IAppEngine engine, IPortadorAppService portadorAppService) : base(engine)
        {
            _portadorAppService = portadorAppService;
        }
        
        /// <summary>
        /// Metodo responsavel por realizar a integração
        /// </summary>
        /// <param name="portadorIntegrarRequest"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("Integrar")]
        public JsonResult IntegrarPortador([FromBody] IntegrarPortadorRequest portadorIntegrarRequest)
        {
            try
            {
                var lIntegrarPortador = _portadorAppService.IntegrarPortador(portadorIntegrarRequest).Result;
                return lIntegrarPortador.sucesso
                    ? ResponseBaseApi.ResponderSucesso(lIntegrarPortador)
                    : ResponseBaseApi.ResponderErro(lIntegrarPortador.mensagem);
            }
            catch (Exception e)
            {
                return ResponseBaseApi.ResponderErro(e.Message);
            }
        }
    }
}