﻿using System.ComponentModel;

namespace SistemaInfo.BBC.Application.Objects.Web.Abastecimento
{
    public class AbastecimentoExport
    {
        [Description("Codigo abastecimento")]
        public int Codigo { get; set; }
        [Description("Codigo protocolo")]
        public int CodigoProtocolo { get; set; }
        [Description("Nota fiscal")]
        public string Nf { get; set; }
        [Description("Placa")]
        public string Placa { get; set; }
        [Description("Combustível")]
        public string Combustivel { get; set; }
        [Description("Litragem")]
        public string Litragem { get; set; }
        [Description("Valor abastecimento")]
        public string ValorAbastecimento { get; set; }
        [Description("Data  abastecimento")]
        public string DataAbastecimento { get; set; }
    }
    
    public class PagamentoAbastecimentoExport
    {
        [Description ("Codigo de Transacao")]
        public int Id { get; set; }
        [Description ("Razao Social")]
        public string RazaoSocial { get; set; }
        [Description ("Posto CNPJ")]
        public string Cnpj { get; set; }
        [Description ("Tarifa")]
        public string Tarifa { get; set; }
        [Description ("Cashback")]
        public string CashbackTransacao { get; set; }
        [Description ("Valor de Operacao")]
        public string ValorOperacao { get; set; }
        [Description ("Data de Pagamento")]
        public string DataPagamento { get; set; }
        [Description ("Status Financeiro")]
        public string Status { get; set; }
        [Description ("Status Pagamentos")]
        public string StatusProtocolo { get; set; }
    }
}