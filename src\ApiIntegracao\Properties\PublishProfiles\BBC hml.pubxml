﻿<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121.
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <WebPublishMethod>MsDeploy</WebPublishMethod>
        <ExcludeApp_Data>True</ExcludeApp_Data>
        <MSDeployServiceURL>https://sw19-142:8172</MSDeployServiceURL>
        <DeployIisAppPath>BBC-API-HML/BBC/apiintegracao</DeployIisAppPath>
        <RemoteSitePhysicalPath/>
        <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
        <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
        <EnableMSDeployBackup>False</EnableMSDeployBackup>
        <_SavePWD>False</_SavePWD>
        <SelfContained>false</SelfContained>
        <_IsPortable>true</_IsPortable>
        <AllowUntrustedCertificate>True</AllowUntrustedCertificate>
        <DeleteExistingFiles>True</DeleteExistingFiles>
        <ExcludeFromPackageFolders>*.config;*.json;logs\*</ExcludeFromPackageFolders>
    </PropertyGroup>
</Project>