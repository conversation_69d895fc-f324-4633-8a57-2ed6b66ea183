﻿using System;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Pedagio;

public class CancelarComplementoPagamentoPedagioResponse : RespPadrao
{
    public CancelarComplementoPagamentoPedagioResponse(){}
    public CancelarComplementoPagamentoPedagioResponse(bool aSucesso, string aMensagem, CancelarComplementoPagamentoPedagioResponseData aData = null)
    {
        sucesso = aSucesso;
        mensagem = aMensagem?.Length > 250 ? aMensagem.Substring(0, 250) : aMensagem;
        data = aData;
    }
    
    public new CancelarComplementoPagamentoPedagioResponseData data { get; set; }
}

public class CancelarComplementoPagamentoPedagioResponseData
{
    public int PagamentoPedagioId { get; set; }
    public decimal ValorCancelado { get; set; }
    public EStatusPagamentoPedagio Status { get; set; }
    public DateTime? DataCancelamento { get; set; }
}