﻿namespace SistemaInfo.BBC.Application.Objects.Api.PagamentoAbastecimento
{
    public class PagamentoAbastecimentoObservacao
    {
        public string PlacaVeiculo { get; set; }
        public string CodigoOperacao { get; set; }
        public string TipoOperacao { get; set; }
        public string RetencaoId { get; set; }
    }
    
    public class LotePagamentoAbastecimentoObservacao
    {
        public string CNPJ { get; set; }
        public string LoteId { get; set; }
        public string ValorPagamento { get; set; }
        public string DataPrevisao { get; set; }
    }
    
    public class LotePagamentoReceitaObservacao
    {
        public string CNPJ { get; set; }
        public string ValorBruto { get; set; }
        public string ValorEmpresa { get; set; }
        public string ValorMdr { get; set; }
        public string DataPrevisao { get; set; }
    }
    
    public class ReceitaPagamentoAbastecimentoObservacao
    {
        public string ContaTransitoria { get; set; }
        public string ContaReceita { get; set; }
        public string ValorTotalReceitaLiquida { get; set; }
        public string ReceitaId { get; set; }
    }
    
    public class LoteReceitaAbastecimentoObservacao
    {
        public string EmpresaId { get; set; }
        public string Protocolo { get; set; }
        public string TipoOperacao { get; set; }
        public string DataPrevPagamento { get; set; }
    }
}