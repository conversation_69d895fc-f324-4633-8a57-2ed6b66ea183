﻿using System;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Enum;

namespace SistemaInfo.BBC.Application.Objects.Api.Pedagio;

public class ComplementarPagamentoPedagioResponse : RespPadrao
{
    public ComplementarPagamentoPedagioResponse(){}
    public ComplementarPagamentoPedagioResponse(bool aSucesso, string aMensagem, ComplementarPagamentoPedagioResponseData aData = null)
    {
        sucesso = aSucesso;
        mensagem = aMensagem?.Length > 250 ? aMensagem.Substring(0, 250) : aMensagem;
        data = aData;
    }
    public new ComplementarPagamentoPedagioResponseData data { get; set; }
}

public class ComplementarPagamentoPedagioResponseData
{
    public int PagamentoPedagioId { get; set; }
    public int IdComplemento { get; set; }
    public decimal ValorTarifa { get; set; }
    public decimal Valor { get; set; }
    public int PagamentoTarifa { get; set; }
    public EStatusPagamentoPedagio Status { get; set; }
    public DateTime? DataBaixa { get; set; }
}