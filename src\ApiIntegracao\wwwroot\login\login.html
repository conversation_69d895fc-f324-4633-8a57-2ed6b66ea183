<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    <style>
    
        .body-login {
            margin-left: 0;
            margin-right: 0;
        }
    
        .login-cover {
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    
        .center-screen {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
    
        .center-body {
            width: 100%;
            background-color: rgba(255, 255, 255, 1);
            display: block;
            text-align: center;
        }
    
        .center-content {
            padding-top: 3%;
            padding-bottom: 3%;
            width: 30%;
            display: inline-block;
            text-align: center;
        }
    
        .form-group {
            margin-bottom: 20px;
        }

        .input-form {
            border: none;
            height: 25px;
            border-radius: 6px !important;
            width: 100%;
        }

        .input-form, .btn-login {
            height: 45px; /* Ajuste este valor conforme necessário */
            padding: 10px 16px;
            font-size: 1em;
            box-sizing: border-box;
        }

        .btn-login {
            width: 100%;
            display: inline-block;
            font-family: inherit;
            font-weight: 400;
            background-color: #056233;
            border-radius: 3px;
            border: 1px solid transparent;
            font-size: 1.2em;
            padding: 6px 12px;
            margin-top: 20px;
            cursor: pointer;
            white-space: nowrap;
            text-transform: none;
            text-align: center;
            color: #fff;
            line-height: 1.3333333;
        }
    
        .btn-login:hover {
            text-decoration: none;
        }

        .btn-login[disabled] {
            background-color: #056233; 
            cursor: not-allowed; 
            opacity: 0.7; 
        }
    
        .input-borderfocus:focus {
            border-style: solid !important;
            border-color: rgb(255, 205, 0) !important;
            border-radius: 6px;
            border-width: thin;
        }
    
        .input-bordebottom {
            border: lightgray;
            border-style: solid;
            border-width: thin;
        }

        .loading-bar {
            width: 100%;
            height: 4px;
            background-color: #056233;
            animation: loading 1.5s infinite;
            margin-top: 15px;
            border-radius: 2px;
        }

        @keyframes loading {
            0% {
                width: 0;
            }
            50% {
                width: 50%;
            }
            100% {
                width: 100%;
            }
        }
    </style>
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
</head>
<body>
    <div>
        <div class="login-cover">
            <div class="center-screen">
                <div class="center-body">
                    <div class="center-content">
                        <img class="img-responsive logo" src="assets/images/BBC_LOGO_DIGITAL_PANTONE.png" alt="Logo"
                            style="margin-bottom: 50px;" />
                        <form autocomplete="off" name="loginForm" role="form">
                            <div class="form-group">
                                <input type="text" id="cnpj-input" autofocus required placeholder="CNPJ" maxlength="14"
                                    class="input-form input-borderfocus input-bordebottom">
                            </div>
                            <div class="form-group">
                                <input type="password" id="senha-input" required placeholder="Senha api"
                                    class="input-form input-borderfocus input-bordebottom">
                            </div>
                            <div class="form-group">
                                <input type="text" id="client-secret" required placeholder="Client secret"
                                    class="input-form input-borderfocus input-bordebottom">
                            </div>
                            <div class="form-group">
                                <input type="text" id="empresa" placeholder="Empresa"
                                    class="input-form input-borderfocus input-bordebottom">
                            </div>
                            <button class="btn-login" onclick="logar(event)" id="login-btn">Visualizar documentação</button>
                            <div class="loading-bar" id="loading-bar" style="display: none;"></div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>

        $(document).ready(function(){
            $('#cnpj-input').mask('00.000.000/0000-00', {reverse: true});
        });

        $(document).ready(function(){
            $('#empresa').mask('00.000.000/0000-00', {reverse: true});
        });

        function carregandoRequisicao(isLoading) {
            const btn = document.getElementById("login-btn");
            const loadingBar = document.getElementById("loading-bar");

            if (isLoading) {
                loadingBar.style.display = "block";
                btn.disabled = true;
            } else {
                loadingBar.style.display = "none";
                btn.disabled = false;
            }
        }

        async function logar(event) {
            event.preventDefault();
            const cnpj = document.getElementById('cnpj-input').value;
            const senhaApi = document.getElementById('senha-input').value;
            const clientSecret = document.getElementById('client-secret').value;
            const empresa = document.getElementById('empresa').value;

            const btn = document.getElementById("login-btn");
            const loadingBar = document.getElementById("loading-bar");

           
            carregandoRequisicao(true);

            const data = {
                cnpj,
                senhaApi,
                clientSecret,
                empresa
            };
            try {
                const response = await fetch(window.location.origin + '/BBC/ApiIntegracao/AuthSession/Login', {
                    method: 'POST',
                    headers: {
                        "Content-Type": "application/json",
                        "SessionKey": localStorage.getItem("token")
                    },
                    body: JSON.stringify(data)
                });
                const result = await response.json();

                if (result.sucesso) {
                    carregandoRequisicao(false);
                    const token = result.data.token; 
                    document.cookie = `SessionKey=${token}; path=/;`;
                    window.location.href = window.location.origin + '/BBC/ApiIntegracao/swagger'; 
                } else {
                    carregandoRequisicao(false);
                    alert(result.mensagem);
                }

            } catch (error) {
                carregandoRequisicao(false);
                console.error('Erro ao conectar com a API:', error);
                alert('Erro ao conectar com a API: ' + error.message);
            } finally {
                carregandoRequisicao(false);
            }
        }
    </script>
</body>

</html>