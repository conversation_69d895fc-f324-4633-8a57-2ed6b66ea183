﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.Pedagio;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    public class ServicosPedagio : ApiControllerBase
    {

        private readonly IPedagioAppService _pedagioAppService;
        public ServicosPedagio(IAppEngine engine,IPedagioAppService pedagioAppService) : base(engine)
        {
            _pedagioAppService = pedagioAppService;
        }
        
        
        /// <summary>
        /// BAT_PDG_01: Consulta todos os pagamentos pendentes e tenta reenvia-los.
        /// Deve ser executado a cada 15 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("ReenviarPagamentosPedagioPendentes")]
        public async Task ReenviarPagamentosPendentes() => await _pedagioAppService.ServiceReenviarPagamentosPendentes();
        
        
        /// <summary>
        /// BAT_PDG_02: Consulta todos os cancelamentos pendentes e tenta reenvia-los.
        /// Deve ser executado a cada 15 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("ReenviarCancelamentosPendentes")]
        public async Task ReenviarCancelamentosPendentes() => await _pedagioAppService.ServiceReenviarPagamentosPendentes();
        
    }
}