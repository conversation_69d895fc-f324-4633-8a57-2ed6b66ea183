using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.Empresa;
using SistemaInfo.BBC.Application.Objects.Web.GruposUsuario;
using SistemaInfo.BBC.Application.Objects.Web.GruposUsuario.Request;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.GrupoUsuario.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.GrupoUsuario
{
    public interface IGrupoUsuarioAppService : IAppService<Domain.Models.GrupoUsuario.GrupoUsuario, IGrupoUsuarioReadRepository,
                      IGrupoUsuarioWriteRepository>    
    {
        ConsultarGrupoUsuarioMenuResponse ConsultarPorId(int idGrupoUsuario);
        
       ConsultarGridGrupoUsuarioResponse ConsultarGridGrupoUsuario(DtoConsultaGridGrupoUsuario request);
       ConsultarGridGrupoUsuarioResponse ConsultarGridGrupoUsuarioPosto(DtoConsultaGridGrupoUsuario request);
        
       Task SaveGrupoUsuario(ConsultarGrupoUsuarioResponse lModel);
       
       Task<ConsultarGridGrupoUsuarioResponse> ConsultarGrupoUsuarioCombo(int requestTake, int requestPage, 
           OrderFilters requestOrder, List<QueryFilters> requestFilters, int? grupoEmpresaId);

       GrupoUsuarioResponse ConsultarPorIdEmpresa(int id);

       Task<int> CadastrarGrupoUsuarioParaEmpresaAsync(GrupoUsuarioCadastrarRequest request);
       Task<ConsultarEmpresaComboResponse> ConsultaGridEmpresasVinculadasCombo
           (int requestTake, int requestPage, OrderFilters requestOrder, List<QueryFilters> requestFilters, List<int> listaIdsEmpresa, int? grupoUsuarioId);
    }
}