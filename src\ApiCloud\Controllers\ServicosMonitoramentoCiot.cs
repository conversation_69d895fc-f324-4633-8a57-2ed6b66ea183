﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SistemaInfo.BBC.ApiCloud.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.MonitoramentoCiot;
using SistemaInfo.BBC.Application.Objects.Web.ServidorCiot;
using SistemaInfo.BBC.Domain.Contracts.Servidor;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiCloud.Controllers
{
    public class ServicosMonitoramentoCiot : ApiControllerBase
    {
        private readonly IMonitoramentoCiotAppService _appService;
        public ServicosMonitoramentoCiot(IAppEngine engine, IMonitoramentoCiotAppService appService) : base(engine)
        {
            _appService = appService;
        }
        
        /// <summary>
        /// BAT_MCIOT_01: VerificaContigencia no CIOT.
        /// Deve ser executado a cada 15 minutos.
        /// </summary>
        [AllowAnonymous]
        [HttpPost("VerificaContigencia")]
        public async Task VerificaContigencia() => await _appService.VerificaContigencia(new List<ConsultarServidorCiotGrid>());
    }
}