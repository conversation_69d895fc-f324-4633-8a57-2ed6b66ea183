using System.Linq;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Transacao;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.Models.Transacao.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Transacao
{
    public interface ITransacaoRegisterAppService : IAppService<Domain.Models.Transacao.Transacao, ITransacaoReadRepository, ITransacaoWriteRepository>
    {
        Task<RespPadrao> RegistrarTransacao(TransacaoRequest transacaoRequest);
        IQueryable<Domain.Models.Transacao.Transacao> GetTransacoesByIdPagamentoEvento(int idPagamentoEvento);
        Task AtualizarTransacao(TransacaoAlterarStatusRequest ltransacaoRequest);
    }
}
