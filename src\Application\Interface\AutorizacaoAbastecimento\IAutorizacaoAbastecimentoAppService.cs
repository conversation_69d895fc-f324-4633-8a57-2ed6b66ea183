using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.AutorizacaoAbastecimento.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento
{
    public interface IAutorizacaoAbastecimentoAppService : IAppService<Domain.Models.AutorizacaoAbastecimento.AutorizacaoAbastecimento, IAutorizacaoAbastecimentoReadRepository, IAutorizacaoAbastecimentoWriteRepository>
    {
        ConsultarGridAutorizacaoAbastecimentoResponse ConsultarGridAutorizacaoAbastecimento(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        AutorizacaoAbastecimentoResponse ConsultarPorId(int idAutorizacaoAbastecimento);
        Task<RespPadrao> Save(List<AutorizacaoAbastecimentoRequest> lAutorizacaoAbastecimentoReq, bool integracao = false);
        Task<RespPadraoApi> IntegrarAutorizacaoAbastecimento(AutorizarAbastecimentoIntegrarRequest autorizarAbastecimentoRequest);
        Task<RespPadraoApi> CancelarAutorizacaoAbastecimento(CancelarAbastecimentoIntegrarRequest autorizarAbastecimentoRequest);
    }
}