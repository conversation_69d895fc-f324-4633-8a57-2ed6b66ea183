using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Documento;
using SistemaInfo.BBC.Domain.Models.Documento.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Documento
{
    public interface IDocumentoAppService : IAppService<Domain.Models.Documento.Documento, IDocumentoReadRepository,IDocumentoWriteRepository>
    {
        Task<DocumentoResponse> EnviarImagem(DocumentoRequest request);
    }
}