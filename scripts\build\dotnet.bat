:: Script de build e execucao de testes unitários
:: exit code 0 = Sucesso
:: exit code 1 = Falha de compilação
:: exit code 2 = Falha nos testes unitários

:: Init
echo off
echo.
call :log ==========================================================================================
call :log ::::: %0 start :::::
call :log BUILD SCRIPT START
call :log ==========================================================================================
echo.

set Seven="C:\Program Files\7-Zip\7z.exe"
if not exist %Seven% (
	set Seven="C:\Program Files (x86)\7-Zip\7z.exe"
)
if not exist %Seven% (
	call :log 7z.exe nao encontrado!
    goto :EndError
)
set test_error=0
set tipo=Release
set projeto=BBC
set repo_dir=%~dp0
call :log Diretorio atual: %~dp0
call :log Alterando para raiz do repositorio
cd "%~dp0%\..\.."
call :log Diretorio atual: %cd%
set root_dir=%cd%
set publish_dir=%root_dir%\src\artefatos

echo.
:: Variáveis em uso
call :log ::: Variaveis em uso :::
call :log Publish dir: %publish_dir%
call :log Seven: %Seven%
dotnet --list-sdks
dotnet --version
goto :teste
call :log ------------------------------------------------------------------------------------------

:build
:: Excluíndo arquivos atualmente na pasta destino da publicação
call :log ::: Excluindo builds antigos :::
echo Excluindo builds antigos
if exist "%publish_dir%" rd /S /Q "%publish_dir%"
call :log OK
call :log ------------------------------------------------------------------------------------------

call :log ::: Publish Api :::
::dotnet publish ".\src\Web\Web.csproj" -c Release -o "%publish_dir%\Web" --no-restore

set msbuildpath="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
if not exist %msbuildpath% (
	set msbuildpath="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %msbuildpath% (
	call :EndError msbuildpath.exe nao localizado
)
echo.
call :log Clean
call %msbuildpath% %root_dir%\src\%projeto%.sln -t:Clean /property:Configuration=%tipo% /verbosity:q
echo.
call :log Restore
dotnet restore %root_dir%\src\%projeto%.sln
echo.
call :log Build
call %msbuildpath% %root_dir%\src\%projeto%.sln /p:WarningLevel=0 /property:Configuration=%tipo% /verbosity:m

if not %errorlevel% == 0 (
    call :log BUILD ERROR Web
    goto :EndError
)
echo.
call :log OK
call :log ------------------------------------------------------------------------------------------
:teste
echo.
call :log ==========================================================================================
call :log Iniciando Testes
call :log ==========================================================================================
set vstestpath="C:\Program Files (x86)\Microsoft Visual Studio\2019\TestAgent\Common7\IDE\CommonExtensions\Microsoft\TestWindow\vstest.console.exe"
if not exist %vstestpath% (
	set vstestpath="C:\Program Files (x86)\Microsoft Visual Studio\2017\TestAgent\Common7\IDE\CommonExtensions\Microsoft\TestWindow\vstest.console.exe"
)
if not exist %vstestpath% (
	call :EndError vstestpath.exe nao localizado
)

::call "%vstestpath%" %root_dir%\tests\%projeto%.Test\bin\%tipo%\netcoreapp3.1\%projeto%.Test.dll /InIsolation --diag:%root_dir%\diag\diag.txt /Tests:UsuarioCadastro_CadastroInvalido,UsuarioCadastro_CadastroValido
::call %vstestpath% %root_dir%\tests\%projeto%.Test\bin\%tipo%\netcoreapp3.1\%projeto%.Test.dll /Parallel
call %vstestpath% %root_dir%\tests\%projeto%.Test\bin\%tipo%\netcoreapp3.1\%projeto%.Test.dll  /Parallel /logger:trx

if not %errorlevel% == 0 (
    call :EndError Falha nos testes unitários
)
goto :EndSuccess

:: Functions
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo %~n0%~x0^> %*
	exit /B 0

:EndError
    echo.
    call :log ==========================================================================================
    call :log BUILD SCRIPT FAILED
	if (%test_error% == 1) (
		call :log TESTE UNITARIO COM INCONSISTENCIAS
	)
    call :log ::::: %0 end :::::
    call :log ==========================================================================================
    echo.
	
	if (%test_error% == 1) (		
		exit 2
	)
    exit 1

:EndSuccess
    echo.
    call :log ==========================================================================================
    call :log BUILD SCRIPT OK
    call :log ::::: %0 end :::::
    call :log ==========================================================================================
    echo.    