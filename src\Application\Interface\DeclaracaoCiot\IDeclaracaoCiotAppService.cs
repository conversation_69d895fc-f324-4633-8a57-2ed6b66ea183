using System.Collections.Generic;
using System.Threading.Tasks;
using DinkToPdf.Contracts;
using SistemaInfo.BBC.Application.Interface.Base;
using SistemaInfo.BBC.Application.Objects.Api.Ciot;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.DeclaracaoCiot;
using SistemaInfo.BBC.Domain.Contracts.Operacoes;
using SistemaInfo.BBC.Domain.External.CIOT.DTO;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.DeclaracaoCiot.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.DeclaracaoCiot
{
    public interface IDeclaracaoCiotAppService : IAppService<Domain.Models.DeclaracaoCiot.DeclaracaoCiot, 
        IDeclaracaoCiotReadRepository, IDeclaracaoCiotWriteRepository>,
        IBaseGetAppService<ConsultarPorIdCiotResponse>
    {
        ConsultarGridCiotResponse ConsultarGridPainelCiot(int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        Task<ConsultarGridOperacaoTransporteResponse> ConsultarGridOperacaoTransporte(ConsultarGridOperacaoTransporteRequest request);
        Task<ConsultarGridOperacaoTransporteHistoricoResponse> ConsultarGridOperacaoTransporteHistorico(ConsultarGridOperacaoTransporteHistoricoRequest request);
        Task<ConsultarOperacaoTransportePorIdRespMessage> ConsultarOperacaoTransportePorId(int idOperacaoTransporte);
        Task<ConsultarVeiculosCiotRespMessage> ConsultaVeiculosCiot(int idOperacaoTransporte);
        List<ConsultarDeclaracaoCiotResponse> ConsultarDeclaracaoCiot(int idPortador);
        CiotConsultarApiResponse ConsultarDeclaracaoCiot(CiotConsultarApiRequest request);
        Task<RespPadrao> Save(CiotRequest lModel);
        Task<RespPadraoApi> Save(CiotRetificarApiRequest lModel);
        string Valida(string dataEmissao, string dataFim, string ciot, bool cancel);
        Task<RespPadrao> AlterarStatusCancelado(CiotStatusRequest request);
        Task<RespPadraoApi> AlterarStatusCancelado(CiotCancelarApiRequest lCiotStatus);
        Task<RespPadrao> AlterarStatusEncerrado(CiotStatusRequest request);
        Task<RespPadraoApi> AlterarStatusEncerrado(CiotEncerrarApiRequest request);
        Task<RespEquiparadoTAC> ConsultarSituacaoTransportador(int proprietarioId);
        Task<RespPadrao> RetificarCiotOperacaoTransporte(RetificarOperacaoTransporteReq request);
        ConsultarSituacaoTransportadorResp ConsultarSituacaoTransportadorAPI(ConsultarSituacaoTransportadorReq req);

        #region Tarefas
        
        Task ServiceEncerrarCiots();
        Task ServiceCancelarCiots();

        #endregion
        
        
        IDocument ImprimirCiot(int ciotId);
        //CARUANA
//        CancelarOperacaoTransporteResp CancelarOperacao(CancelarOperacaoTransporteReq req);
        //ConsultarSituacaoTransportadorResp ConsultarSituacaoTransportador(ConsultarSituacaoTransportadorReq req);
//        DeclararOperacaoTransporteResp DeclararOperacaoTransporte(DeclararOperacaoTransporteReq req);
//        EncerrarOperacaoTransporteResp EncerrarOperacaoTransporte(EncerrarOperacaoTransporteReq req);
//        RetificarOperacaoTransporteResp RetificarOperacaoTransporte(RetificarOperacaoTransporteReq req);        
    }
}