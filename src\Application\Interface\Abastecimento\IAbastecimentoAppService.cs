using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Mobile.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.Abastecimento;
using SistemaInfo.BBC.Application.Objects.Web.ManutencaoAbastecimento;
using SistemaInfo.BBC.Domain.External.Mobile2You.DTO;
using SistemaInfo.BBC.Domain.Grid;
using SistemaInfo.BBC.Domain.Models.Abastecimento.Repository;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.DataTransferObjects;

namespace SistemaInfo.BBC.Application.Interface.Abastecimento
{
    public interface IAbastecimentoAppService : IAppService<Domain.Models.Abastecimento.Abastecimento, IAbastecimentoReadRepository, IAbastecimentoWriteRepository>
    {
        ConsultarGridAbastecimentoResponse ConsultarGridAbastecimento(int IdPosto, int take, int page, 
            OrderFilters orderFilters, List<QueryFilters> filters, DateTime? dataInicial = null, DateTime? dataFinal = null);
        AbastecimentoResponse ConsultarPorId(int idAbastecimento, string portadorCpfCnpj);
        List<AbastecimentosResponse> ConsultarAbastecimentosPorPortadorId(DateTime dtIni, DateTime dtFim, int idPortador);
        Task<RespPadrao> Save(AbastecimentoRequest lAbastecimentoReq, bool integracao = false);
        Task<RespPadrao> ConsultarFuncionario(string portadorCpfCnpj);
        Task<RespPadrao> Cancelar(int abastecimentoId, string portadorId, bool integracao = false);
        RespPadrao IntegracaoAbastecimentoMobile (IntegrarAbastecimentoMobileRequest request, string portadorCpfCnpj);
        RespPadrao ImpressaoComprovanteAbastecimentoApi (ImpressaoComprovanteAbastecimentoMobileRequest printRequest, string portadorCpfCnpj);
        
        ConsultarGridHistoricoManutencaoAbastecimentoResponse ConsultarGridHistoricoCancelamentos(int IdPosto, DateTime dtInicial, DateTime dtFinal, int status, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters); 
        RespPadrao ReimpressaoProtocolo(ManutencaoAbastecimentoReimpressaoRequest request);
        ConsultarGridManutencaoAbastecimento ConsultarAbastecimentos(int idEmpresa, DateTime dtInicial, DateTime dtFinal, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        
        ConsultarGridAbastecimentoPainelFinanceiroResponse ConsultarGridLoteAbastecimentos(ISessionUser user, int pagamentoAbastecimento, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarGridAbastecimentoPainelFinanceiroResponse ConsultarGridLoteAbastecimentos(int idPosto, int pagamentoAbastecimento, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarGridLoteAbastecimentoResponse ConsultarGridLoteAbastecimento(int lotePagamento, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);

        ConsultarGridAbastecimentosProtocoloResponse ConsultarGridProtocoloAbastecimento(int protocolo, int take, int page, OrderFilters orderFilters, List<QueryFilters> filters);
        ConsultarPostoAbastecimentoMobileResponse ConsultaPostoAbastecimento(CnpjPostQrCodeRequest request);
        RespPadrao EnviarComprovanteAbastecimentoEmail(ManutencaoAbastecimentoReimpressaoRequest request);
        ConsultarGridPainelAbastecimentoResponse ConsultarGridPainelAbastecimento(DtoConsultaGridPainelAbastecimento request);
    }
}