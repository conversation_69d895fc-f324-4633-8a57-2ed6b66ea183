echo off
echo %~dp0
set pastaOrigem=%~dp0
call %pastaOrigem%variaveis.bat
setlocal enabledelayedexpansion

cd %projeto%
call :log git status
if exist "%publish_dir%" rd /S /Q "%publish_dir%"
mkdir "%publish_dir%"
set temp=%publish_dir%\branch-build.h
git branch --show-current >> %temp%
set /p branchname=<%temp%
call :log %branchname%
git status

dotnet nuget remove source "Sistema Info"
dotnet nuget add source https://nuget.sistemainfo.com.br/ --name "Sistema Info"
dotnet nuget list source

:input
	call :info Digite o projeto para build
	call :opt 1 - Completo
	call :opt 2 - Web
	call :opt 3 - API JSL
	call :opt 4 - API Integracao
	call :opt 5 - API Abastecimento
	call :opt 6 - API Mobile Abastecimentos
	call :opt 7 - API Service
	call :opt 8 - API Ciot
	call :opt 9 - API Mobile Pagamentos
	echo.
	set /p build=""
	call :info build selecionado: %build%

if %build%==1 (
	dotnet clean -c Release ".\src\%solution%.sln" -v:m
	
	call :log publish controle							1/8
	dotnet publish ".\src\Web\Web.csproj" -c Release -o ".\src\artefatos\BBC - BBC Controle\bin"
	
	call :log copia back do controle para o posto		2/8
	mkdir ".\src\artefatos\BBC - Rede BBC Controle\bin"
	xcopy ".\src\artefatos\BBC - BBC Controle\bin" ".\src\artefatos\BBC - Rede BBC Controle\bin" /E /I /Y

	call :log publish api								3/8
	dotnet publish ".\src\Api\Api.csproj" -c Release -o ".\src\artefatos\BBC - API"

	call :log publish api integracao					4/8
	dotnet publish ".\src\ApiIntegracao\ApiIntegracao.csproj" -c Release -o ".\src\artefatos\BBC - API Integracao"

	call :log publish api ciot							5/8
	dotnet publish ".\src\ApiCiot\ApiCiot.csproj" -c Release -o ".\src\artefatos\BBC - API Ciot"
	
	call :log publish api abastecimento					6/8
	dotnet publish ".\src\ApiAbastecimento\ApiAbastecimento.csproj" -c Release -o ".\src\artefatos\BBC - API Abastecimento"

	call :log publish api mobile						7/8
	dotnet publish ".\src\Mobile\Mobile.csproj" -c Release -o ".\src\artefatos\BBC - Mobile"

	call :log publish api cloud							8/8
	dotnet publish ".\src\ApiCloud\ApiCloud.csproj" -c Release -o ".\src\artefatos\BBC - API Service"
	
	call :log publish api mobile pagamentos
	dotnet publish ".\src\Mobile.Pagamentos\Mobile.Pagamentos.csproj" -c Release -o ".\src\artefatos\BBC - Mobile Pagamentos"	
	call :fim
)
if %build%==2 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish controle
	dotnet publish ".\src\Web\Web.csproj" -c Release -o ".\src\artefatos\BBC - BBC Controle\bin"

	call :log copia back do controle para o posto
	mkdir ".\src\artefatos\BBC - Rede BBC Controle\bin"
	xcopy ".\src\artefatos\BBC - BBC Controle\bin" ".\src\artefatos\BBC - Rede BBC Controle\bin" /E /I /Y
	call :fim
)
if %build%==3 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish api
	dotnet publish ".\src\Api\Api.csproj" -c Release -o ".\src\artefatos\BBC - API"
	call :fim
)
if %build%==4 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish api integracao
	dotnet publish ".\src\ApiIntegracao\ApiIntegracao.csproj" -c Release -o ".\src\artefatos\BBC - API Integracao"
	call :fim
)
if %build%==5 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish api abastecimento
	dotnet publish ".\src\ApiAbastecimento\ApiAbastecimento.csproj" -c Release -o ".\src\artefatos\BBC - API Abastecimento"
	call :fim
)
if %build%==6 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish api mobile
	dotnet publish ".\src\Mobile\Mobile.csproj" -c Release -o ".\src\artefatos\BBC - Mobile"
	call :fim
)
if %build%==7 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish api cloud
	dotnet publish ".\src\ApiCloud\ApiCloud.csproj" -c Release -o ".\src\artefatos\BBC - API Service"
	call :fim
)
if %build%==8 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish api ciot
	dotnet publish ".\src\ApiCiot\ApiCiot.csproj" -c Release -o ".\src\artefatos\BBC - API Ciot"
	call :fim
)
if %build%==9 (
	dotnet clean  -c Release ".\src\%solution%.sln"
	call :log publish api mobile pagamentos
	dotnet publish ".\src\Mobile.Pagamentos\Mobile.Pagamentos.csproj" -c Release -o ".\src\artefatos\BBC - Mobile Pagamentos"
	call :fim
)
else (
	call :warn opcao nao existente!
	goto :input
)
call :fim

:fim
	call %pastaOrigem%remove-config.bat
	if %errorlevel%==1 (call :erro) else (call :sucesso)
	exit %errorlevel%
	
:: Sets up the ESC string for use later in this script
:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0
	
:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:info
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo.
	exit /B 0

:warn
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo !ESC![93m	%*
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:erro
	echo.
	call :setESC
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo !ESC![91m	%~n0%~x0^> %*
	echo !ESC![91m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 1

:sucesso
	echo.
	call :setESC
	echo !ESC![92m===================================================================================================!!ESC![0m
	echo !ESC![92m	Operacao Concluida!!ESC![0m
	echo !ESC![92m===================================================================================================!!ESC![0m
	echo.
	if %NoStop%==False (pause)
	exit 0