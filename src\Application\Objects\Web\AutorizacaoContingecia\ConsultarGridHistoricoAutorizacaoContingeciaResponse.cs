using System.Collections.Generic;

namespace SistemaInfo.BBC.Application.Objects.Web.AutorizacaoContingecia
{
    public class ConsultarGridHistoricoAutorizacaoContingecia
    {
        public int Id { get; set; }
        public int? CombustivelId { get; set; }
        public string NomeCombustivel { get; set; }
        public string Placa { get; set; }
        public decimal? Litragem { get; set; }
        public int? PostoId { get; set; }
        public string NomePosto { get; set; }
        public decimal? Valor { get; set; }
        public string Status { get; set; }
        public string Motivo { get; set; }
        public string MotivoExterno { get; set; }
        public string MotivoSolicitacao { get; set; }
        public string DataCadastro { get; set; }
        public string UsuarioAlteracao { get; set; }
        public string DataAlteracao { get; set; }
        

    }

    
    public class ConsultarGridHistoricoAutorizacaoContingeciaResponse
    {
        public int totalItems { get; set; }
        public List<ConsultarGridHistoricoAutorizacaoContingecia> items{ get; set; }
    }
}