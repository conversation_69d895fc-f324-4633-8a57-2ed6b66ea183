using Microsoft.EntityFrameworkCore;
using SistemaInfo.BBC.Domain.Models.AuthClientSecret.Repository;
using SistemaInfo.BBC.Infra.Data.Context;
using SistemaInfo.Framework.DomainDrivenDesign.Infra.Repository;

namespace SistemaInfo.BBC.Infra.Data.Repository.AuthClientSecret
{
    
    public class AuthClientSecretBaseWriteRepository<TContext, TAuthClientSecretEntity> : WriteOnlyRepository<TAuthClientSecretEntity, TContext>
        where TContext : DbContext
        where TAuthClientSecretEntity : Domain.Models.AuthClientSecret.AuthClientSecret
    {
        public AuthClientSecretBaseWriteRepository(TContext context) : base(context)
        {
        }
    }
 
    public class AuthClientSecretWriteRepository : AuthClientSecretBaseWriteRepository<ConfigContext, Domain.Models.AuthClientSecret.AuthClientSecret>, IAuthClientSecretWriteRepository
    {
        public AuthClientSecretWriteRepository(ConfigContext context) : base(context)
        {
        }
    }
}
