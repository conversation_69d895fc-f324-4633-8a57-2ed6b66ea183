﻿using SistemaInfo.BBC.Application.Objects.Web.ConfiguracaoMonitoramentoCiot;
using SistemaInfo.BBC.Application.Objects.Web.ServidorCiot;
using SistemaInfo.BBC.Domain.Contracts.MonitoramentoCiot;
using SistemaInfo.BBC.Domain.Contracts.Parametro;
using SistemaInfo.BBC.Domain.Contracts.Servidor;
using SistemaInfo.BBC.Domain.Models.ServidorCiot;
using SistemaInfo.BBC.Domain.Models.ServidorCiot.Commands;
using SistemaInfo.Framework.Utils;
using StatusServidorCiot = SistemaInfo.BBC.Domain.Models.ServidorCiot.StatusServidorCiot;

namespace SistemaInfo.BBC.Application.AutoMapper.ServidorCiotMapper
{
    public class ServidorCiotMappingProfile : SistemaInfoMappingProfile
    {
        public ServidorCiotMappingProfile()
        {

            CreateMap<ConfiguracaoMonitoramentoCiotRequest, SincronizarParametroCiotMessage>();

            CreateMap<ServidorCiot, ServidorSincronizarMessage>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.ServidorId))
                .ForMember(dest => dest.StatusServidorCiot, opts => opts.MapFrom(s => s.Status));
            
            CreateMap<ServidorCiot, ConsultarServidorCiotGrid>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(s => s.ServidorId))
                .ForMember(dest => dest.Nome, opts => opts.MapFrom(s => s.Nome))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(s => s.Status.GetHashCode()))
                .ForMember(dest => dest.Ativo, opts => opts.MapFrom(s => s.Ativo))
                .ForMember(dest => dest.StatusComunicacaoAntt, opts => opts.MapFrom(s => s.StatusComunicacaoAntt.GetHashCode()))
                .ForMember(dest => dest.TipoServidor, opts => opts.MapFrom(s => s.TipoServidor.GetHashCode()))
                .ForMember(dest => dest.Link, opts => opts.MapFrom(s => s.Link))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastrada.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao, opts => opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.UsuarioAlteracaoId, opts => opts.MapFrom(s => s.UsuarioAlteracaoId))
                .ForMember(dest => dest.UsuarioCadastroId, opts => opts.MapFrom(s => s.UsuarioCadastroId));

            CreateMap<ServidorCiot, ConsultarServidorCiotHistoricoResponse>()
                .ForMember(dest => dest.DataCadastro, 
                    opts => opts.MapFrom(s => s.DataCadastrada.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataAlteracao, opts =>  opts.MapFrom(s => s.DataAlteracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.Status, 
                    opts => opts.MapFrom(s => s.Status.GetHashCode()))
                .ForMember(dest => dest.StatusComunicacaoAntt,
                    opts => opts.MapFrom(s => s.StatusComunicacaoAntt.GetHashCode()))
                .ForMember(dest => dest.TipoServidor, 
                    opts => opts.MapFrom(s => s.TipoServidor.GetHashCode()));
            
            CreateMap<ServidorCiot, ConsultarServidorCiotResponse>()
                .ForMember(dest => dest.Status, 
                    opts => opts.MapFrom(s => s.Status.GetHashCode()))
                .ForMember(dest => dest.StatusComunicacaoAntt,
                    opts => opts.MapFrom(s => s.StatusComunicacaoAntt.GetHashCode()))
                .ForMember(dest => dest.TipoServidor, 
                    opts => opts.MapFrom(s => s.TipoServidor.GetHashCode()));
            
            CreateMap<DadosServidorGrid, ServidorSincronizarMessage>()
                .ForMember(dest => dest.Ativo, 
                    opts => opts.MapFrom(s => s.Ativo))
                .ForMember(dest => dest.StatusServidorCiot, 
                    opts => opts.MapFrom(s => s.Status.ToEnum<StatusServidorCiot>()))
                .ForMember(dest => dest.StatusComunicacaoAntt,
                    opts => opts.MapFrom(s => s.StatusComunicacaoAntt.GetHashCode()))
                .ForMember(dest => dest.TipoServidor, 
                    opts => opts.MapFrom(s => s.TipoServidor.GetHashCode()));

            CreateMap<DadosServidorGrid, ServidorCiotSalvarCommand>()
                .ForMember(dest => dest.ServidorId, opts => opts.MapFrom(s => s.Id))
                .ForMember(dest => dest.Status,
                    opts => opts.MapFrom(s => s.Status.GetHashCode()))
                .ForMember(dest => dest.TipoServidor,
                    opts => opts.MapFrom(s => s.TipoServidor.GetHashCode()))
                .ForMember(dest => dest.StatusComunicacaoAntt,
                    opts => opts.MapFrom(s => s.StatusComunicacaoAntt.GetHashCode()));
            
            CreateMap<CadastrarServidorCiotRequest, ServidorCiotSalvarCommand>()
                .ForMember(dest => dest.ServidorId, opts => opts.MapFrom(s => s.Id));
            
            CreateMap<ServidorCiotSalvarCommand, ServidorCiot>();
            

            CreateMap<ServidorCiotEditarCommand, ServidorCiot>();
            
            CreateMap<ConsultarServidorCiotResponse, ServidorCiotEditarCommand>()
                .ForMember(dest => dest.StatusComunicacaoAntt,
                    opts => opts.MapFrom(s => s.StatusComunicacaoAntt.GetHashCode()))
                .ForMember(dest => dest.TipoServidor, 
                    opts => opts.MapFrom(s => s.TipoServidor.GetHashCode()));
            
            
        }
    }
}