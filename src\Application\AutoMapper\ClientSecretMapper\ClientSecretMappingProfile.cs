﻿using System;
using SistemaInfo.BBC.Application.Objects.Web.ClientSecret;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.ClientSecret;
using SistemaInfo.BBC.Domain.Models.ClientSecret.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.ClientSecretMapper
{
    public class ClientSecretMappingProfile : SistemaInfoMappingProfile
    {
        public ClientSecretMappingProfile()
        {
            CreateMap<ConsultarClientSecretResponse, ClientSecretEditarCommand>();
            
            CreateMap<ClientSecretAlterarStatusRequest, ClientSecretAlterarStatusCommand>();
            
            CreateMap<ClientSecretEditarCommand, ClientSecret>();
            
            CreateMap<ClientSecret, ClientSecretEditarCommand>()
                .ForMember(dest => dest.EmpresaId, opts => opts.MapFrom(s => s.IdEmpresa));
            
            CreateMap<ClientSecret, ClientSecretAdicionarCommand>()
                .ForMember(dest => dest.EmpresaId, opts => opts.MapFrom(s => s.IdEmpresa));
            
            CreateMap<ClientSecretRequest, ClientSecretAdicionarCommand>()
                .ForMember(dest => dest.EmpresaId, opts => opts.MapFrom(s => s.IdEmpresa));
            
            CreateMap<ClientSecretRequest, ClientSecretEditarCommand>()
                .ForMember(dest => dest.EmpresaId, opts => opts.MapFrom(s => s.IdEmpresa));
            
            CreateMap<ClientSecretAdicionarCommand, ClientSecret>()
                .ForMember(dest => dest.IdEmpresa, opts => opts.MapFrom(s => s.EmpresaId));

            CreateMap<ClientSecretEditarCommand, ClientSecret>()
                .ForMember(dest => dest.IdEmpresa, opts => opts.MapFrom(s => s.EmpresaId));

            CreateMap<ClientSecret, ConsultarClientSecretGrid>()
                .ForMember(dest => dest.SecretKey, opts => opts.MapFrom(s => s.SecretKey.Remove(4,28).Insert(4,"******************")))
                .ForMember(dest => dest.SecretKeySearch, opts => opts.MapFrom(s => s.SecretKey))
                .ForMember(dest => dest.DataCadastro, opts => opts.MapFrom(s => s.DataCadastro.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataExpiracao, opts => opts.MapFrom(s => s.DataExpiracao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.DataDesativacao, opts => opts.MapFrom(s => s.DataDesativacao.ToStringBr(FormatDateTimeMethod.ShortDateTime)))
                .ForMember(dest => dest.Ativo, opts => opts.MapFrom(s => (s.DataExpiracao.HasValue) ? s.DataExpiracao < DateTime.Now ? 0 : s.Ativo : s.Ativo ))
                .ForMember(dest => dest.GrupoEmpresa, opts => opts.MapFrom(s => s.GrupoEmpresa != null ? s.GrupoEmpresa.RazaoSocial : ""))
                .ForMember(dest => dest.UsuarioCadastro, opts => opts.MapFrom(s =>$"{s.UsuarioCadastroId} - {s.Usuario.Nome}"))
                .ForMember(dest => dest.Empresa, opts => opts.MapFrom(s => s.Empresa != null ? s.Empresa.RazaoSocial : ""))
                .ForMember(dest => dest.Cnpj, opts => opts.MapFrom(s => s.Empresa != null ? s.Empresa.Cnpj.ToCNPJFormato() : (s.GrupoEmpresa != null ? s.GrupoEmpresa.Cnpj.ToCNPJFormato() : "")))
                .ForMember(dest => dest.CnpjSearch, opts => opts.MapFrom(s => s.Empresa != null ? s.Empresa.Cnpj : (s.GrupoEmpresa != null ? s.GrupoEmpresa.Cnpj: "")));
            
            CreateMap<ClientSecret, ConsultarClientSecretResponse>()
                .ForMember(dest => dest.NomeEmpresa, opts => opts.MapFrom(s => s.Empresa.NomeFantasia))
                .ForMember(dest => dest.NomeGrupoEmpresa, opts => opts.MapFrom(s => s.GrupoEmpresa.RazaoSocial))
                .ForMember(dest => dest.EmpresaId, opts => opts.MapFrom(s => s.IdEmpresa))
                .ForMember(dest => dest.SecretKey, opts => opts.MapFrom(s => s.SecretKey.Remove(4,28).Insert(4,"******************")));

            CreateMap<ClientSecretEditarCommand, ConsultarClientSecretResponse>();
        }
    }
}