using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Api.Pix.Transferencia;
using SistemaInfo.BBC.Application.Objects.Api.Viagem;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Pix;
using SistemaInfo.BBC.Domain.Models.Pix.Responses.Transferencia;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Pix
{
    public interface IPixAppService : IAppService
    {
        Task<RespPadrao> ValidarChave(int idContaResponsavelPelaValidacao, string chave);
        Task<CriarTransferenciaPixResponse> CriarTransferenciaPix(CriarTransferenciaPixRequest request, int idContaOrigem);
        Task<ConsultarTransferenciaResponse> ConsultarTransferencia(string transationCode);
        Task<RespPadrao> ConsultarPixManual(int idPagamentoEvento);
        Task<RespPadrao> ConsultarChave(string documento, string chave);
        Task ServiceConsultarStatusPix();
        Task<string> RetornarMensagemDockPix(string mensagemTraduzida, string dockResponseMessage, string dockResponseReturnMessage);
    }
}