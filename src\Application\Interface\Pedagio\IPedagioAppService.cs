﻿using System.Threading.Tasks;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.BBC.Application.Objects.Web.CentralNotificacoes;
using SistemaInfo.BBC.Application.Objects.Web.CentralPendencias;
using SistemaInfo.BBC.Application.Objects.Web.PagamentoValePedagio;
using SistemaInfo.BBC.Domain.Contracts.Pedagio;
using SistemaInfo.Framework.DomainDrivenDesign.Application.Service;

namespace SistemaInfo.BBC.Application.Interface.Pedagio;

public interface IPedagioAppService : IAppService
{
    Task<ConsultarGridPagamentoValePedagioResponse> ConsultarGridPagamentoValePedagio(ConsultarGridPagamentoValePedagioRequest request);
    Task<RespPadrao> ConsultarPorId(int idPagamentoValePedagio);
    Task<ConsultarGridTransacaoPagamentoValePedagioResponse> ConsultarGridTransacaoPagamentoValePedagio(ConsultarGridTransacaoPagamentoValePedagioRequest request);
    Task<ConsultarGridPagamentoValePedagioHistoricoResponse> ConsultarGridPagamentoValePedagioHistorico(ConsultarGridTransacaoPagamentoValePedagioRequest request);

    #region Service
    
    Task ServiceReenviarPagamentosPendentes();
    Task ServiceReenviarCancelamentosPendentes();

    #endregion
    Task<IntegrarPedagioMessageResponse> ReenviarPagamentoPedagio(int idPagamentoValePedagio);
    Task<RespPadrao> ConsultarNotificacaoPorId(int idCentralNotificacao);
    Task<ConsultarGridCentralNotificacoesValePedagioResponse> ConsultarGridCentralNotificacoesValePedagio(ConsultarGridCentralNotificacoesValePedagioRequest request);
    Task<ConsultarGridCentralPendenciasValePedagioResponse> ConsultarGridCentralPendenciasValePedagio(ConsultarGridCentralPendenciasValePedagioRequest request);
}