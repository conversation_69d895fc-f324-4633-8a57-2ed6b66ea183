echo off
echo %~dp0
set pastaOrigem=%~dp0
echo off
setlocal enabledelayedexpansion
call %pastaOrigem%variaveis.bat

cd %projeto%

if not exist ".\src\artefatos" mkdir ".\src\artefatos"
set temp=%projeto%\src\artefatos\branch-front.h
git branch --show-current >> %temp%
set /p branchname=<%temp%
echo %branchname%
git status
call :node

if defined build (
	echo %build%
) else (
:input
	call :info Digite o projeto para build
	call :opt 0 - Full
	call :opt 1 - Controle
	call :opt 2 - Posto
	echo.
	set /p build=""
	call :info build selecionado: %build%
)

if %build%==0 (
	call :Build-Controle
	call :Build-Posto
	goto :fim
)
if %build%==1 (
	call :Build-Controle
	goto :fim
)
if %build%==2 (
	call :Build-Posto
	goto :fim
) else (
	goto :input
)
	
:Build-Controle
cd %projeto%\src\Front
call :build
robocopy "%projeto%\src\Front\dist" "%projeto%\src\artefatos\BBC - BBC Controle" *.html *.js *.css /s /z /r:2 /mt:10 /lev:3 /s
exit /b 0

:Build-Posto
cd %projeto%\src\Front.Posto
call :build
robocopy "%projeto%\src\Front.Posto\dist" "%projeto%\src\artefatos\BBC - Rede BBC Controle" *.html *.js *.css /s /z /r:2 /mt:10 /lev:3 /s
exit /b 0

:build
cmd /c npm -v
echo verifica bower_components e node_modules
if not exist "bower_components" rd /S /Q "node_modules"
if not exist "node_modules" (
rd /S /Q "bower_components"
echo npm install
cmd /c npm install
echo npm install -g gulp
cmd /c npm install -g gulp
)
echo ok
if exist "dist" cmd /c gulp clean
if exist "node_modules" if exist "bower_components" (cmd /c gulp build) else (goto :erro)
exit /b 0

:node
set "TARGET_VERSION=v10"

for /f "delims=" %%v in ('nvm current') do (
    set "CURRENT_VERSION=%%v"
)

for /f "tokens=1" %%a in ("!CURRENT_VERSION!") do (
    set "CLEAN_VERSION=%%a"
)

for /f "delims=." %%B in ("!CLEAN_VERSION!") do (
    set "current_major=%%B"
)

if "!current_major!" neq "%TARGET_VERSION%" (
    echo Versão atual é !CLEAN_VERSION!, trocando para %TARGET_VERSION%...
	pause
    nvm use %TARGET_VERSION%
)
call :info node version: %CURRENT_VERSION%
exit /b

:voltarNode
call :info Voltando versão node para %CURRENT_VERSION%
pause
nvm use %CURRENT_VERSION%
exit /b

:setESC
    for /F "tokens=1,2 delims=#" %%a in ('"prompt #$H#$E# & echo on & for %%b in (1) do rem"') do (
      set ESC=%%b
      exit /B 0
    )
    exit /B 0
	
:opt
	call :setESC
	echo !ESC![92m %* !!ESC![0m
	exit /B 0

:log
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![95m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:info
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo !ESC![95m	%*
	echo !ESC![94m===================================================================================================!!ESC![0m
	echo.
	exit /B 0

:warn
	:: %~n0 = nome arquivo | %~x0 = extensão arquivo
	echo.
	call :setESC
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo !ESC![93m	%*
	echo !ESC![93m===================================================================================================!!ESC![0m
	echo.
	exit /B 0
	
:erro
echo.
echo ===================================================================================================
echo.
echo erro
echo.
echo ===================================================================================================
echo.
exit /b 0

:sucesso
echo.
echo ===================================================================================================
echo.
echo sucesso
echo.
echo ===================================================================================================
echo.
exit /b 0

:fim
call %pastaOrigem%remove-config.bat
if %errorlevel%==1 (call :erro) else (call :sucesso)
if %NoStop%==False (pause)
call :voltarNode