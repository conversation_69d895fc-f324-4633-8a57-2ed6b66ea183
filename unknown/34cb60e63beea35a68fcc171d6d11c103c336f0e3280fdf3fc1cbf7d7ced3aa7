﻿using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SistemaInfo.BBC.Application.Objects.Api.Transacao;
using SistemaInfo.BBC.Application.Objects.Mobile.Viagem.Response;
using SistemaInfo.BBC.Application.Objects.Web.Transacao;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.BBC.Domain.External.Conductor.DTO.Transferencia;
using SistemaInfo.BBC.Domain.Helper;
using SistemaInfo.BBC.Domain.Models.Transacao;
using SistemaInfo.BBC.Domain.Models.Transacao.Commands;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Application.AutoMapper.TransacaoMapper
{
    public class TransacaoMappingProfile : SistemaInfoMappingProfile
    {
        public TransacaoMappingProfile()
        {
            CreateMap<TransacaoRequest, TransacaoSalvarComRetornoCommand>();
            
            CreateMap<TransacaoSalvarComRetornoCommand, Transacao>();
            
            CreateMap<TransacaoRequest, Transacao>();
            
            CreateMap<TransacaoAlterarStatusCommand, Transacao>()
                .ForMember(d => d.IdPagamentoEvento, opt => opt.MapFrom(x => x.IdPagamentoEvento == 0 ? default : x.IdPagamentoEvento));

            CreateMap<Transferencia, TransacaoRequest>()
                .ForMember(d => d.Destino, opt => opt.MapFrom(x => x.destinationAccount))
                .ForMember(d => d.Origem, opt => opt.MapFrom(x => x.originalAccount))
                .ForMember(d => d.Valor, opt => opt.MapFrom(x => x.amount))
                .ForMember(d => d.Descricao, opt => opt.MapFrom(x => x.description));

            CreateMap<Transacao, TransacaoRequest>();

            CreateMap<TransacaoAlterarStatusRequest, TransacaoAlterarStatusCommand>();
            
            CreateMap<Transacao, TransacaoSalvarComRetornoCommand>();

            CreateMap<Transacao, TransacaoPagamentoConsultarGridItem>()
                .ForMember(dest => dest.TransacaoId, opt => opt.MapFrom(x => x.Id))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(x => x.Status.ToString()))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(x => x.FormaPagamento.ToString()))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(s => StringHelper.FormatMoney(s.Valor)))
                .ForMember(dest => dest.CodigoContaOrigem, opt => opt.MapFrom(x => x.Origem))
                .ForMember(dest => dest.CodigoContaDestino, opt => opt.MapFrom(x => x.Destino))
                .ForMember(dest => dest.ChavePix, opt => opt.MapFrom(x => x.PagamentoEvento.ChavePix))
                .ForMember(dest => dest.CodigoBanco, opt => opt.MapFrom(x => x.CodigoBanco))
                .ForMember(dest => dest.Agencia, opt => opt.MapFrom(x => x.Agencia))
                .ForMember(dest => dest.Conta, opt => opt.MapFrom(x => x.Conta))
                .ForMember(dest => dest.TipoConta, opt => opt.MapFrom(x => x.TipoConta))
                .ForMember(dest => dest.DataCadastro, opt => opt.MapFrom(x => x.DataCadastro.FormatDateTimeBr()))
                .ForMember(dest => dest.DataBaixa, opt => opt.MapFrom(x => x.DataBaixa.FormatDateTimeBr()))
                .ForMember(dest => dest.DataCancelamento, opt => opt.MapFrom(x => x.DataCancelamento.FormatDateTimeBr()))
                .ForMember(dest => dest.DataAlteracao, opt => opt.MapFrom(x => x.DataAlteracao.FormatDateTimeBr()));

            CreateMap<Transacao, ConsultarTransacaoResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(x => x.Id))
                .ForMember(dest => dest.IdResponseDock, opt => opt.MapFrom(x => x.ResponseCodeDock))
                .ForMember(dest => dest.IdResponseCancelamentoDock, opt => opt.MapFrom(x => x.ResponseCodeDockCancelamento))
                .ForMember(dest => dest.JsonEnvioDock, opt => opt.MapFrom(x => x.JsonEnvioDock))
                .ForMember(dest => dest.JsonRespostaDock, opt => opt.MapFrom(x => x.JsonRespostaDock))
                .ForMember(dest => dest.JsonEnvioDockCancelamento, opt => opt.MapFrom(x => x.JsonEnvioDockCancelamento))
                .ForMember(dest => dest.JsonRespostaDockCancelamento, opt => opt.MapFrom(x => x.JsonRespostaDockCancelamento))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(x => x.Status.GetHashCode()))
                .ForMember(dest => dest.DataHoraRequisicao, opt => opt.MapFrom(x => x.DataCadastro.FormatDateTimeBr()))
                .ForMember(dest => dest.DataHoraCancelamentoResposta, opt => opt.MapFrom(x => x.DataCancelamento.FormatDateTimeBr()))
                .ForMember(dest => dest.DataHoraResposta, opt => opt.MapFrom(x => x.DataRetornoDock.FormatDateTimeBr()));
            
            
            CreateMap<Transacao, TransacaoMobileResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.DataCadastro, opt => opt.MapFrom(src => src.DataCadastro.ToString("yyyy-MM-ddTHH:mm:ss")))
                .ForMember(dest => dest.DataAlteracao, opt => opt.Ignore())
                .ForMember(dest => dest.DataCancelamento, opt => opt.Ignore())
                .ForMember(dest => dest.DocumentoOrigem, opt => opt.MapFrom(src => GetDocumentoOrigem(src))) // TODO preencher depois vai precisar
                .ForMember(dest => dest.DocumentoDestino, opt => opt.MapFrom(src => GetDocumentoDestino(src))) // TODO preencher depois vai precisar
                .ForMember(dest => dest.ContaOrigem, opt => opt.MapFrom(src => src.Origem.ToString()))
                .ForMember(dest => dest.ContaDestino, opt => opt.MapFrom(src => src.Destino.ToString()))
                .ForMember(dest => dest.Valor, opt => opt.MapFrom(src => src.Valor))
                .ForMember(dest => dest.TransactionCodeCancelamento, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDockCancelamento)))
                .ForMember(dest => dest.TransactionCodePagamento, opt => opt.MapFrom(src => GetTransactionCode(src.JsonRespostaDock)))
                .ForMember(dest => dest.FormaPagamento, opt => opt.MapFrom(src => src.FormaPagamento.ToString()))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));
            
        }
        
        private static string GetTransactionCode(string jsonRespostaDock)
        {
            if (string.IsNullOrEmpty(jsonRespostaDock))
            {
                return null;
            }
            try
            {
                JObject json = JObject.Parse(jsonRespostaDock);
                JToken transactionCodeToken = json["transactionCode"];
                if (transactionCodeToken != null)
                {
                    return transactionCodeToken.Value<string>();
                }
                JToken dataToken = json["data"];
                if (dataToken != null && dataToken.HasValues)
                {
                    return dataToken["TransactionCode"]?.Value<string>();
                }
                return null;
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"Erro ao deserializar JsonRespostaDock para TransactionCode: {ex.Message}");
                return null;
            }
        }

        private static string GetDocumentoOrigem(Transacao transacao)
        {
            //transacao do proprietário
            if (transacao.Valor == transacao.PagamentoEvento.Valor)
            {
                return transacao.PagamentoEvento.Empresa.Cnpj;
            }

            //transacao do motorista
            if (transacao.Valor == transacao.PagamentoEvento.ValorTransferenciaMotorista)
            {
                return transacao.PagamentoEvento.Viagem.PortadorProprietario.CpfCnpj;
            }
            return null;
        }
        private static string GetDocumentoDestino(Transacao transacao)
        {
            if (transacao.FormaPagamento == FormaPagamentoEvento.Pix)
            {
                if (!string.IsNullOrEmpty(transacao.PagamentoEvento.RecebedorAutorizado))
                {
                    return transacao.PagamentoEvento.RecebedorAutorizado;
                }

                return transacao.PagamentoEvento.Viagem.PortadorProprietario.CpfCnpj;
            }
            
            //transacao do proprietário
            if (transacao.Valor == transacao.PagamentoEvento.Valor)
            {
                return transacao.PagamentoEvento.Viagem.PortadorProprietario.CpfCnpj;
            }

            //transacao do motorista
            if (transacao.Valor == transacao.PagamentoEvento.ValorTransferenciaMotorista)
            {
                return transacao.PagamentoEvento.Viagem.PortadorMotorista.CpfCnpj;
            }
            return null;
        }
    }
}