<div class="row">
    <div class="col-sm-12">
        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="control-label">Login <span class="text-danger">*</span></label>
                    <div>
                        <input autocomplete="one-time-code" required type="text" ng-model="vm.authClientSecret.login"
                               maxlength="100" name="Login" class="form-control" ng-readonly="!vm.isNew()"/>
                        <small class="form-text text-muted" ng-show="!vm.isNew()">
                            O login não pode ser alterado após a criação.
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="control-label">Descrição <span class="text-danger">*</span></label>
                    <div>
                        <input autocomplete="one-time-code" required type="text" ng-model="vm.authClientSecret.descricao"
                               maxlength="200" name="Descrição" class="form-control"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="control-label">Senha <span class="text-danger" ng-show="vm.isNew()">*</span></label>
                    <div>
                        <input autocomplete="new-password" type="password" ng-model="vm.authClientSecret.senha"
                               maxlength="100" name="Senha" class="form-control" 
                               ng-required="vm.isNew()"
                               placeholder="{{vm.isNew() ? 'Digite a senha' : 'Deixe em branco para manter a senha atual'}}"/>
                    </div>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label class="control-label">Client Secret</label>
                    <div>
                        <input type="text" ng-model="vm.authClientSecret.clientSecret"
                               maxlength="300" name="ClientSecret" class="form-control" ng-readonly="!vm.isNew()"
                               placeholder="{{vm.isNew() ? 'Será gerado automaticamente se não informado' : 'Client Secret atual'}}"/>
                        <small class="form-text text-muted" ng-show="vm.isNew()">
                            Se não informado, será gerado automaticamente um client secret único.
                        </small>
                        <small class="form-text text-muted" ng-show="!vm.isNew()">
                            O client secret não pode ser alterado após a criação por questões de segurança.
                        </small>
                    </div>
                </div>
            </div>
        </div>
       
    </div>
</div>
