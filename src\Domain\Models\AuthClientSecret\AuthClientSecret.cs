using System;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;

namespace SistemaInfo.BBC.Domain.Models.AuthClientSecret
{
    public class AuthClientSecret : Entity<AuthClientSecret, int, NotImplementedEntityValidator<AuthClientSecret>>
    {
        public string Login { get; set; }
        public string Senha { get; set; }
        public string ClientSecret { get; set; }
        public string Descricao { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public int UsuarioCadastroId { get; set; }
        public int? UsuarioAlteracaoId { get; set; }
        public int Ativo { get; set; } = 1;

        #region Propriedades de Navegacao

        public virtual Usuario.Usuario Usuario { get; set; }
        public virtual Usuario.Usuario UsuarioAlteracao { get; set; }

        #endregion
    }
}
