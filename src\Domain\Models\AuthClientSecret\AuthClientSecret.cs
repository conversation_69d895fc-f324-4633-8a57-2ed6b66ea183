using System;
using System.Text;
using SistemaInfo.BBC.Domain.Enum;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Models.Validator;
using SistemaInfo.Framework.Utils;

namespace SistemaInfo.BBC.Domain.Models.AuthClientSecret
{
    public class AuthClientSecret : Entity<AuthClientSecret, int, NotImplementedEntityValidator<AuthClientSecret>>
    {
        public string Login { get; set; }
        public string Senha { get; set; }
        public string ClientSecret { get; set; }
        public string Descricao { get; set; }
        public DateTime DataCadastro { get; set; }
        public DateTime? DataAlteracao { get; set; }
        public int UsuarioCadastroId { get; set; }
        public int? UsuarioAlteracaoId { get; set; }
        public int Ativo { get; set; } = 1;

        #region Propriedades de Navegacao

        public virtual Usuario.Usuario Usuario { get; set; }
        public virtual Usuario.Usuario UsuarioAlteracao { get; set; }

        #endregion


        public bool LoginAtivo()
        {
            return Ativo == EBool.Verdadeiro.ToInt();
        }
        
        public bool LoginValido(string valor)
        {
            return Login == valor;
        }

        public bool SenhaValida(string valor)
        {
            return Senha == valor.GetHashSha1();
        }
        
        public bool ClientSecretValida(string valor)
        {
            return ClientSecret == valor;
        }
    }
}
