<!DOCTYPE html>
<html lang="">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/x-icon" href="/images/favicon.ico"/>
    <title>BBC Integração</title>
    <style>
        @import url('https://fonts.googleapis.com/css?family=Lato&display=swap');
        html, body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100%;
            width: 100%;
        }

        body {
            background: linear-gradient(135deg, #fbffdf 0%, #060d20 100%);
        }

        #container {
            border-style: none;
            min-height: 70px;
            margin: 100px auto;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        img {
            max-width: 480px;
        }

        p {
            font-family: 'Lato', sans-serif;
            font-size: 30px;
            color: #134F4D;
        }

        canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            background: transparent;
        }
    </style>
</head>

<body>
    <canvas id="animated-background"></canvas>
    <div id="container">
        <img src="images/ic_search.png" alt="">
    </div>
    <script>
        const canvas = document.getElementById('animated-background');
        const context = canvas.getContext('2d');
    
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
    
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    
        const particlesArray = [];
        const numberOfParticles = 200;
    
        class Particle {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 5 + 1; 
                this.speedY = Math.random() * 0.8 + 0.1;
                this.depth = Math.random() * canvas.height * 0.5;
                this.alpha = Math.random() * 0.8 + 0.2; 
                this.opacityChangeSpeed = Math.random() * 0.01 + 0.002; 
                this.currentOpacityDirection = 1;
            }
    
            update() {
                this.y -= this.speedY;
                
                if (this.alpha <= 0.2) {
                    this.currentOpacityDirection = 0.8;
                } else if (this.alpha >= 1) {
                    this.currentOpacityDirection = -1;
                }
                this.alpha += this.opacityChangeSpeed * this.currentOpacityDirection;
                
                if (this.y < 0) {
                    this.y = canvas.height + 10;
                    this.x = Math.random() * canvas.width;
                    this.depth = Math.random() * canvas.height * 0.5;
                    this.alpha = Math.random() * 0.8 + 0.2;
                }
            }
    
            draw() {
                const sizeFactor = 1 - (this.depth / (canvas.height * 0.5));
                const scaledSize = this.size * sizeFactor;
    
                context.fillStyle = `rgba(255, 255, 255, ${this.alpha})`;
                context.beginPath();
                context.arc(this.x, this.y, scaledSize, 0, Math.PI * 2);
                context.closePath();
                context.fill();
            }
        }
    
        function init() {
            for (let i = 0; i < numberOfParticles; i++) {
                particlesArray.push(new Particle());
            }
        }
    
        function animate() {
            context.clearRect(0, 0, canvas.width, canvas.height);
    
            for (let i = 0; i < particlesArray.length; i++) {
                particlesArray[i].update();
                particlesArray[i].draw();
            }
    
            requestAnimationFrame(animate);
        }
    
        init();
        animate();
    </script>
</body>

</html>
