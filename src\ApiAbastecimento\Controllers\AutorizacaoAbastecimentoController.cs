using System;
using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NLog;
using SistemaInfo.BBC.ApiAbastecimento.Controllers.Base;
using SistemaInfo.BBC.Application.Interface.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Api.AutorizacaoAbastecimento;
using SistemaInfo.BBC.Application.Objects.Base;
using SistemaInfo.Framework.DomainDrivenDesign.Domain.Engine;

namespace SistemaInfo.BBC.ApiAbastecimento.Controllers
{
    /// <summary>
    /// class contendo metodos utilizado na aplicação de AutorizacaoAbastecimento
    /// </summary>
    [Route("AutorizacaoAbastecimentos")]
    public class AutorizacaoAbastecimentoController : ApiControllerBase<IAutorizacaoAbastecimentoAppService>
    {
        /// <summary>
        /// Injeção de dependencias para a aplicação AutorizacaoAbastecimento
        /// </summary>
        /// <param name="engine"></param>
        /// <param name="appService"></param>
        public AutorizacaoAbastecimentoController(IAppEngine engine, IAutorizacaoAbastecimentoAppService appService) : base(engine, appService)
        {
        }
        
        /// <summary>
        /// metodo responsavel pela integração de autorizacao
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("AutorizacaoAbastecimento")]
        public JsonResult IntegrarAutorizacaoAbastecimento([FromBody]AutorizarAbastecimentoIntegrarRequest request)
        {
            try
            {
                var logger = LogManager.GetCurrentClassLogger();
                var sw = new Stopwatch();
                sw.Start();
                logger.Info("Iniciando integração de autorização de abastecimento\n" +
                            $"Data: {DateTime.Now}\n" +
                            $"Request: {JsonConvert.SerializeObject(request)}");
                var response = AppService.IntegrarAutorizacaoAbastecimento(request).Result;
                sw.Stop();
                logger.Info($"Integração de autorização de abastecimento finalizada\n" +
                            $"Data: {DateTime.Now}\n" +
                            $"Tempo total: {sw.ElapsedMilliseconds}ms\n" +
                            $"Response: {JsonConvert.SerializeObject(response)}");
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }   
        
        
        /// <summary>
        /// metodo responsavel pela aintegração de autorizacao
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Produces("application/json")]
        [HttpPost("CancelarAutorizacaoAbastecimento")]
        public JsonResult CancelarAutorizacaoAbastecimento([FromBody]CancelarAbastecimentoIntegrarRequest request)
        {            
            try
            {
                var response = AppService.CancelarAutorizacaoAbastecimento(request).Result;
                return ResponseBaseApi.BigJson(response);
            }
            catch (Exception)
            {
                return ResponseBaseApi.ResponderErro("Não foi possível realizar a operação");
            }
        }   
    }
 }